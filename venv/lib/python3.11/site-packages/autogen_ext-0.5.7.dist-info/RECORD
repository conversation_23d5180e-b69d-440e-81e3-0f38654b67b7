autogen_ext-0.5.7.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
autogen_ext-0.5.7.dist-info/METADATA,sha256=x8IR6WkPTFPM4XZm7grdzR_zNPjWtgliP0JuIwxA3pg,6971
autogen_ext-0.5.7.dist-info/RECORD,,
autogen_ext-0.5.7.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
autogen_ext-0.5.7.dist-info/licenses/LICENSE-CODE,sha256=ws_MuBL-SCEBqPBFl9_FqZkaaydIJmxHrJG2parhU4M,1141
autogen_ext/__init__.py,sha256=MPM23Fs4dZ4eEIteZunUEhCtDm58cw1D2Da9Mp2cxNA,83
autogen_ext/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/agents/azure/__init__.py,sha256=eC_gbaIUzAs_2ve0yqCtXz5p7hA7LjFvnq8X4GdeaA0,297
autogen_ext/agents/azure/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/agents/azure/__pycache__/_azure_ai_agent.cpython-311.pyc,,
autogen_ext/agents/azure/__pycache__/_types.cpython-311.pyc,,
autogen_ext/agents/azure/_azure_ai_agent.py,sha256=pB54IKXXcuPoG14lVy-pxNWZiBznlqYrFDyojwAOy5s,44899
autogen_ext/agents/azure/_types.py,sha256=hAih3iCoa0FSoKoc4T21LswE59M66U9__3yCfd9vwsU,2052
autogen_ext/agents/file_surfer/__init__.py,sha256=8xoQzuZtzCrDT110t-H6S60-OxP58EsfAT5kXSYJkos,63
autogen_ext/agents/file_surfer/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/agents/file_surfer/__pycache__/_file_surfer.cpython-311.pyc,,
autogen_ext/agents/file_surfer/__pycache__/_markdown_file_browser.cpython-311.pyc,,
autogen_ext/agents/file_surfer/__pycache__/_tool_definitions.cpython-311.pyc,,
autogen_ext/agents/file_surfer/_file_surfer.py,sha256=MiyvNhhPbDiRwiSS6Jr789SLn1xFT20CuCdKZ0N2tDQ,7642
autogen_ext/agents/file_surfer/_markdown_file_browser.py,sha256=afv8XaVVEPEPKzc6UezvhTrbRxxLdFfWDk8scrYdZtE,12726
autogen_ext/agents/file_surfer/_tool_definitions.py,sha256=9TBJXG6ftMAMOfnut0JNpHCNOO8ApjDS8SlgtetB1Rw,1518
autogen_ext/agents/magentic_one/__init__.py,sha256=5nnvDbfjXQ4g-D0s3yJ07TZU7lZ40KG46gWLNogE8WQ,348
autogen_ext/agents/magentic_one/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/agents/magentic_one/__pycache__/_magentic_one_coder_agent.cpython-311.pyc,,
autogen_ext/agents/magentic_one/_magentic_one_coder_agent.py,sha256=JKOc40gfSythZdt69uPMkFKI6SOT7jFn2NaiCYuV4N8,2997
autogen_ext/agents/openai/__init__.py,sha256=tl9b2Bdofm1gtJTsH6yRxx-2Cqg46qqFqN0sxiw2MS0,331
autogen_ext/agents/openai/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/agents/openai/__pycache__/_openai_assistant_agent.cpython-311.pyc,,
autogen_ext/agents/openai/_openai_assistant_agent.py,sha256=ldJ1-eZEEh-NoChu3JP4FfJIsEQFNkASgIUUO3x7u-0,30244
autogen_ext/agents/video_surfer/__init__.py,sha256=9JvZCHF_4fVKwh3e1_lCFbtG_3iE4VaY1N97zIz3ulA,66
autogen_ext/agents/video_surfer/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/agents/video_surfer/__pycache__/_video_surfer.cpython-311.pyc,,
autogen_ext/agents/video_surfer/__pycache__/tools.cpython-311.pyc,,
autogen_ext/agents/video_surfer/_video_surfer.py,sha256=RXgrILMh_Gkr462ruFVdRIRRkjnpiJVHjDhe__aFpy0,6952
autogen_ext/agents/video_surfer/tools.py,sha256=FIvIa6-us4SQAQq_U9ZWJ-Ex0O5OoNIJ92pKHMTAGfM,5527
autogen_ext/agents/web_surfer/__init__.py,sha256=YzKK8R6dV_TqEtbmqVGKoQy3zMn6DxBtXhuhiKmLtpo,171
autogen_ext/agents/web_surfer/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/agents/web_surfer/__pycache__/_events.cpython-311.pyc,,
autogen_ext/agents/web_surfer/__pycache__/_multimodal_web_surfer.cpython-311.pyc,,
autogen_ext/agents/web_surfer/__pycache__/_prompts.cpython-311.pyc,,
autogen_ext/agents/web_surfer/__pycache__/_set_of_mark.cpython-311.pyc,,
autogen_ext/agents/web_surfer/__pycache__/_tool_definitions.cpython-311.pyc,,
autogen_ext/agents/web_surfer/__pycache__/_types.cpython-311.pyc,,
autogen_ext/agents/web_surfer/__pycache__/playwright_controller.cpython-311.pyc,,
autogen_ext/agents/web_surfer/_events.py,sha256=Sb6IAWK4_Pe-Gt_wDbU45z84ElvQKRT0LnaspA1bvnA,218
autogen_ext/agents/web_surfer/_multimodal_web_surfer.py,sha256=Cg29bs1fEcK2IUJuYtzQ5kvroPJ7M-T5L0_DOhaQRkU,42375
autogen_ext/agents/web_surfer/_prompts.py,sha256=6g-GHAkFK_EC1B0r3lOQkAmm_VAbWfYo8g-zNM64W6k,2628
autogen_ext/agents/web_surfer/_set_of_mark.py,sha256=e1-O3RWKs26JDG7OHzIVH64IkTgdkogRjZEiSFw8yYk,3590
autogen_ext/agents/web_surfer/_tool_definitions.py,sha256=v3UoTc-rVgLBsWq7gtxIET_AdlbtlUcu1zwGD50pKqc,10190
autogen_ext/agents/web_surfer/_types.py,sha256=74DFpJ5PDGuiZuUdCYlxHA2E28mDETuSpZdGe24dwKI,3130
autogen_ext/agents/web_surfer/page_script.js,sha256=UacdBbs2_Kkzz2BGdKPHJxSqPG8jlz171UX8OBRt2lg,13495
autogen_ext/agents/web_surfer/playwright_controller.py,sha256=zHyAd1eRo3fQmSaRmGWVJnLQhY7QPMrmXxZP9s6SR8I,21327
autogen_ext/auth/azure/__init__.py,sha256=_HEwj_gx_3CR4qn9fbP0h1Ajf1351Pa2jtNZx4TInsw,2066
autogen_ext/auth/azure/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/cache_store/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
autogen_ext/cache_store/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/cache_store/__pycache__/diskcache.cpython-311.pyc,,
autogen_ext/cache_store/__pycache__/redis.cpython-311.pyc,,
autogen_ext/cache_store/diskcache.py,sha256=yyfSn1yzJF1P3DORGdxfln8mVn12F3fDWM3bI0Q6XSk,1751
autogen_ext/cache_store/redis.py,sha256=Y2aKqx4-xICLIR13nT3MDuwAIeBppRvrHU3PLHAX5Zw,2867
autogen_ext/code_executors/__pycache__/_common.cpython-311.pyc,,
autogen_ext/code_executors/_common.py,sha256=mKkXmZBrUkwKu6mzaHZi3KeM6cI2-FrkRrKobhX79bI,6425
autogen_ext/code_executors/azure/__init__.py,sha256=3KOPqty3DQusWmUff8wYyV7Fj-1wJzkBjlrAn0Q654s,153
autogen_ext/code_executors/azure/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/code_executors/azure/__pycache__/_azure_container_code_executor.cpython-311.pyc,,
autogen_ext/code_executors/azure/_azure_container_code_executor.py,sha256=MXqOeDBlojE4SxPGQIZNjOFOwqHMbm3ekvL0Qjik3EE,22296
autogen_ext/code_executors/docker/__init__.py,sha256=XELrAxc1X3OQk6o2Pwo1VLPvlnYrlVlOB8CWS4zRK7Q,110
autogen_ext/code_executors/docker/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/code_executors/docker/__pycache__/_docker_code_executor.cpython-311.pyc,,
autogen_ext/code_executors/docker/_docker_code_executor.py,sha256=0Zv3yw29-B-s794qj983WUeFnbCnXnbrDwqFtDivqTk,26451
autogen_ext/code_executors/docker_jupyter/__init__.py,sha256=xt-WQlJ11eqpQmGHUc3rtU0YWlRiEpAKD94QKFKApC8,319
autogen_ext/code_executors/docker_jupyter/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/code_executors/docker_jupyter/__pycache__/_docker_jupyter.cpython-311.pyc,,
autogen_ext/code_executors/docker_jupyter/__pycache__/_jupyter_server.cpython-311.pyc,,
autogen_ext/code_executors/docker_jupyter/_docker_jupyter.py,sha256=M8wBEeesTFQz4As9E15XOU5oRyDLaM8Ztwu5CUix1X4,12236
autogen_ext/code_executors/docker_jupyter/_jupyter_server.py,sha256=hLBYIW7Vpme3o_C4zuex7cpgt3fpKr7ASvgQba72v0Y,16272
autogen_ext/code_executors/jupyter/__init__.py,sha256=0xmprSsJMidnZS2Kc7iU01OAnrWUODvcn541s6nX15s,142
autogen_ext/code_executors/jupyter/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/code_executors/jupyter/__pycache__/_jupyter_code_executor.cpython-311.pyc,,
autogen_ext/code_executors/jupyter/_jupyter_code_executor.py,sha256=Av5luehYm4F7b87k1J328-Fb5uWsP4dx270lMOq7esM,12040
autogen_ext/code_executors/local/__init__.py,sha256=nqAUcYagFnAfH-EqFQS0E8JTWRnMKF7fFR-JMNkZFHk,19510
autogen_ext/code_executors/local/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/experimental/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
autogen_ext/experimental/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/experimental/task_centric_memory/README.md,sha256=7WOlaz0kjhqnpmNYw9Rb1ouu6DgF7DwImUah2RFP7ew,10028
autogen_ext/experimental/task_centric_memory/__init__.py,sha256=iy7DwVWlIW2YWNVTqjkyIdMlkg8Ni0hMJfwqFlZ2B5k,193
autogen_ext/experimental/task_centric_memory/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/experimental/task_centric_memory/__pycache__/_memory_bank.cpython-311.pyc,,
autogen_ext/experimental/task_centric_memory/__pycache__/_prompter.cpython-311.pyc,,
autogen_ext/experimental/task_centric_memory/__pycache__/_string_similarity_map.cpython-311.pyc,,
autogen_ext/experimental/task_centric_memory/__pycache__/memory_controller.cpython-311.pyc,,
autogen_ext/experimental/task_centric_memory/_memory_bank.py,sha256=YcestiweMxwQG0w0-dFWjoqsMvmbFuHlEBA0SuSWg9k,8347
autogen_ext/experimental/task_centric_memory/_prompter.py,sha256=FjgSv7_zq0XXGUs0Qd76Rtc5-SWZ4rlypMl2I-dq260,12873
autogen_ext/experimental/task_centric_memory/_string_similarity_map.py,sha256=05D7Sb1qxLwDEKXoO9hb-VlDb8c1NgtjZDK8EfSjlV0,5722
autogen_ext/experimental/task_centric_memory/memory_controller.py,sha256=d7w5zHv6ukIxbJzC1onsqO3w7yVfQ5kYOAyiEr67WJY,20796
autogen_ext/experimental/task_centric_memory/utils/__init__.py,sha256=d26dp1Taus24fsJNbxJzhP_pGTt0G-tuG43-y4LhmCQ,416
autogen_ext/experimental/task_centric_memory/utils/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/experimental/task_centric_memory/utils/__pycache__/_functions.cpython-311.pyc,,
autogen_ext/experimental/task_centric_memory/utils/__pycache__/apprentice.cpython-311.pyc,,
autogen_ext/experimental/task_centric_memory/utils/__pycache__/chat_completion_client_recorder.cpython-311.pyc,,
autogen_ext/experimental/task_centric_memory/utils/__pycache__/grader.cpython-311.pyc,,
autogen_ext/experimental/task_centric_memory/utils/__pycache__/page_logger.cpython-311.pyc,,
autogen_ext/experimental/task_centric_memory/utils/__pycache__/teachability.cpython-311.pyc,,
autogen_ext/experimental/task_centric_memory/utils/_functions.py,sha256=W1F45raxwgDRL2zPHt33NYBrWJ7eSTApBZSylIRsBz4,3328
autogen_ext/experimental/task_centric_memory/utils/apprentice.py,sha256=nkO_ZHYCQpHxzMdrut9jHdk9ZRSvV5rEELjGyReIRLo,10240
autogen_ext/experimental/task_centric_memory/utils/chat_completion_client_recorder.py,sha256=eV9Q9Xf3C-DWkx07kBsH2NxXkPF69LrKgDdTKHzthuA,9674
autogen_ext/experimental/task_centric_memory/utils/grader.py,sha256=EgG-ltDfATOYFTGw5pG-ZnQLf6XfgWPkZ5thC6xIBVE,7421
autogen_ext/experimental/task_centric_memory/utils/page_logger.py,sha256=19U87gM2gqZF3kmyeJJNIh9IHhrgzZR9QmEN0yLJ1HI,19446
autogen_ext/experimental/task_centric_memory/utils/teachability.py,sha256=TZRxfkUX89o12cQ7zAX2WsB8zuAFLo4D4FD9hOm-sz0,5269
autogen_ext/memory/__init__.py,sha256=AbpHGcgLb-kRsJGnwFEktk7uzpZOCcBY74-YBdrKVGs,1
autogen_ext/memory/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/memory/__pycache__/chromadb.cpython-311.pyc,,
autogen_ext/memory/canvas/__init__.py,sha256=gzI_7gn2Gv826mxujuAAdHTxl--p3hcMS-wfvlQYTuk,133
autogen_ext/memory/canvas/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/memory/canvas/__pycache__/_canvas.cpython-311.pyc,,
autogen_ext/memory/canvas/__pycache__/_canvas_writer.cpython-311.pyc,,
autogen_ext/memory/canvas/__pycache__/_text_canvas.cpython-311.pyc,,
autogen_ext/memory/canvas/__pycache__/_text_canvas_memory.cpython-311.pyc,,
autogen_ext/memory/canvas/_canvas.py,sha256=IA9xMWfMjtfyFzgfrNseQawKzlGx_GKlfYrCgDlRoyY,1476
autogen_ext/memory/canvas/_canvas_writer.py,sha256=RYJ0VtJpdqjg7XlRVSib-uaWjJghFC8nYo0E6S1-iJQ,1896
autogen_ext/memory/canvas/_text_canvas.py,sha256=GlrSyFgfwShiafateg3Xl-yFbfF4QrVsv2s5nZPz5Dw,8686
autogen_ext/memory/canvas/_text_canvas_memory.py,sha256=yq90OWGDkKp2zx6FEnGQtlqFfAG9nCYswSFYDGOAK0k,9362
autogen_ext/memory/chromadb.py,sha256=WlU3GdisJRt6uAzBcxhRWmMHcsXyNFivaQE70mXbFWE,17943
autogen_ext/models/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
autogen_ext/models/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/models/_utils/__pycache__/normalize_stop_reason.cpython-311.pyc,,
autogen_ext/models/_utils/__pycache__/parse_r1_content.cpython-311.pyc,,
autogen_ext/models/_utils/normalize_stop_reason.py,sha256=YZ0KatGpt_lpP4_fS4G9L2jm8VgNHCqmBK4C_jbQ23Y,591
autogen_ext/models/_utils/parse_r1_content.py,sha256=eXgTHhqHxVAQzxe1rrgcgCBbn1LuuGaKbx5EUYZSzcQ,1106
autogen_ext/models/anthropic/__init__.py,sha256=M-6X4GfPPWjmOflWrNzpxb9E7tt2g-6yRyQExb8eMKg,766
autogen_ext/models/anthropic/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/models/anthropic/__pycache__/_anthropic_client.cpython-311.pyc,,
autogen_ext/models/anthropic/__pycache__/_model_info.cpython-311.pyc,,
autogen_ext/models/anthropic/_anthropic_client.py,sha256=R6fq8QmeluMLTuqe6wZZF3ErYPaB_SJAC2clru09T6M,47204
autogen_ext/models/anthropic/_model_info.py,sha256=1v9TYzUxJx73u9V-cUipF2lJe6dJh0ggSC0EAFobL4M,4158
autogen_ext/models/anthropic/config/__init__.py,sha256=XunbZbOMbYWRzR9N-m86bjNOUaWLP1tOaIhCl8mpSrA,3552
autogen_ext/models/anthropic/config/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/models/azure/__init__.py,sha256=A9-8k5ZlNpuwMbI4XpJFNfpa1wBV_GnYkRKYuZmuMsE,192
autogen_ext/models/azure/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/models/azure/__pycache__/_azure_ai_client.cpython-311.pyc,,
autogen_ext/models/azure/_azure_ai_client.py,sha256=d1BgHzIGOi6JE7nAa35PYkmMTmZTWnamfWbHnUKB8tU,23309
autogen_ext/models/azure/config/__init__.py,sha256=72DnD0qVT59g3hAdHWBwr9kGV9dTZJWJH8Zs_e9RnKw,1512
autogen_ext/models/azure/config/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/models/cache/__init__.py,sha256=shscSCzZKNa6ixb0tzc6UQP1s_XcfXBaadj9K4Z6axU,150
autogen_ext/models/cache/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/models/cache/__pycache__/_chat_completion_cache.cpython-311.pyc,,
autogen_ext/models/cache/_chat_completion_cache.py,sha256=iG919cGhc9QgnM510ghLrh-hgqU42aJwOpEgtsvDGQs,9522
autogen_ext/models/llama_cpp/__init__.py,sha256=MnOBAbJc92zxH0hN_oil0fBVP3qWU8yqrBkB8hHChcc,323
autogen_ext/models/llama_cpp/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/models/llama_cpp/__pycache__/_llama_cpp_completion_client.cpython-311.pyc,,
autogen_ext/models/llama_cpp/_llama_cpp_completion_client.py,sha256=GKECn9gD3eQHxH60uBhwjX1IgFvGhIPYGGGhCU44q9M,17421
autogen_ext/models/ollama/__init__.py,sha256=BEQ5xBEYOBzEAg8Xr_bs-1D160hLGZai8UAs2iwYvO4,288
autogen_ext/models/ollama/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/models/ollama/__pycache__/_model_info.cpython-311.pyc,,
autogen_ext/models/ollama/__pycache__/_ollama_client.cpython-311.pyc,,
autogen_ext/models/ollama/_model_info.py,sha256=7NftPpa0pYN0hvfr9J6HShSkawYtHhNIx7zy_sEUQkk,10570
autogen_ext/models/ollama/_ollama_client.py,sha256=8PuMGTEqmKILVshVwIugRsNOvCKJZGXR-pqNL_p7wjA,38876
autogen_ext/models/ollama/config/__init__.py,sha256=j0eLKmr8McP9POu4LZnMf_dFezs5bDwyn131UNPEbxs,1563
autogen_ext/models/ollama/config/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/models/openai/__init__.py,sha256=IJlZ5fdSou_euNRt-fuVzyLnQjFA9z_3Uo6Not4Fs7Y,748
autogen_ext/models/openai/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/models/openai/__pycache__/_message_transform.cpython-311.pyc,,
autogen_ext/models/openai/__pycache__/_model_info.cpython-311.pyc,,
autogen_ext/models/openai/__pycache__/_openai_client.cpython-311.pyc,,
autogen_ext/models/openai/__pycache__/_utils.cpython-311.pyc,,
autogen_ext/models/openai/_message_transform.py,sha256=yiMF3KBov496jGRN6_H7fu7OFm1pBW-eti6FjeP9UQ0,17770
autogen_ext/models/openai/_model_info.py,sha256=OZXs8a-KIUPP-M2Dxd2aKYVc7fxqdSp50cViCaOzC44,13932
autogen_ext/models/openai/_openai_client.py,sha256=x-eYtowceEV5loy9RhhRDWYNkaJSiKWweEaNF9SsOaA,70927
autogen_ext/models/openai/_transformation/__init__.py,sha256=6KvoCx9zzHJiXRJb49mYEfTsIUBR65OjmBxb8qX7hSM,582
autogen_ext/models/openai/_transformation/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/models/openai/_transformation/__pycache__/registry.cpython-311.pyc,,
autogen_ext/models/openai/_transformation/__pycache__/types.cpython-311.pyc,,
autogen_ext/models/openai/_transformation/registry.py,sha256=ITgcq5CeYqyOBqtOWjQUq5AzCd9ZJ0DP0qTyt4UjnJQ,4684
autogen_ext/models/openai/_transformation/types.py,sha256=K9SPv66XNlwP7eV6C-Xn7SUjSnOYfNGq9geN063PPOE,838
autogen_ext/models/openai/_utils.py,sha256=2d5ER-ds-VMDKLgyw806cW0PJGPPGpBUCZ1Oer-34nM,516
autogen_ext/models/openai/config/__init__.py,sha256=exKfqyPooOfFU-JuJZXn8tEPVnS333p--pmvUNi4GEI,4358
autogen_ext/models/openai/config/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/models/replay/__init__.py,sha256=oXNJ5nxBhg7ryxWcMejzvIjdQOKM5FPT4pOJFtDcyqY,120
autogen_ext/models/replay/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/models/replay/__pycache__/_replay_chat_completion_client.cpython-311.pyc,,
autogen_ext/models/replay/_replay_chat_completion_client.py,sha256=eytPdf4ewewxNvE9sthc1aOiqqFs4p6F0o0gWZTINI4,12571
autogen_ext/models/semantic_kernel/__init__.py,sha256=Oh93RAuk01GGW9TLB6elUKOv1qXdh-p_OBYW6z2tk1c,104
autogen_ext/models/semantic_kernel/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/models/semantic_kernel/__pycache__/_sk_chat_completion_adapter.cpython-311.pyc,,
autogen_ext/models/semantic_kernel/_sk_chat_completion_adapter.py,sha256=CSdwJvmq2U__lbdiCm2HBcavFfuZ70BbaO4KUYiQVuk,32479
autogen_ext/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
autogen_ext/runtimes/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
autogen_ext/runtimes/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/runtimes/grpc/__init__.py,sha256=4xixtX_bAZVqrGDJigjKg4k1fKWp88HCbP-F_h4t1So,515
autogen_ext/runtimes/grpc/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/runtimes/grpc/__pycache__/_constants.cpython-311.pyc,,
autogen_ext/runtimes/grpc/__pycache__/_type_helpers.cpython-311.pyc,,
autogen_ext/runtimes/grpc/__pycache__/_utils.cpython-311.pyc,,
autogen_ext/runtimes/grpc/__pycache__/_worker_runtime.cpython-311.pyc,,
autogen_ext/runtimes/grpc/__pycache__/_worker_runtime_host.cpython-311.pyc,,
autogen_ext/runtimes/grpc/__pycache__/_worker_runtime_host_servicer.cpython-311.pyc,,
autogen_ext/runtimes/grpc/_constants.py,sha256=mzeA-BARAPVz4oFkr-dlUe67kUcmqLnnZrpQ-j1RIbk,516
autogen_ext/runtimes/grpc/_type_helpers.py,sha256=EI78UT7LcYNo-omz85G515L-5Hmp1lIQ6l3cPWoodgM,176
autogen_ext/runtimes/grpc/_utils.py,sha256=eGncv8DqFi4nBVkfgnEVl9VOfbbJMa3dSKT1MSzPmbs,2068
autogen_ext/runtimes/grpc/_worker_runtime.py,sha256=jbli700bfqMYHDW1PdPbVWlX-6gHLVk4L6JH5gxTpzA,35952
autogen_ext/runtimes/grpc/_worker_runtime_host.py,sha256=1w8e7DTqmyP2duMJ_Glg5wu_Dpoji2qz5P4kCbf65VM,2650
autogen_ext/runtimes/grpc/_worker_runtime_host_servicer.py,sha256=_57n60WWCYRK1g5KVorSdbJVqumeuQP01XQs0CDcOVg,16995
autogen_ext/runtimes/grpc/protos/__init__.py,sha256=dbotk27W7m3d1tz09aijZnHk_IKdXar4xi0l0WpLwts,124
autogen_ext/runtimes/grpc/protos/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/runtimes/grpc/protos/__pycache__/agent_worker_pb2.cpython-311.pyc,,
autogen_ext/runtimes/grpc/protos/__pycache__/agent_worker_pb2_grpc.cpython-311.pyc,,
autogen_ext/runtimes/grpc/protos/__pycache__/cloudevent_pb2.cpython-311.pyc,,
autogen_ext/runtimes/grpc/protos/__pycache__/cloudevent_pb2_grpc.cpython-311.pyc,,
autogen_ext/runtimes/grpc/protos/agent_worker_pb2.py,sha256=KlkaTUCqwrueudTayhW1dOqXQ3nnKNuhqf8fgD4bmXk,8007
autogen_ext/runtimes/grpc/protos/agent_worker_pb2.pyi,sha256=VH4iWXtSVIzaYprDdUhTexnQVeT4C-X7BNxqNZvrwrY,16558
autogen_ext/runtimes/grpc/protos/agent_worker_pb2_grpc.py,sha256=Vbs_mDjdOKyIMMTBnLP-3bPVY5tHLjeIN9UtpKU4jPg,12396
autogen_ext/runtimes/grpc/protos/agent_worker_pb2_grpc.pyi,sha256=JM6tjpWoSqlSotGkBf-3yj1jCec-vwM-bKLX0lZ_SXs,4438
autogen_ext/runtimes/grpc/protos/cloudevent_pb2.py,sha256=_Y6fpY1XShwuxNxDVvLDmG5UOptA0jk6uQ8ERsQAk80,3058
autogen_ext/runtimes/grpc/protos/cloudevent_pb2.pyi,sha256=0XotcQ080-vyLTEm8je8Cp0_eF3jyGxsf1FFmShrn_k,5445
autogen_ext/runtimes/grpc/protos/cloudevent_pb2_grpc.py,sha256=I_mUGo88SMzh1L-lLGPgurKQVK1EnpMzCzKHhT8sLwY,891
autogen_ext/runtimes/grpc/protos/cloudevent_pb2_grpc.pyi,sha256=I3qN0aHuzx9SFDj_rJTDq50Lyr4RD8dehVXe82JoWEQ,642
autogen_ext/teams/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
autogen_ext/teams/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/teams/__pycache__/magentic_one.cpython-311.pyc,,
autogen_ext/teams/magentic_one.py,sha256=ynX55gvA2oG5dwzYYjHIACUhX3yGG-RnimTgpNxXBFU,9572
autogen_ext/tools/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
autogen_ext/tools/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/tools/azure/__init__.py,sha256=J2g0PsE7Ex8CY7Yux38wpv3qBnraORgQxwu7JzvbeqU,385
autogen_ext/tools/azure/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/tools/azure/__pycache__/_ai_search.cpython-311.pyc,,
autogen_ext/tools/azure/__pycache__/_config.cpython-311.pyc,,
autogen_ext/tools/azure/_ai_search.py,sha256=Hy2JJ6WjySW6uoemxOCqYCgeUxvlnoilrPjMePoDfao,50735
autogen_ext/tools/azure/_config.py,sha256=n8s7Fl7N9k8CWBsLilFZXUBD4OIbSP4KxawRWmp6_1I,8270
autogen_ext/tools/code_execution/__init__.py,sha256=y6GAqJjTiIDRjonfhCn6aNwxRLJPYKOXVAHjrOaQoMY,178
autogen_ext/tools/code_execution/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/tools/code_execution/__pycache__/_code_execution.cpython-311.pyc,,
autogen_ext/tools/code_execution/_code_execution.py,sha256=myj1KAfEPUsEvmmov9TVhAstvhejJlOmSVUWK01BQ1E,3606
autogen_ext/tools/graphrag/__init__.py,sha256=vusbuxMieXPFU4VvYwbUauBpAtHk-iMpvbwzWmLOaQI,653
autogen_ext/tools/graphrag/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/tools/graphrag/__pycache__/_config.cpython-311.pyc,,
autogen_ext/tools/graphrag/__pycache__/_global_search.cpython-311.pyc,,
autogen_ext/tools/graphrag/__pycache__/_local_search.cpython-311.pyc,,
autogen_ext/tools/graphrag/_config.py,sha256=lud-s6I-UzTCIgCMFOAt03flh2FdZSPJDP5TL8Ug1nc,1699
autogen_ext/tools/graphrag/_global_search.py,sha256=aLkhEpFk-0W1M9feM5ZT3v47RSEtbKBMdvXiMUlocOk,8653
autogen_ext/tools/graphrag/_local_search.py,sha256=pZJ8Nri10DJSXpQIiBaEm1o-4kAUwiVRvt3X2Dg4Brc,9349
autogen_ext/tools/http/__init__.py,sha256=T4bemr9syEvpasNK6slKaQUptCiL-9-9LcPEG0BlIbw,57
autogen_ext/tools/http/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/tools/http/__pycache__/_http_tool.cpython-311.pyc,,
autogen_ext/tools/http/_http_tool.py,sha256=V3tlhYMYiR1KfSVKqjdfm6Y5rN7gwTgWUg2AN0vOF4s,8498
autogen_ext/tools/langchain/__init__.py,sha256=4seSB4QI9hpPQadFVOlWvuglhoH4jo9jxCGciIVDW0c,89
autogen_ext/tools/langchain/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/tools/langchain/__pycache__/_langchain_adapter.cpython-311.pyc,,
autogen_ext/tools/langchain/_langchain_adapter.py,sha256=nIs3v0boVnj_BxBdZdb6XafoFp0sbIReqCcn24qb46w,8400
autogen_ext/tools/mcp/__init__.py,sha256=_9jhZGdyZwjMpWjLApVKf4cgGhHQYtopxTtR5YoYNtQ,547
autogen_ext/tools/mcp/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/tools/mcp/__pycache__/_actor.cpython-311.pyc,,
autogen_ext/tools/mcp/__pycache__/_base.cpython-311.pyc,,
autogen_ext/tools/mcp/__pycache__/_config.cpython-311.pyc,,
autogen_ext/tools/mcp/__pycache__/_factory.cpython-311.pyc,,
autogen_ext/tools/mcp/__pycache__/_session.cpython-311.pyc,,
autogen_ext/tools/mcp/__pycache__/_sse.cpython-311.pyc,,
autogen_ext/tools/mcp/__pycache__/_stdio.cpython-311.pyc,,
autogen_ext/tools/mcp/__pycache__/_workbench.cpython-311.pyc,,
autogen_ext/tools/mcp/_actor.py,sha256=NITmbP2KgDhP2phx7ZoJBdJzZ7IrWWmYDtI7JgoefwM,5573
autogen_ext/tools/mcp/_base.py,sha256=QsLs3Y7BdXPUPmFBahGLhbEav0LRvWYcVf40Ki_pjZ0,7085
autogen_ext/tools/mcp/_config.py,sha256=g59hCY7pe3HzK8sc2O9WdcKnYy4MJ5zj3n1FHmM9Kd0,732
autogen_ext/tools/mcp/_factory.py,sha256=9I6THgWCBYhhHmsSf0rLeSXP3vq8hqJg6JaltWP99a0,8302
autogen_ext/tools/mcp/_session.py,sha256=tHNeW1FfiRHVq2wGBj_1_OlTtsS-lDbynSkq28LJJrw,1141
autogen_ext/tools/mcp/_sse.py,sha256=CDszMRZIYhwbo8NVNGa_IQZ55FbglqUaFysYxww1ab8,4351
autogen_ext/tools/mcp/_stdio.py,sha256=kYgmyZQ0j6PpQs_Q17A6PSlv4j6LarEHye9seQmuy3w,2605
autogen_ext/tools/mcp/_workbench.py,sha256=il_gci-vaYURkOJ5-PlVpNrqCDHT8A2nC3xyciKYOgA,12136
autogen_ext/tools/semantic_kernel/__init__.py,sha256=L296eV_avmWHi07V4cjU3MBn7MbDOfcTlyHdabGrjdY,108
autogen_ext/tools/semantic_kernel/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/tools/semantic_kernel/__pycache__/_kernel_function_from_tool.cpython-311.pyc,,
autogen_ext/tools/semantic_kernel/_kernel_function_from_tool.py,sha256=AykANf7s9fAeC43lcZ_k1T8j59DAhYrxP1OOi07TATc,2736
autogen_ext/ui/__init__.py,sha256=f5Modvn8SLyTlS5_BJqnXgnNkwrvZCkHwPGw-zQ_HWc,154
autogen_ext/ui/__pycache__/__init__.cpython-311.pyc,,
autogen_ext/ui/__pycache__/_rich_console.cpython-311.pyc,,
autogen_ext/ui/_rich_console.py,sha256=d9z5uS7_NRG_hRAafuJ-s2l1QvgrYOQEkZjFOTj_RKc,8459
