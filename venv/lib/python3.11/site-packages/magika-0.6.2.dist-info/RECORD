../../../bin/magika,sha256=IuWuCZ6dUKKna0IZuGk-LorrkFOR1PMUsYg6Uyry7Lc,23687136
magika-0.6.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
magika-0.6.2.dist-info/METADATA,sha256=zRMrZFfLVUYWxhvOAaDRGrPvgT0M4UpgrS6TTFgwYZ0,15830
magika-0.6.2.dist-info/RECORD,,
magika-0.6.2.dist-info/WHEEL,sha256=BIHewG3xDASNrCQSw936TG4pT5uL1AnxgJ7f_yNgUy0,101
magika/__init__.py,sha256=p7s3-iWlqlaXWy4dcr_tnBszJQbdM8kZxjt2zeM3_rI,924
magika/__pycache__/__init__.cpython-311.pyc,,
magika/__pycache__/colors.cpython-311.pyc,,
magika/__pycache__/logger.cpython-311.pyc,,
magika/__pycache__/magika.cpython-311.pyc,,
magika/cli/__pycache__/magika_client.cpython-311.pyc,,
magika/cli/__pycache__/magika_rust_client_not_found_warning.cpython-311.pyc,,
magika/cli/magika_client.py,sha256=Z7SvQdPbo77rI6nAnejY-gG53hh2yyWcQMfaTRnzZAI,11737
magika/cli/magika_rust_client_not_found_warning.py,sha256=6vPRiZ05Zs7B9HnrpRmaNAw8JRAoNMYYlm1BwA25uKU,1169
magika/colors.py,sha256=Cak62MhIIDX-S1eTiu1VxncKEOZ9EEOwWkkdEFxQO8A,1037
magika/config/content_types_kb.min.json,sha256=dSCK26abwFVkA7YrMu88z4tc5JRBF4CEWFLlLmWDoQ0,44768
magika/logger.py,sha256=ZN67Ssj52NVUPDkTqeOnvdrXuUukPadJ-3vfU3CMKfI,2908
magika/magika.py,sha256=oHyozSCOx8a43bwDRuLDtjs1GiyOpylZsmlwqg2_FhE,31662
magika/models/standard_v3_3/README.md,sha256=D76Gfe3N_C37P-Mbtmckr4p_0ISkA0CJWPt2YAWPI9s,16448
magika/models/standard_v3_3/config.min.json,sha256=riTHQiBTWPb_bf1frLZ0P7aXQ9u6g3PnPaWP8MvWlds,2141
magika/models/standard_v3_3/metadata.json,sha256=H4liT8j_gyMIzd-GkhSc19EHLnq6L000jiQA46r7LE0,19
magika/models/standard_v3_3/model.onnx,sha256=_i0utJxfiKngpsBI4V1v_fhiNVGcKvxTUETeQzFp7Iw,3163737
magika/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
magika/types/__init__.py,sha256=T34NFWx1WGvnuy-glHh_rgUGd_L9ZBwzuzcHtnWTIc0,1541
magika/types/__pycache__/__init__.cpython-311.pyc,,
magika/types/__pycache__/content_type_info.cpython-311.pyc,,
magika/types/__pycache__/content_type_label.cpython-311.pyc,,
magika/types/__pycache__/magika_error.cpython-311.pyc,,
magika/types/__pycache__/magika_prediction.cpython-311.pyc,,
magika/types/__pycache__/magika_result.cpython-311.pyc,,
magika/types/__pycache__/model.cpython-311.pyc,,
magika/types/__pycache__/overwrite_reason.cpython-311.pyc,,
magika/types/__pycache__/prediction_mode.cpython-311.pyc,,
magika/types/__pycache__/seekable.cpython-311.pyc,,
magika/types/__pycache__/status.cpython-311.pyc,,
magika/types/__pycache__/strenum.cpython-311.pyc,,
magika/types/content_type_info.py,sha256=OvRZNcUCJVTYCPiE-AT-frK3PJOLhoOqVeUGejLk9F8,1345
magika/types/content_type_label.py,sha256=QWHfYU99K4pPmDS_eLTQsCmKCw72keEzticuIWgb17c,7720
magika/types/magika_error.py,sha256=FQUAhlvnMea26Fzj-MweM7aWBnM1BIzWZkZoJyYTd5Q,39
magika/types/magika_prediction.py,sha256=Y3EkOdgygOe2bpIPYxWGLZrlHk-89Y2VwFeOLGxD4eA,920
magika/types/magika_result.py,sha256=E7SjF6C7cN3RvoIe-PhSr6peMUIaw8rTKbBwa411u7c,3260
magika/types/model.py,sha256=MI7ld-WH2b9nrrow5rDuaTjuUD6aPMrtScS-jefxZWc,1465
magika/types/overwrite_reason.py,sha256=lthZczci9w7OFEYCv0z7gfHh0huMeu2m4j4lUjfMHHk,770
magika/types/prediction_mode.py,sha256=17dmK1B_8t5uPrt38twVRXCbS9XxcBrU1Ki6UauoAWo,954
magika/types/seekable.py,sha256=LEMCMrYDc1WLrxV91DSq5eTp4JQkxSlm_yjx1EXXewU,1068
magika/types/status.py,sha256=b9CrA7ooEUI0VFQ2VblmqMjZEicyv88icxlqd_O9IkQ,992
magika/types/strenum.py,sha256=cuXOcFI7gqZuTfRGhLa-VBhz2eVcOUX_zhDgKqoKW-U,1296
