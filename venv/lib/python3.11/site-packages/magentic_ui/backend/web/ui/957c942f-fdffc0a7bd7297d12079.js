"use strict";(self.webpackChunkMagentic_UI=self.webpackChunkMagentic_UI||[]).push([[784],{61815:function(e,t,r){r.d(t,{JY:function(){return vn},gL:function(){return so},sx:function(){return Gn}});var n=r(96540),o=r(40961),i=r(55856),a=r(81224),s=r(32737),l=r(37901),c=r(49341),d=r(10629),p=r(58168);function u(e,t){}u.bind(null,"warn"),u.bind(null,"error");function g(){}function m(e,t,r){const n=t.map((t=>{const n=function(e,t){return{...e,...t}}(r,t.options);return e.addEventListener(t.eventName,t.fn,n),function(){e.removeEventListener(t.eventName,t.fn,n)}}));return function(){n.forEach((e=>{e()}))}}const f=!0,b="Invariant failed";class h extends Error{}function v(e,t){throw new h(f?b:`${b}: ${t||""}`)}h.prototype.toString=function(){return this.message};class I extends n.Component{constructor(...e){super(...e),this.callbacks=null,this.unbind=g,this.onWindowError=e=>{const t=this.getCallbacks();t.isDragging()&&t.tryAbort();e.error instanceof h&&e.preventDefault()},this.getCallbacks=()=>{if(!this.callbacks)throw new Error("Unable to find AppCallbacks in <ErrorBoundary/>");return this.callbacks},this.setCallbacks=e=>{this.callbacks=e}}componentDidMount(){this.unbind=m(window,[{eventName:"error",fn:this.onWindowError}])}componentDidCatch(e){if(!(e instanceof h))throw e;this.setState({})}componentWillUnmount(){this.unbind()}render(){return this.props.children(this.setCallbacks)}}const y=e=>e+1,D=(e,t)=>{const r=e.droppableId===t.droppableId,n=y(e.index),o=y(t.index);return r?`\n      You have moved the item from position ${n}\n      to position ${o}\n    `:`\n    You have moved the item from position ${n}\n    in list ${e.droppableId}\n    to list ${t.droppableId}\n    in position ${o}\n  `},x=(e,t,r)=>t.droppableId===r.droppableId?`\n      The item ${e}\n      has been combined with ${r.draggableId}`:`\n      The item ${e}\n      in list ${t.droppableId}\n      has been combined with ${r.draggableId}\n      in list ${r.droppableId}\n    `,A=e=>`\n  The item has returned to its starting position\n  of ${y(e.index)}\n`,E={dragHandleUsageInstructions:"\n  Press space bar to start a drag.\n  When dragging you can use the arrow keys to move the item around and escape to cancel.\n  Some screen readers may require you to be in focus mode or to use your pass through key\n",onDragStart:e=>`\n  You have lifted an item in position ${y(e.source.index)}\n`,onDragUpdate:e=>{const t=e.destination;if(t)return D(e.source,t);const r=e.combine;return r?x(e.draggableId,e.source,r):"You are over an area that cannot be dropped on"},onDragEnd:e=>{if("CANCEL"===e.reason)return`\n      Movement cancelled.\n      ${A(e.source)}\n    `;const t=e.destination,r=e.combine;return t?`\n      You have dropped the item.\n      ${D(e.source,t)}\n    `:r?`\n      You have dropped the item.\n      ${x(e.draggableId,e.source,r)}\n    `:`\n    The item has been dropped while not over a drop area.\n    ${A(e.source)}\n  `}},w={x:0,y:0},C=(e,t)=>({x:e.x+t.x,y:e.y+t.y}),S=(e,t)=>({x:e.x-t.x,y:e.y-t.y}),B=(e,t)=>e.x===t.x&&e.y===t.y,O=e=>({x:0!==e.x?-e.x:0,y:0!==e.y?-e.y:0}),R=(e,t,r=0)=>"x"===e?{x:t,y:r}:{x:r,y:t},N=(e,t)=>Math.sqrt((t.x-e.x)**2+(t.y-e.y)**2),P=(e,t)=>Math.min(...t.map((t=>N(e,t)))),T=e=>t=>({x:e(t.x),y:e(t.y)});const L=(e,t)=>({top:e.top+t.y,left:e.left+t.x,bottom:e.bottom+t.y,right:e.right+t.x}),G=e=>[{x:e.left,y:e.top},{x:e.right,y:e.top},{x:e.left,y:e.bottom},{x:e.right,y:e.bottom}],M=(e,t)=>t&&t.shouldClipSubject?((e,t)=>{const r=(0,l.l)({top:Math.max(t.top,e.top),right:Math.min(t.right,e.right),bottom:Math.min(t.bottom,e.bottom),left:Math.max(t.left,e.left)});return r.width<=0||r.height<=0?null:r})(t.pageMarginBox,e):(0,l.l)(e);var _=({page:e,withPlaceholder:t,axis:r,frame:n})=>{const o=((e,t)=>t?L(e,t.scroll.diff.displacement):e)(e.marginBox,n),i=((e,t,r)=>r&&r.increasedBy?{...e,[t.end]:e[t.end]+r.increasedBy[t.line]}:e)(o,r,t);return{page:e,withPlaceholder:t,active:M(i,n)}},F=(e,t)=>{e.frame||v();const r=e.frame,n=S(t,r.scroll.initial),o=O(n),i={...r,scroll:{initial:r.scroll.initial,current:t,diff:{value:n,displacement:o},max:r.scroll.max}},a=_({page:e.subject.page,withPlaceholder:e.subject.withPlaceholder,axis:e.axis,frame:i});return{...e,frame:i,subject:a}};const k=(0,c.A)((e=>e.reduce(((e,t)=>(e[t.descriptor.id]=t,e)),{}))),W=(0,c.A)((e=>e.reduce(((e,t)=>(e[t.descriptor.id]=t,e)),{}))),U=(0,c.A)((e=>Object.values(e))),$=(0,c.A)((e=>Object.values(e)));var H=(0,c.A)(((e,t)=>{const r=$(t).filter((t=>e===t.descriptor.droppableId)).sort(((e,t)=>e.descriptor.index-t.descriptor.index));return r}));function j(e){return e.at&&"REORDER"===e.at.type?e.at.destination:null}function V(e){return e.at&&"COMBINE"===e.at.type?e.at.combine:null}var K=(0,c.A)(((e,t)=>t.filter((t=>t.descriptor.id!==e.descriptor.id)))),q=(e,t)=>e.descriptor.droppableId===t.descriptor.id;const z={point:w,value:0},Y={invisible:{},visible:{},all:[]},J={displaced:Y,displacedBy:z,at:null};var X=(e,t)=>r=>e<=r&&r<=t,Q=e=>{const t=X(e.top,e.bottom),r=X(e.left,e.right);return n=>{if(t(n.top)&&t(n.bottom)&&r(n.left)&&r(n.right))return!0;const o=t(n.top)||t(n.bottom),i=r(n.left)||r(n.right);if(o&&i)return!0;const a=n.top<e.top&&n.bottom>e.bottom,s=n.left<e.left&&n.right>e.right;if(a&&s)return!0;return a&&i||s&&o}},Z=e=>{const t=X(e.top,e.bottom),r=X(e.left,e.right);return e=>t(e.top)&&t(e.bottom)&&r(e.left)&&r(e.right)};const ee={direction:"vertical",line:"y",crossAxisLine:"x",start:"top",end:"bottom",size:"height",crossAxisStart:"left",crossAxisEnd:"right",crossAxisSize:"width"},te={direction:"horizontal",line:"x",crossAxisLine:"y",start:"left",end:"right",size:"width",crossAxisStart:"top",crossAxisEnd:"bottom",crossAxisSize:"height"};const re=({target:e,destination:t,viewport:r,withDroppableDisplacement:n,isVisibleThroughFrameFn:o})=>{const i=n?((e,t)=>{const r=t.frame?t.frame.scroll.diff.displacement:w;return L(e,r)})(e,t):e;return((e,t,r)=>!!t.subject.active&&r(t.subject.active)(e))(i,t,o)&&((e,t,r)=>r(t)(e))(i,r,o)},ne=e=>re({...e,isVisibleThroughFrameFn:Z});function oe({afterDragging:e,destination:t,displacedBy:r,viewport:n,forceShouldAnimate:o,last:i}){return e.reduce((function(e,a){const s=function(e,t){const r=e.page.marginBox,n={top:t.point.y,right:0,bottom:0,left:t.point.x};return(0,l.l)((0,l.fT)(r,n))}(a,r),c=a.descriptor.id;e.all.push(c);var d;if(!(d={target:s,destination:t,viewport:n,withDroppableDisplacement:!0},re({...d,isVisibleThroughFrameFn:Q})))return e.invisible[a.descriptor.id]=!0,e;const p=((e,t,r)=>{if("boolean"==typeof r)return r;if(!t)return!0;const{invisible:n,visible:o}=t;if(n[e])return!1;const i=o[e];return!i||i.shouldAnimate})(c,i,o),u={draggableId:c,shouldAnimate:p};return e.visible[c]=u,e}),{all:[],visible:{},invisible:{}})}function ie({insideDestination:e,inHomeList:t,displacedBy:r,destination:n}){const o=function(e,t){if(!e.length)return 0;const r=e[e.length-1].descriptor.index;return t.inHomeList?r:r+1}(e,{inHomeList:t});return{displaced:Y,displacedBy:r,at:{type:"REORDER",destination:{droppableId:n.descriptor.id,index:o}}}}function ae({draggable:e,insideDestination:t,destination:r,viewport:n,displacedBy:o,last:i,index:a,forceShouldAnimate:s}){const l=q(e,r);if(null==a)return ie({insideDestination:t,inHomeList:l,displacedBy:o,destination:r});const c=t.find((e=>e.descriptor.index===a));if(!c)return ie({insideDestination:t,inHomeList:l,displacedBy:o,destination:r});const d=K(e,t),p=t.indexOf(c);return{displaced:oe({afterDragging:d.slice(p),destination:r,displacedBy:o,last:i,viewport:n.frame,forceShouldAnimate:s}),displacedBy:o,at:{type:"REORDER",destination:{droppableId:r.descriptor.id,index:a}}}}function se(e,t){return Boolean(t.effected[e])}var le=({isMovingForward:e,isInHomeList:t,draggable:r,draggables:n,destination:o,insideDestination:i,previousImpact:a,viewport:s,afterCritical:l})=>{const c=a.at;if(c||v(),"REORDER"===c.type){const n=(({isMovingForward:e,isInHomeList:t,insideDestination:r,location:n})=>{if(!r.length)return null;const o=n.index,i=e?o+1:o-1,a=r[0].descriptor.index,s=r[r.length-1].descriptor.index;return i<a||i>(t?s:s+1)?null:i})({isMovingForward:e,isInHomeList:t,location:c.destination,insideDestination:i});return null==n?null:ae({draggable:r,insideDestination:i,destination:o,viewport:s,last:a.displaced,displacedBy:a.displacedBy,index:n})}const d=(({isMovingForward:e,destination:t,draggables:r,combine:n,afterCritical:o})=>{if(!t.isCombineEnabled)return null;const i=n.draggableId,a=r[i].descriptor.index;return se(i,o)?e?a:a-1:e?a+1:a})({isMovingForward:e,destination:o,displaced:a.displaced,draggables:n,combine:c.combine,afterCritical:l});return null==d?null:ae({draggable:r,insideDestination:i,destination:o,viewport:s,last:a.displaced,displacedBy:a.displacedBy,index:d})},ce=({afterCritical:e,impact:t,draggables:r})=>{const n=V(t);n||v();const o=n.draggableId,i=r[o].page.borderBox.center,a=(({displaced:e,afterCritical:t,combineWith:r,displacedBy:n})=>{const o=Boolean(e.visible[r]||e.invisible[r]);return se(r,t)?o?w:O(n.point):o?n.point:w})({displaced:t.displaced,afterCritical:e,combineWith:o,displacedBy:t.displacedBy});return C(i,a)};const de=(e,t)=>t.margin[e.start]+t.borderBox[e.size]/2,pe=(e,t,r)=>t[e.crossAxisStart]+r.margin[e.crossAxisStart]+r.borderBox[e.crossAxisSize]/2,ue=({axis:e,moveRelativeTo:t,isMoving:r})=>R(e.line,t.marginBox[e.end]+de(e,r),pe(e,t.marginBox,r)),ge=({axis:e,moveRelativeTo:t,isMoving:r})=>R(e.line,t.marginBox[e.start]-((e,t)=>t.margin[e.end]+t.borderBox[e.size]/2)(e,r),pe(e,t.marginBox,r));var me=({impact:e,draggable:t,draggables:r,droppable:n,afterCritical:o})=>{const i=H(n.descriptor.id,r),a=t.page,s=n.axis;if(!i.length)return(({axis:e,moveInto:t,isMoving:r})=>R(e.line,t.contentBox[e.start]+de(e,r),pe(e,t.contentBox,r)))({axis:s,moveInto:n.page,isMoving:a});const{displaced:c,displacedBy:d}=e,p=c.all[0];if(p){const e=r[p];if(se(p,o))return ge({axis:s,moveRelativeTo:e.page,isMoving:a});const t=(0,l.cY)(e.page,d.point);return ge({axis:s,moveRelativeTo:t,isMoving:a})}const u=i[i.length-1];if(u.descriptor.id===t.descriptor.id)return a.borderBox.center;if(se(u.descriptor.id,o)){const e=(0,l.cY)(u.page,O(o.displacedBy.point));return ue({axis:s,moveRelativeTo:e,isMoving:a})}return ue({axis:s,moveRelativeTo:u.page,isMoving:a})},fe=(e,t)=>{const r=e.frame;return r?C(t,r.scroll.diff.displacement):t};var be=e=>{const t=(({impact:e,draggable:t,droppable:r,draggables:n,afterCritical:o})=>{const i=t.page.borderBox.center,a=e.at;return r&&a?"REORDER"===a.type?me({impact:e,draggable:t,draggables:n,droppable:r,afterCritical:o}):ce({impact:e,draggables:n,afterCritical:o}):i})(e),r=e.droppable;return r?fe(r,t):t},he=(e,t)=>{const r=S(t,e.scroll.initial),n=O(r);return{frame:(0,l.l)({top:t.y,bottom:t.y+e.frame.height,left:t.x,right:t.x+e.frame.width}),scroll:{initial:e.scroll.initial,max:e.scroll.max,current:t,diff:{value:r,displacement:n}}}};function ve(e,t){return e.map((e=>t[e]))}var Ie=({pageBorderBoxCenter:e,draggable:t,viewport:r})=>{const n=((e,t)=>C(e.scroll.diff.displacement,t))(r,e),o=S(n,t.page.borderBox.center);return C(t.client.borderBox.center,o)},ye=({draggable:e,destination:t,newPageBorderBoxCenter:r,viewport:n,withDroppableDisplacement:o,onlyOnMainAxis:i=!1})=>{const a=S(r,e.page.borderBox.center),s={target:L(e.page.borderBox,a),destination:t,withDroppableDisplacement:o,viewport:n};return i?(e=>{return re({...e,isVisibleThroughFrameFn:(t=e.destination.axis,e=>{const r=X(e.top,e.bottom),n=X(e.left,e.right);return e=>t===ee?r(e.top)&&r(e.bottom):n(e.left)&&n(e.right)})});var t})(s):ne(s)},De=({isMovingForward:e,draggable:t,destination:r,draggables:n,previousImpact:o,viewport:i,previousPageBorderBoxCenter:a,previousClientSelection:s,afterCritical:l})=>{if(!r.isEnabled)return null;const c=H(r.descriptor.id,n),d=q(t,r),p=(({isMovingForward:e,draggable:t,destination:r,insideDestination:n,previousImpact:o})=>{if(!r.isCombineEnabled)return null;if(!j(o))return null;function i(e){const t={type:"COMBINE",combine:{draggableId:e,droppableId:r.descriptor.id}};return{...o,at:t}}const a=o.displaced.all,s=a.length?a[0]:null;if(e)return s?i(s):null;const l=K(t,n);if(!s)return l.length?i(l[l.length-1].descriptor.id):null;const c=l.findIndex((e=>e.descriptor.id===s));-1===c&&v();const d=c-1;return d<0?null:i(l[d].descriptor.id)})({isMovingForward:e,draggable:t,destination:r,insideDestination:c,previousImpact:o})||le({isMovingForward:e,isInHomeList:d,draggable:t,draggables:n,destination:r,insideDestination:c,previousImpact:o,viewport:i,afterCritical:l});if(!p)return null;const u=be({impact:p,draggable:t,droppable:r,draggables:n,afterCritical:l});if(ye({draggable:t,destination:r,newPageBorderBoxCenter:u,viewport:i.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0})){return{clientSelection:Ie({pageBorderBoxCenter:u,draggable:t,viewport:i}),impact:p,scrollJumpRequest:null}}const g=S(u,a),m=(({impact:e,viewport:t,destination:r,draggables:n,maxScrollChange:o})=>{const i=he(t,C(t.scroll.current,o)),a=r.frame?F(r,C(r.frame.scroll.current,o)):r,s=e.displaced,l=oe({afterDragging:ve(s.all,n),destination:r,displacedBy:e.displacedBy,viewport:i.frame,last:s,forceShouldAnimate:!1}),c=oe({afterDragging:ve(s.all,n),destination:a,displacedBy:e.displacedBy,viewport:t.frame,last:s,forceShouldAnimate:!1}),d={},p={},u=[s,l,c];return s.all.forEach((e=>{const t=function(e,t){for(let r=0;r<t.length;r++){const n=t[r].visible[e];if(n)return n}return null}(e,u);t?p[e]=t:d[e]=!0})),{...e,displaced:{all:s.all,invisible:d,visible:p}}})({impact:p,viewport:i,destination:r,draggables:n,maxScrollChange:g});return{clientSelection:s,impact:m,scrollJumpRequest:g}};const xe=e=>{const t=e.subject.active;return t||v(),t};const Ae=(e,t)=>{const r=e.page.borderBox.center;return se(e.descriptor.id,t)?S(r,t.displacedBy.point):r},Ee=(e,t)=>{const r=e.page.borderBox;return se(e.descriptor.id,t)?L(r,O(t.displacedBy.point)):r};var we=(0,c.A)((function(e,t){const r=t[e.line];return{value:r,point:R(e.line,r)}}));const Ce=(e,t)=>({...e,scroll:{...e.scroll,max:t}}),Se=(e,t,r)=>{const n=e.frame;q(t,e)&&v(),e.subject.withPlaceholder&&v();const o=we(e.axis,t.displaceBy).point,i=((e,t,r)=>{const n=e.axis;if("virtual"===e.descriptor.mode)return R(n.line,t[n.line]);const o=e.subject.page.contentBox[n.size],i=H(e.descriptor.id,r).reduce(((e,t)=>e+t.client.marginBox[n.size]),0)+t[n.line]-o;return i<=0?null:R(n.line,i)})(e,o,r),a={placeholderSize:o,increasedBy:i,oldFrameMaxScroll:e.frame?e.frame.scroll.max:null};if(!n){const t=_({page:e.subject.page,withPlaceholder:a,axis:e.axis,frame:e.frame});return{...e,subject:t}}const s=i?C(n.scroll.max,i):n.scroll.max,l=Ce(n,s),c=_({page:e.subject.page,withPlaceholder:a,axis:e.axis,frame:l});return{...e,subject:c,frame:l}};var Be=({isMovingForward:e,previousPageBorderBoxCenter:t,draggable:r,isOver:n,draggables:o,droppables:i,viewport:a,afterCritical:s})=>{const l=(({isMovingForward:e,pageBorderBoxCenter:t,source:r,droppables:n,viewport:o})=>{const i=r.subject.active;if(!i)return null;const a=r.axis,s=X(i[a.start],i[a.end]),l=U(n).filter((e=>e!==r)).filter((e=>e.isEnabled)).filter((e=>Boolean(e.subject.active))).filter((e=>Q(o.frame)(xe(e)))).filter((t=>{const r=xe(t);return e?i[a.crossAxisEnd]<r[a.crossAxisEnd]:r[a.crossAxisStart]<i[a.crossAxisStart]})).filter((e=>{const t=xe(e),r=X(t[a.start],t[a.end]);return s(t[a.start])||s(t[a.end])||r(i[a.start])||r(i[a.end])})).sort(((t,r)=>{const n=xe(t)[a.crossAxisStart],o=xe(r)[a.crossAxisStart];return e?n-o:o-n})).filter(((e,t,r)=>xe(e)[a.crossAxisStart]===xe(r[0])[a.crossAxisStart]));if(!l.length)return null;if(1===l.length)return l[0];const c=l.filter((e=>X(xe(e)[a.start],xe(e)[a.end])(t[a.line])));return 1===c.length?c[0]:c.length>1?c.sort(((e,t)=>xe(e)[a.start]-xe(t)[a.start]))[0]:l.sort(((e,r)=>{const n=P(t,G(xe(e))),o=P(t,G(xe(r)));return n!==o?n-o:xe(e)[a.start]-xe(r)[a.start]}))[0]})({isMovingForward:e,pageBorderBoxCenter:t,source:n,droppables:i,viewport:a});if(!l)return null;const c=H(l.descriptor.id,o),d=(({pageBorderBoxCenter:e,viewport:t,destination:r,insideDestination:n,afterCritical:o})=>{const i=n.filter((e=>ne({target:Ee(e,o),destination:r,viewport:t.frame,withDroppableDisplacement:!0}))).sort(((t,n)=>{const i=N(e,fe(r,Ae(t,o))),a=N(e,fe(r,Ae(n,o)));return i<a?-1:a<i?1:t.descriptor.index-n.descriptor.index}));return i[0]||null})({pageBorderBoxCenter:t,viewport:a,destination:l,insideDestination:c,afterCritical:s}),p=(({previousPageBorderBoxCenter:e,moveRelativeTo:t,insideDestination:r,draggable:n,draggables:o,destination:i,viewport:a,afterCritical:s})=>{if(!t){if(r.length)return null;const e={displaced:Y,displacedBy:z,at:{type:"REORDER",destination:{droppableId:i.descriptor.id,index:0}}},t=be({impact:e,draggable:n,droppable:i,draggables:o,afterCritical:s}),l=q(n,i)?i:Se(i,n,o);return ye({draggable:n,destination:l,newPageBorderBoxCenter:t,viewport:a.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0})?e:null}const l=Boolean(e[i.axis.line]<=t.page.borderBox.center[i.axis.line]),c=(()=>{const e=t.descriptor.index;return t.descriptor.id===n.descriptor.id||l?e:e+1})(),d=we(i.axis,n.displaceBy);return ae({draggable:n,insideDestination:r,destination:i,viewport:a,displacedBy:d,last:Y,index:c})})({previousPageBorderBoxCenter:t,destination:l,draggable:r,draggables:o,moveRelativeTo:d,insideDestination:c,viewport:a,afterCritical:s});if(!p)return null;const u=be({impact:p,draggable:r,droppable:l,draggables:o,afterCritical:s});return{clientSelection:Ie({pageBorderBoxCenter:u,draggable:r,viewport:a}),impact:p,scrollJumpRequest:null}},Oe=e=>{const t=e.at;return t?"REORDER"===t.type?t.destination.droppableId:t.combine.droppableId:null};var Re=({state:e,type:t})=>{const r=((e,t)=>{const r=Oe(e);return r?t[r]:null})(e.impact,e.dimensions.droppables),n=Boolean(r),o=e.dimensions.droppables[e.critical.droppable.id],i=r||o,a=i.axis.direction,s="vertical"===a&&("MOVE_UP"===t||"MOVE_DOWN"===t)||"horizontal"===a&&("MOVE_LEFT"===t||"MOVE_RIGHT"===t);if(s&&!n)return null;const l="MOVE_DOWN"===t||"MOVE_RIGHT"===t,c=e.dimensions.draggables[e.critical.draggable.id],d=e.current.page.borderBoxCenter,{draggables:p,droppables:u}=e.dimensions;return s?De({isMovingForward:l,previousPageBorderBoxCenter:d,draggable:c,destination:i,draggables:p,viewport:e.viewport,previousClientSelection:e.current.client.selection,previousImpact:e.impact,afterCritical:e.afterCritical}):Be({isMovingForward:l,previousPageBorderBoxCenter:d,draggable:c,isOver:i,draggables:p,droppables:u,viewport:e.viewport,afterCritical:e.afterCritical})};function Ne(e){return"DRAGGING"===e.phase||"COLLECTING"===e.phase}function Pe(e){const t=X(e.top,e.bottom),r=X(e.left,e.right);return function(e){return t(e.y)&&r(e.x)}}function Te({pageBorderBox:e,draggable:t,droppables:r}){const n=U(r).filter((t=>{if(!t.isEnabled)return!1;const r=t.subject.active;if(!r)return!1;if(o=r,!((n=e).left<o.right&&n.right>o.left&&n.top<o.bottom&&n.bottom>o.top))return!1;var n,o;if(Pe(r)(e.center))return!0;const i=t.axis,a=r.center[i.crossAxisLine],s=e[i.crossAxisStart],l=e[i.crossAxisEnd],c=X(r[i.crossAxisStart],r[i.crossAxisEnd]),d=c(s),p=c(l);return!d&&!p||(d?s<a:l>a)}));return n.length?1===n.length?n[0].descriptor.id:function({pageBorderBox:e,draggable:t,candidates:r}){const n=t.page.borderBox.center,o=r.map((t=>{const r=t.axis,o=R(t.axis.line,e.center[r.line],t.page.borderBox.center[r.crossAxisLine]);return{id:t.descriptor.id,distance:N(n,o)}})).sort(((e,t)=>t.distance-e.distance));return o[0]?o[0].id:null}({pageBorderBox:e,draggable:t,candidates:n}):null}const Le=(e,t)=>(0,l.l)(L(e,t));function Ge({displaced:e,id:t}){return Boolean(e.visible[t]||e.invisible[t])}var Me=({pageOffset:e,draggable:t,draggables:r,droppables:n,previousImpact:o,viewport:i,afterCritical:a})=>{const s=Le(t.page.borderBox,e),l=Te({pageBorderBox:s,draggable:t,droppables:n});if(!l)return J;const c=n[l],d=H(c.descriptor.id,r),p=((e,t)=>{const r=e.frame;return r?Le(t,r.scroll.diff.value):t})(c,s);return(({draggable:e,pageBorderBoxWithDroppableScroll:t,previousImpact:r,destination:n,insideDestination:o,afterCritical:i})=>{if(!n.isCombineEnabled)return null;const a=n.axis,s=we(n.axis,e.displaceBy),l=s.value,c=t[a.start],d=t[a.end],p=K(e,o).find((e=>{const t=e.descriptor.id,n=e.page.borderBox,o=n[a.size]/4,s=se(t,i),p=Ge({displaced:r.displaced,id:t});return s?p?d>n[a.start]+o&&d<n[a.end]-o:c>n[a.start]-l+o&&c<n[a.end]-l-o:p?d>n[a.start]+l+o&&d<n[a.end]+l-o:c>n[a.start]+o&&c<n[a.end]-o}));return p?{displacedBy:s,displaced:r.displaced,at:{type:"COMBINE",combine:{draggableId:p.descriptor.id,droppableId:n.descriptor.id}}}:null})({pageBorderBoxWithDroppableScroll:p,draggable:t,previousImpact:o,destination:c,insideDestination:d,afterCritical:a})||(({pageBorderBoxWithDroppableScroll:e,draggable:t,destination:r,insideDestination:n,last:o,viewport:i,afterCritical:a})=>{const s=r.axis,l=we(r.axis,t.displaceBy),c=l.value,d=e[s.start],p=e[s.end],u=function({draggable:e,closest:t,inHomeList:r}){return t?r&&t.descriptor.index>e.descriptor.index?t.descriptor.index-1:t.descriptor.index:null}({draggable:t,closest:K(t,n).find((e=>{const t=e.descriptor.id,r=e.page.borderBox.center[s.line],n=se(t,a),i=Ge({displaced:o,id:t});return n?i?p<=r:d<r-c:i?p<=r+c:d<r}))||null,inHomeList:q(t,r)});return ae({draggable:t,insideDestination:n,destination:r,viewport:i,last:o,displacedBy:l,index:u})})({pageBorderBoxWithDroppableScroll:p,draggable:t,destination:c,insideDestination:d,last:o.displaced,viewport:i,afterCritical:a})},_e=(e,t)=>({...e,[t.descriptor.id]:t});const Fe=({previousImpact:e,impact:t,droppables:r})=>{const n=Oe(e),o=Oe(t);if(!n)return r;if(n===o)return r;const i=r[n];if(!i.subject.withPlaceholder)return r;const a=(e=>{const t=e.subject.withPlaceholder;t||v();const r=e.frame;if(!r){const t=_({page:e.subject.page,axis:e.axis,frame:null,withPlaceholder:null});return{...e,subject:t}}const n=t.oldFrameMaxScroll;n||v();const o=Ce(r,n),i=_({page:e.subject.page,axis:e.axis,frame:o,withPlaceholder:null});return{...e,subject:i,frame:o}})(i);return _e(r,a)};var ke=({state:e,clientSelection:t,dimensions:r,viewport:n,impact:o,scrollJumpRequest:i})=>{const a=n||e.viewport,s=r||e.dimensions,l=t||e.current.client.selection,c=S(l,e.initial.client.selection),d={offset:c,selection:l,borderBoxCenter:C(e.initial.client.borderBoxCenter,c)},p={selection:C(d.selection,a.scroll.current),borderBoxCenter:C(d.borderBoxCenter,a.scroll.current),offset:C(d.offset,a.scroll.diff.value)},u={client:d,page:p};if("COLLECTING"===e.phase)return{...e,dimensions:s,viewport:a,current:u};const g=s.draggables[e.critical.draggable.id],m=o||Me({pageOffset:p.offset,draggable:g,draggables:s.draggables,droppables:s.droppables,previousImpact:e.impact,viewport:a,afterCritical:e.afterCritical}),f=(({draggable:e,draggables:t,droppables:r,previousImpact:n,impact:o})=>{const i=Fe({previousImpact:n,impact:o,droppables:r}),a=Oe(o);if(!a)return i;const s=r[a];if(q(e,s))return i;if(s.subject.withPlaceholder)return i;const l=Se(s,e,t);return _e(i,l)})({draggable:g,impact:m,previousImpact:e.impact,draggables:s.draggables,droppables:s.droppables});return{...e,current:u,dimensions:{draggables:s.draggables,droppables:f},impact:m,viewport:a,scrollJumpRequest:i||null,forceShouldAnimate:!i&&null}};var We=({impact:e,viewport:t,draggables:r,destination:n,forceShouldAnimate:o})=>{const i=e.displaced,a=function(e,t){return e.map((e=>t[e]))}(i.all,r),s=oe({afterDragging:a,destination:n,displacedBy:e.displacedBy,viewport:t.frame,forceShouldAnimate:o,last:i});return{...e,displaced:s}},Ue=({impact:e,draggable:t,droppable:r,draggables:n,viewport:o,afterCritical:i})=>{const a=be({impact:e,draggable:t,draggables:n,droppable:r,afterCritical:i});return Ie({pageBorderBoxCenter:a,draggable:t,viewport:o})},$e=({state:e,dimensions:t,viewport:r})=>{"SNAP"!==e.movementMode&&v();const n=e.impact,o=r||e.viewport,i=t||e.dimensions,{draggables:a,droppables:s}=i,l=a[e.critical.draggable.id],c=Oe(n);c||v();const d=s[c],p=We({impact:n,viewport:o,destination:d,draggables:a}),u=Ue({impact:p,draggable:l,droppable:d,draggables:a,viewport:o,afterCritical:e.afterCritical});return ke({impact:p,clientSelection:u,state:e,dimensions:i,viewport:o})},He=({draggable:e,home:t,draggables:r,viewport:n})=>{const o=we(t.axis,e.displaceBy),i=H(t.descriptor.id,r),a=i.indexOf(e);-1===a&&v();const s=i.slice(a+1),l=s.reduce(((e,t)=>(e[t.descriptor.id]=!0,e)),{}),c={inVirtualList:"virtual"===t.descriptor.mode,displacedBy:o,effected:l};var d;return{impact:{displaced:oe({afterDragging:s,destination:t,displacedBy:o,last:null,viewport:n.frame,forceShouldAnimate:!1}),displacedBy:o,at:{type:"REORDER",destination:(d=e.descriptor,{index:d.index,droppableId:d.droppableId})}},afterCritical:c}};const je=e=>{0},Ve=e=>{0};var Ke=({additions:e,updatedDroppables:t,viewport:r})=>{const n=r.scroll.diff.value;return e.map((e=>{const o=e.descriptor.droppableId,i=(e=>{const t=e.frame;return t||v(),t})(t[o]),a=i.scroll.diff.value,s=(({draggable:e,offset:t,initialWindowScroll:r})=>{const n=(0,l.cY)(e.client,t),o=(0,l.SQ)(n,r);return{...e,placeholder:{...e.placeholder,client:n},client:n,page:o}})({draggable:e,offset:C(n,a),initialWindowScroll:r.scroll.initial});return s}))};const qe=e=>"SNAP"===e.movementMode,ze=(e,t,r)=>{const n=((e,t)=>({draggables:e.draggables,droppables:_e(e.droppables,t)}))(e.dimensions,t);return!qe(e)||r?ke({state:e,dimensions:n}):$e({state:e,dimensions:n})};function Ye(e){return e.isDragging&&"SNAP"===e.movementMode?{...e,scrollJumpRequest:null}:e}const Je={phase:"IDLE",completed:null,shouldFlush:!1};var Xe=(e=Je,t)=>{if("FLUSH"===t.type)return{...Je,shouldFlush:!0};if("INITIAL_PUBLISH"===t.type){"IDLE"!==e.phase&&v();const{critical:r,clientSelection:n,viewport:o,dimensions:i,movementMode:a}=t.payload,s=i.draggables[r.draggable.id],l=i.droppables[r.droppable.id],c={selection:n,borderBoxCenter:s.client.borderBox.center,offset:w},d={client:c,page:{selection:C(c.selection,o.scroll.initial),borderBoxCenter:C(c.selection,o.scroll.initial),offset:C(c.selection,o.scroll.diff.value)}},p=U(i.droppables).every((e=>!e.isFixedOnPage)),{impact:u,afterCritical:g}=He({draggable:s,home:l,draggables:i.draggables,viewport:o});return{phase:"DRAGGING",isDragging:!0,critical:r,movementMode:a,dimensions:i,initial:d,current:d,isWindowScrollAllowed:p,impact:u,afterCritical:g,onLiftImpact:u,viewport:o,scrollJumpRequest:null,forceShouldAnimate:null}}if("COLLECTION_STARTING"===t.type){if("COLLECTING"===e.phase||"DROP_PENDING"===e.phase)return e;"DRAGGING"!==e.phase&&v();return{...e,phase:"COLLECTING"}}if("PUBLISH_WHILE_DRAGGING"===t.type)return"COLLECTING"!==e.phase&&"DROP_PENDING"!==e.phase&&v(),(({state:e,published:t})=>{je();const r=t.modified.map((t=>{const r=e.dimensions.droppables[t.droppableId];return F(r,t.scroll)})),n={...e.dimensions.droppables,...k(r)},o=W(Ke({additions:t.additions,updatedDroppables:n,viewport:e.viewport})),i={...e.dimensions.draggables,...o};t.removals.forEach((e=>{delete i[e]}));const a={droppables:n,draggables:i},s=Oe(e.impact),l=s?a.droppables[s]:null,c=a.draggables[e.critical.draggable.id],d=a.droppables[e.critical.droppable.id],{impact:p,afterCritical:u}=He({draggable:c,home:d,draggables:i,viewport:e.viewport}),g=l&&l.isCombineEnabled?e.impact:p,m=Me({pageOffset:e.current.page.offset,draggable:a.draggables[e.critical.draggable.id],draggables:a.draggables,droppables:a.droppables,previousImpact:g,viewport:e.viewport,afterCritical:u});Ve();const f={...e,phase:"DRAGGING",impact:m,onLiftImpact:p,dimensions:a,afterCritical:u,forceShouldAnimate:!1};return"COLLECTING"===e.phase?f:{...f,phase:"DROP_PENDING",reason:e.reason,isWaiting:!1}})({state:e,published:t.payload});if("MOVE"===t.type){if("DROP_PENDING"===e.phase)return e;Ne(e)||v();const{client:r}=t.payload;return B(r,e.current.client.selection)?e:ke({state:e,clientSelection:r,impact:qe(e)?e.impact:null})}if("UPDATE_DROPPABLE_SCROLL"===t.type){if("DROP_PENDING"===e.phase)return Ye(e);if("COLLECTING"===e.phase)return Ye(e);Ne(e)||v();const{id:r,newScroll:n}=t.payload,o=e.dimensions.droppables[r];if(!o)return e;const i=F(o,n);return ze(e,i,!1)}if("UPDATE_DROPPABLE_IS_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;Ne(e)||v();const{id:r,isEnabled:n}=t.payload,o=e.dimensions.droppables[r];o||v(),o.isEnabled===n&&v();const i={...o,isEnabled:n};return ze(e,i,!0)}if("UPDATE_DROPPABLE_IS_COMBINE_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;Ne(e)||v();const{id:r,isCombineEnabled:n}=t.payload,o=e.dimensions.droppables[r];o||v(),o.isCombineEnabled===n&&v();const i={...o,isCombineEnabled:n};return ze(e,i,!0)}if("MOVE_BY_WINDOW_SCROLL"===t.type){if("DROP_PENDING"===e.phase||"DROP_ANIMATING"===e.phase)return e;Ne(e)||v(),e.isWindowScrollAllowed||v();const r=t.payload.newScroll;if(B(e.viewport.scroll.current,r))return Ye(e);const n=he(e.viewport,r);return qe(e)?$e({state:e,viewport:n}):ke({state:e,viewport:n})}if("UPDATE_VIEWPORT_MAX_SCROLL"===t.type){if(!Ne(e))return e;const r=t.payload.maxScroll;if(B(r,e.viewport.scroll.max))return e;const n={...e.viewport,scroll:{...e.viewport.scroll,max:r}};return{...e,viewport:n}}if("MOVE_UP"===t.type||"MOVE_DOWN"===t.type||"MOVE_LEFT"===t.type||"MOVE_RIGHT"===t.type){if("COLLECTING"===e.phase||"DROP_PENDING"===e.phase)return e;"DRAGGING"!==e.phase&&v();const r=Re({state:e,type:t.type});return r?ke({state:e,impact:r.impact,clientSelection:r.clientSelection,scrollJumpRequest:r.scrollJumpRequest}):e}if("DROP_PENDING"===t.type){const r=t.payload.reason;"COLLECTING"!==e.phase&&v();return{...e,phase:"DROP_PENDING",isWaiting:!0,reason:r}}if("DROP_ANIMATE"===t.type){const{completed:r,dropDuration:n,newHomeClientOffset:o}=t.payload;"DRAGGING"!==e.phase&&"DROP_PENDING"!==e.phase&&v();return{phase:"DROP_ANIMATING",completed:r,dropDuration:n,newHomeClientOffset:o,dimensions:e.dimensions}}if("DROP_COMPLETE"===t.type){const{completed:e}=t.payload;return{phase:"IDLE",completed:e,shouldFlush:!1}}return e};function Qe(e,t){return e instanceof Object&&"type"in e&&e.type===t}const Ze=e=>({type:"LIFT",payload:e}),et=e=>({type:"PUBLISH_WHILE_DRAGGING",payload:e}),tt=()=>({type:"COLLECTION_STARTING",payload:null}),rt=e=>({type:"UPDATE_DROPPABLE_SCROLL",payload:e}),nt=e=>({type:"UPDATE_DROPPABLE_IS_ENABLED",payload:e}),ot=e=>({type:"UPDATE_DROPPABLE_IS_COMBINE_ENABLED",payload:e}),it=e=>({type:"MOVE",payload:e}),at=()=>({type:"MOVE_UP",payload:null}),st=()=>({type:"MOVE_DOWN",payload:null}),lt=()=>({type:"MOVE_RIGHT",payload:null}),ct=()=>({type:"MOVE_LEFT",payload:null}),dt=()=>({type:"FLUSH",payload:null}),pt=e=>({type:"DROP_COMPLETE",payload:e}),ut=e=>({type:"DROP",payload:e}),gt=()=>({type:"DROP_ANIMATION_FINISHED",payload:null});const mt="cubic-bezier(.2,1,.1,1)",ft={drop:0,combining:.7},bt={drop:.75},ht={outOfTheWay:.2,minDropTime:.33,maxDropTime:.55},vt=`${ht.outOfTheWay}s ${"cubic-bezier(0.2, 0, 0, 1)"}`,It={fluid:`opacity ${vt}`,snap:`transform ${vt}, opacity ${vt}`,drop:e=>{const t=`${e}s ${mt}`;return`transform ${t}, opacity ${t}`},outOfTheWay:`transform ${vt}`,placeholder:`height ${vt}, width ${vt}, margin ${vt}`},yt=e=>B(e,w)?void 0:`translate(${e.x}px, ${e.y}px)`,Dt=yt,xt=(e,t)=>{const r=yt(e);if(r)return t?`${r} scale(${bt.drop})`:r},{minDropTime:At,maxDropTime:Et}=ht,wt=Et-At;const Ct=({getState:e,dispatch:t})=>r=>n=>{if(!Qe(n,"DROP"))return void r(n);const o=e(),i=n.payload.reason;if("COLLECTING"===o.phase)return void t((e=>({type:"DROP_PENDING",payload:e}))({reason:i}));if("IDLE"===o.phase)return;"DROP_PENDING"===o.phase&&o.isWaiting&&v(),"DRAGGING"!==o.phase&&"DROP_PENDING"!==o.phase&&v();const a=o.critical,s=o.dimensions,l=s.draggables[o.critical.draggable.id],{impact:c,didDropInsideDroppable:d}=(({draggables:e,reason:t,lastImpact:r,home:n,viewport:o,onLiftImpact:i})=>{if(!r.at||"DROP"!==t)return{impact:We({draggables:e,impact:i,destination:n,viewport:o,forceShouldAnimate:!0}),didDropInsideDroppable:!1};return"REORDER"===r.at.type?{impact:r,didDropInsideDroppable:!0}:{impact:{...r,displaced:Y},didDropInsideDroppable:!0}})({reason:i,lastImpact:o.impact,afterCritical:o.afterCritical,onLiftImpact:o.onLiftImpact,home:o.dimensions.droppables[o.critical.droppable.id],viewport:o.viewport,draggables:o.dimensions.draggables}),p=d?j(c):null,u=d?V(c):null,g={index:a.draggable.index,droppableId:a.droppable.id},m={draggableId:l.descriptor.id,type:l.descriptor.type,source:g,reason:i,mode:o.movementMode,destination:p,combine:u},f=(({impact:e,draggable:t,dimensions:r,viewport:n,afterCritical:o})=>{const{draggables:i,droppables:a}=r,s=Oe(e),l=s?a[s]:null,c=a[t.descriptor.droppableId],d=Ue({impact:e,draggable:t,draggables:i,afterCritical:o,droppable:l||c,viewport:n});return S(d,t.client.borderBox.center)})({impact:c,draggable:l,dimensions:s,viewport:o.viewport,afterCritical:o.afterCritical}),b={critical:o.critical,afterCritical:o.afterCritical,result:m,impact:c};if(!(!B(o.current.client.offset,f)||Boolean(m.combine)))return void t(pt({completed:b}));const h=(({current:e,destination:t,reason:r})=>{const n=N(e,t);if(n<=0)return At;if(n>=1500)return Et;const o=At+wt*(n/1500);return Number(("CANCEL"===r?.6*o:o).toFixed(2))})({current:o.current.client.offset,destination:f,reason:i});t((e=>({type:"DROP_ANIMATE",payload:e}))({newHomeClientOffset:f,dropDuration:h,completed:b}))};var St=()=>({x:window.pageXOffset,y:window.pageYOffset});function Bt({onWindowScroll:e}){const t=(0,d.A)((function(){e(St())})),r=function(e){return{eventName:"scroll",options:{passive:!0,capture:!1},fn:t=>{t.target!==window&&t.target!==window.document||e()}}}(t);let n=g;function o(){return n!==g}return{start:function(){o()&&v(),n=m(window,[r])},stop:function(){o()||v(),t.cancel(),n(),n=g},isActive:o}}const Ot=e=>{const t=Bt({onWindowScroll:t=>{e.dispatch({type:"MOVE_BY_WINDOW_SCROLL",payload:{newScroll:t}})}});return e=>r=>{!t.isActive()&&Qe(r,"INITIAL_PUBLISH")&&t.start(),t.isActive()&&(e=>Qe(e,"DROP_COMPLETE")||Qe(e,"DROP_ANIMATE")||Qe(e,"FLUSH"))(r)&&t.stop(),e(r)}};var Rt=()=>{const e=[];return{add:t=>{const r=setTimeout((()=>(t=>{const r=e.findIndex((e=>e.timerId===t));-1===r&&v();const[n]=e.splice(r,1);n.callback()})(r))),n={timerId:r,callback:t};e.push(n)},flush:()=>{if(!e.length)return;const t=[...e];e.length=0,t.forEach((e=>{clearTimeout(e.timerId),e.callback()}))}}};const Nt=(e,t)=>{je(),t(),Ve()},Pt=(e,t)=>({draggableId:e.draggable.id,type:e.droppable.type,source:{droppableId:e.droppable.id,index:e.draggable.index},mode:t});function Tt(e,t,r,n){if(!e)return void r(n(t));const o=(e=>{let t=!1,r=!1;const n=setTimeout((()=>{r=!0})),o=o=>{t||r||(t=!0,e(o),clearTimeout(n))};return o.wasCalled=()=>t,o})(r);e(t,{announce:o}),o.wasCalled()||r(n(t))}var Lt=(e,t)=>{const r=((e,t)=>{const r=Rt();let n=null;const o=r=>{n||v(),n=null,Nt(0,(()=>Tt(e().onDragEnd,r,t,E.onDragEnd)))};return{beforeCapture:(t,r)=>{n&&v(),Nt(0,(()=>{const n=e().onBeforeCapture;n&&n({draggableId:t,mode:r})}))},beforeStart:(t,r)=>{n&&v(),Nt(0,(()=>{const n=e().onBeforeDragStart;n&&n(Pt(t,r))}))},start:(o,i)=>{n&&v();const a=Pt(o,i);n={mode:i,lastCritical:o,lastLocation:a.source,lastCombine:null},r.add((()=>{Nt(0,(()=>Tt(e().onDragStart,a,t,E.onDragStart)))}))},update:(o,i)=>{const a=j(i),s=V(i);n||v();const l=!((e,t)=>{if(e===t)return!0;const r=e.draggable.id===t.draggable.id&&e.draggable.droppableId===t.draggable.droppableId&&e.draggable.type===t.draggable.type&&e.draggable.index===t.draggable.index,n=e.droppable.id===t.droppable.id&&e.droppable.type===t.droppable.type;return r&&n})(o,n.lastCritical);l&&(n.lastCritical=o);const c=(p=a,!(null==(d=n.lastLocation)&&null==p||null!=d&&null!=p&&d.droppableId===p.droppableId&&d.index===p.index));var d,p;c&&(n.lastLocation=a);const u=!((e,t)=>null==e&&null==t||null!=e&&null!=t&&e.draggableId===t.draggableId&&e.droppableId===t.droppableId)(n.lastCombine,s);if(u&&(n.lastCombine=s),!l&&!c&&!u)return;const g={...Pt(o,n.mode),combine:s,destination:a};r.add((()=>{Nt(0,(()=>Tt(e().onDragUpdate,g,t,E.onDragUpdate)))}))},flush:()=>{n||v(),r.flush()},drop:o,abort:()=>{if(!n)return;const e={...Pt(n.lastCritical,n.mode),combine:null,destination:null,reason:"CANCEL"};o(e)}}})(e,t);return e=>t=>n=>{if(Qe(n,"BEFORE_INITIAL_CAPTURE"))return void r.beforeCapture(n.payload.draggableId,n.payload.movementMode);if(Qe(n,"INITIAL_PUBLISH")){const e=n.payload.critical;return r.beforeStart(e,n.payload.movementMode),t(n),void r.start(e,n.payload.movementMode)}if(Qe(n,"DROP_COMPLETE")){const e=n.payload.completed.result;return r.flush(),t(n),void r.drop(e)}if(t(n),Qe(n,"FLUSH"))return void r.abort();const o=e.getState();"DRAGGING"===o.phase&&r.update(o.critical,o.impact)}};const Gt=e=>t=>r=>{if(!Qe(r,"DROP_ANIMATION_FINISHED"))return void t(r);const n=e.getState();"DROP_ANIMATING"!==n.phase&&v(),e.dispatch(pt({completed:n.completed}))},Mt=e=>{let t=null,r=null;return n=>o=>{if((Qe(o,"FLUSH")||Qe(o,"DROP_COMPLETE")||Qe(o,"DROP_ANIMATION_FINISHED"))&&(r&&(cancelAnimationFrame(r),r=null),t&&(t(),t=null)),n(o),!Qe(o,"DROP_ANIMATE"))return;const i={eventName:"scroll",options:{capture:!0,passive:!1,once:!0},fn:function(){"DROP_ANIMATING"===e.getState().phase&&e.dispatch({type:"DROP_ANIMATION_FINISHED",payload:null})}};r=requestAnimationFrame((()=>{r=null,t=m(window,[i])}))}};var _t=e=>t=>r=>n=>{if((e=>Qe(e,"DROP_COMPLETE")||Qe(e,"DROP_ANIMATE")||Qe(e,"FLUSH"))(n))return e.stop(),void r(n);if(Qe(n,"INITIAL_PUBLISH")){r(n);const o=t.getState();return"DRAGGING"!==o.phase&&v(),void e.start(o)}r(n),e.scroll(t.getState())};const Ft=e=>t=>r=>{if(t(r),!Qe(r,"PUBLISH_WHILE_DRAGGING"))return;const n=e.getState();"DROP_PENDING"===n.phase&&(n.isWaiting||e.dispatch(ut({reason:n.reason})))},kt=i.Zz;var Wt=({dimensionMarshal:e,focusMarshal:t,styleMarshal:r,getResponders:n,announce:o,autoScroller:a})=>{return(0,i.y$)(Xe,kt((0,i.Tw)((s=r,()=>e=>t=>{Qe(t,"INITIAL_PUBLISH")&&s.dragging(),Qe(t,"DROP_ANIMATE")&&s.dropping(t.payload.completed.result.reason),(Qe(t,"FLUSH")||Qe(t,"DROP_COMPLETE"))&&s.resting(),e(t)}),(e=>()=>t=>r=>{(Qe(r,"DROP_COMPLETE")||Qe(r,"FLUSH")||Qe(r,"DROP_ANIMATE"))&&e.stopPublishing(),t(r)})(e),(e=>({getState:t,dispatch:r})=>n=>o=>{if(!Qe(o,"LIFT"))return void n(o);const{id:i,clientSelection:a,movementMode:s}=o.payload,l=t();"DROP_ANIMATING"===l.phase&&r(pt({completed:l.completed})),"IDLE"!==t().phase&&v(),r(dt()),r({type:"BEFORE_INITIAL_CAPTURE",payload:{draggableId:i,movementMode:s}});const c={draggableId:i,scrollOptions:{shouldPublishImmediately:"SNAP"===s}},{critical:d,dimensions:p,viewport:u}=e.startPublishing(c);r({type:"INITIAL_PUBLISH",payload:{critical:d,dimensions:p,clientSelection:a,movementMode:s,viewport:u}})})(e),Ct,Gt,Mt,Ft,_t(a),Ot,(e=>{let t=!1;return()=>r=>n=>{if(Qe(n,"INITIAL_PUBLISH"))return t=!0,e.tryRecordFocus(n.payload.critical.draggable.id),r(n),void e.tryRestoreFocusRecorded();if(r(n),t){if(Qe(n,"FLUSH"))return t=!1,void e.tryRestoreFocusRecorded();if(Qe(n,"DROP_COMPLETE")){t=!1;const r=n.payload.completed.result;r.combine&&e.tryShiftRecord(r.draggableId,r.combine.draggableId),e.tryRestoreFocusRecorded()}}}})(t),Lt(n,o))));var s};var Ut=({scrollHeight:e,scrollWidth:t,height:r,width:n})=>{const o=S({x:t,y:e},{x:n,y:r});return{x:Math.max(0,o.x),y:Math.max(0,o.y)}},$t=()=>{const e=document.documentElement;return e||v(),e},Ht=()=>{const e=$t();return Ut({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,width:e.clientWidth,height:e.clientHeight})},jt=({critical:e,scrollOptions:t,registry:r})=>{je();const n=(()=>{const e=St(),t=Ht(),r=e.y,n=e.x,o=$t(),i=n+o.clientWidth,a=r+o.clientHeight;return{frame:(0,l.l)({top:r,left:n,right:i,bottom:a}),scroll:{initial:e,current:e,max:t,diff:{value:w,displacement:w}}}})(),o=n.scroll.current,i=e.droppable,a=r.droppable.getAllByType(i.type).map((e=>e.callbacks.getDimensionAndWatchScroll(o,t))),s=r.draggable.getAllByType(e.draggable.type).map((e=>e.getDimension(o))),c={draggables:W(s),droppables:k(a)};Ve();return{dimensions:c,critical:e,viewport:n}};function Vt(e,t,r){if(r.descriptor.id===t.id)return!1;if(r.descriptor.type!==t.type)return!1;return"virtual"===e.droppable.getById(r.descriptor.droppableId).descriptor.mode}var Kt=(e,t)=>{let r=null;const n=function({registry:e,callbacks:t}){let r={additions:{},removals:{},modified:{}},n=null;const o=()=>{n||(t.collectionStarting(),n=requestAnimationFrame((()=>{n=null,je();const{additions:o,removals:i,modified:a}=r,s=Object.keys(o).map((t=>e.draggable.getById(t).getDimension(w))).sort(((e,t)=>e.descriptor.index-t.descriptor.index)),l=Object.keys(a).map((t=>({droppableId:t,scroll:e.droppable.getById(t).callbacks.getScrollWhileDragging()}))),c={additions:s,removals:Object.keys(i),modified:l};r={additions:{},removals:{},modified:{}},Ve(),t.publish(c)})))};return{add:e=>{const t=e.descriptor.id;r.additions[t]=e,r.modified[e.descriptor.droppableId]=!0,r.removals[t]&&delete r.removals[t],o()},remove:e=>{const t=e.descriptor;r.removals[t.id]=!0,r.modified[t.droppableId]=!0,r.additions[t.id]&&delete r.additions[t.id],o()},stop:()=>{n&&(cancelAnimationFrame(n),n=null,r={additions:{},removals:{},modified:{}})}}}({callbacks:{publish:t.publishWhileDragging,collectionStarting:t.collectionStarting},registry:e}),o=t=>{r||v();const o=r.critical.draggable;"ADDITION"===t.type&&Vt(e,o,t.value)&&n.add(t.value),"REMOVAL"===t.type&&Vt(e,o,t.value)&&n.remove(t.value)},i={updateDroppableIsEnabled:(n,o)=>{e.droppable.exists(n)||v(),r&&t.updateDroppableIsEnabled({id:n,isEnabled:o})},updateDroppableIsCombineEnabled:(n,o)=>{r&&(e.droppable.exists(n)||v(),t.updateDroppableIsCombineEnabled({id:n,isCombineEnabled:o}))},scrollDroppable:(t,n)=>{r&&e.droppable.getById(t).callbacks.scroll(n)},updateDroppableScroll:(n,o)=>{r&&(e.droppable.exists(n)||v(),t.updateDroppableScroll({id:n,newScroll:o}))},startPublishing:t=>{r&&v();const n=e.draggable.getById(t.draggableId),i=e.droppable.getById(n.descriptor.droppableId),a={draggable:n.descriptor,droppable:i.descriptor},s=e.subscribe(o);return r={critical:a,unsubscribe:s},jt({critical:a,registry:e,scrollOptions:t.scrollOptions})},stopPublishing:()=>{if(!r)return;n.stop();const t=r.critical.droppable;e.droppable.getAllByType(t.type).forEach((e=>e.callbacks.dragStopped())),r.unsubscribe(),r=null}};return i},qt=(e,t)=>"IDLE"===e.phase||"DROP_ANIMATING"===e.phase&&(e.completed.result.draggableId!==t&&"DROP"===e.completed.result.reason),zt=e=>{window.scrollBy(e.x,e.y)};const Yt=(0,c.A)((e=>U(e).filter((e=>!!e.isEnabled&&!!e.frame))));var Jt=({center:e,destination:t,droppables:r})=>{if(t){const e=r[t];return e.frame?e:null}const n=((e,t)=>{const r=Yt(t).find((t=>(t.frame||v(),Pe(t.frame.pageMarginBox)(e))))||null;return r})(e,r);return n};const Xt={startFromPercentage:.25,maxScrollAtPercentage:.05,maxPixelScroll:28,ease:e=>e**2,durationDampening:{stopDampeningAt:1200,accelerateAt:360},disabled:!1};var Qt=({startOfRange:e,endOfRange:t,current:r})=>{const n=t-e;if(0===n)return 0;return(r-e)/n},Zt=({distanceToEdge:e,thresholds:t,dragStartTime:r,shouldUseTimeDampening:n,getAutoScrollerOptions:o})=>{const i=((e,t,r=()=>Xt)=>{const n=r();if(e>t.startScrollingFrom)return 0;if(e<=t.maxScrollValueAt)return n.maxPixelScroll;if(e===t.startScrollingFrom)return 1;const o=1-Qt({startOfRange:t.maxScrollValueAt,endOfRange:t.startScrollingFrom,current:e}),i=n.maxPixelScroll*n.ease(o);return Math.ceil(i)})(e,t,o);return 0===i?0:n?Math.max(((e,t,r)=>{const n=r(),o=n.durationDampening.accelerateAt,i=n.durationDampening.stopDampeningAt,a=t,s=i,l=Date.now()-a;if(l>=i)return e;if(l<o)return 1;const c=Qt({startOfRange:o,endOfRange:s,current:l}),d=e*n.ease(c);return Math.ceil(d)})(i,r,o),1):i},er=({container:e,distanceToEdges:t,dragStartTime:r,axis:n,shouldUseTimeDampening:o,getAutoScrollerOptions:i})=>{const a=((e,t,r=()=>Xt)=>{const n=r();return{startScrollingFrom:e[t.size]*n.startFromPercentage,maxScrollValueAt:e[t.size]*n.maxScrollAtPercentage}})(e,n,i);return t[n.end]<t[n.start]?Zt({distanceToEdge:t[n.end],thresholds:a,dragStartTime:r,shouldUseTimeDampening:o,getAutoScrollerOptions:i}):-1*Zt({distanceToEdge:t[n.start],thresholds:a,dragStartTime:r,shouldUseTimeDampening:o,getAutoScrollerOptions:i})};const tr=T((e=>0===e?0:e));var rr=({dragStartTime:e,container:t,subject:r,center:n,shouldUseTimeDampening:o,getAutoScrollerOptions:i})=>{const a={top:n.y-t.top,right:t.right-n.x,bottom:t.bottom-n.y,left:n.x-t.left},s=er({container:t,distanceToEdges:a,dragStartTime:e,axis:ee,shouldUseTimeDampening:o,getAutoScrollerOptions:i}),l=er({container:t,distanceToEdges:a,dragStartTime:e,axis:te,shouldUseTimeDampening:o,getAutoScrollerOptions:i}),c=tr({x:l,y:s});if(B(c,w))return null;const d=(({container:e,subject:t,proposedScroll:r})=>{const n=t.height>e.height,o=t.width>e.width;return o||n?o&&n?null:{x:o?0:r.x,y:n?0:r.y}:r})({container:t,subject:r,proposedScroll:c});return d?B(d,w)?null:d:null};const nr=T((e=>0===e?0:e>0?1:-1)),or=(()=>{const e=(e,t)=>e<0?e:e>t?e-t:0;return({current:t,max:r,change:n})=>{const o=C(t,n),i={x:e(o.x,r.x),y:e(o.y,r.y)};return B(i,w)?null:i}})(),ir=({max:e,current:t,change:r})=>{const n={x:Math.max(t.x,e.x),y:Math.max(t.y,e.y)},o=nr(r),i=or({max:n,current:t,change:o});return!i||(0!==o.x&&0===i.x||0!==o.y&&0===i.y)},ar=(e,t)=>ir({current:e.scroll.current,max:e.scroll.max,change:t}),sr=(e,t)=>{const r=e.frame;return!!r&&ir({current:r.scroll.current,max:r.scroll.max,change:t})};var lr=({state:e,dragStartTime:t,shouldUseTimeDampening:r,scrollWindow:n,scrollDroppable:o,getAutoScrollerOptions:i})=>{const a=e.current.page.borderBoxCenter,s=e.dimensions.draggables[e.critical.draggable.id].page.marginBox;if(e.isWindowScrollAllowed){const o=(({viewport:e,subject:t,center:r,dragStartTime:n,shouldUseTimeDampening:o,getAutoScrollerOptions:i})=>{const a=rr({dragStartTime:n,container:e.frame,subject:t,center:r,shouldUseTimeDampening:o,getAutoScrollerOptions:i});return a&&ar(e,a)?a:null})({dragStartTime:t,viewport:e.viewport,subject:s,center:a,shouldUseTimeDampening:r,getAutoScrollerOptions:i});if(o)return void n(o)}const l=Jt({center:a,destination:Oe(e.impact),droppables:e.dimensions.droppables});if(!l)return;const c=(({droppable:e,subject:t,center:r,dragStartTime:n,shouldUseTimeDampening:o,getAutoScrollerOptions:i})=>{const a=e.frame;if(!a)return null;const s=rr({dragStartTime:n,container:a.pageMarginBox,subject:t,center:r,shouldUseTimeDampening:o,getAutoScrollerOptions:i});return s&&sr(e,s)?s:null})({dragStartTime:t,droppable:l,subject:s,center:a,shouldUseTimeDampening:r,getAutoScrollerOptions:i});c&&o(l.descriptor.id,c)},cr=({scrollWindow:e,scrollDroppable:t,getAutoScrollerOptions:r=()=>Xt})=>{const n=(0,d.A)(e),o=(0,d.A)(t);let i=null;const a=e=>{i||v();const{shouldUseTimeDampening:t,dragStartTime:a}=i;lr({state:e,scrollWindow:n,scrollDroppable:o,dragStartTime:a,shouldUseTimeDampening:t,getAutoScrollerOptions:r})};return{start:e=>{je(),i&&v();const t=Date.now();let n=!1;const o=()=>{n=!0};lr({state:e,dragStartTime:0,shouldUseTimeDampening:!1,scrollWindow:o,scrollDroppable:o,getAutoScrollerOptions:r}),i={dragStartTime:t,shouldUseTimeDampening:n},Ve(),n&&a(e)},stop:()=>{i&&(n.cancel(),o.cancel(),i=null)},scroll:a}},dr=({move:e,scrollDroppable:t,scrollWindow:r})=>{const n=(e,r)=>{if(!sr(e,r))return r;const n=((e,t)=>{const r=e.frame;return r&&sr(e,t)?or({current:r.scroll.current,max:r.scroll.max,change:t}):null})(e,r);if(!n)return t(e.descriptor.id,r),null;const o=S(r,n);t(e.descriptor.id,o);return S(r,o)},o=(e,t,n)=>{if(!e)return n;if(!ar(t,n))return n;const o=((e,t)=>{if(!ar(e,t))return null;const r=e.scroll.max,n=e.scroll.current;return or({current:n,max:r,change:t})})(t,n);if(!o)return r(n),null;const i=S(n,o);r(i);return S(n,i)};return t=>{const r=t.scrollJumpRequest;if(!r)return;const i=Oe(t.impact);i||v();const a=n(t.dimensions.droppables[i],r);if(!a)return;const s=t.viewport,l=o(t.isWindowScrollAllowed,s,a);l&&((t,r)=>{const n=C(t.current.client.selection,r);e({client:n})})(t,l)}},pr=({scrollDroppable:e,scrollWindow:t,move:r,getAutoScrollerOptions:n})=>{const o=cr({scrollWindow:t,scrollDroppable:e,getAutoScrollerOptions:n}),i=dr({move:r,scrollWindow:t,scrollDroppable:e});return{scroll:e=>{n().disabled||"DRAGGING"!==e.phase||("FLUID"!==e.movementMode?e.scrollJumpRequest&&i(e):o.scroll(e))},start:o.start,stop:o.stop}};const ur="data-rfd",gr=(()=>{const e=`${ur}-drag-handle`;return{base:e,draggableId:`${e}-draggable-id`,contextId:`${e}-context-id`}})(),mr=(()=>{const e=`${ur}-draggable`;return{base:e,contextId:`${e}-context-id`,id:`${e}-id`}})(),fr=(()=>{const e=`${ur}-droppable`;return{base:e,contextId:`${e}-context-id`,id:`${e}-id`}})(),br={contextId:`${ur}-scroll-container-context-id`},hr=(e,t)=>e.map((e=>{const r=e.styles[t];return r?`${e.selector} { ${r} }`:""})).join(" ");var vr=e=>{const t=(r=e,e=>`[${e}="${r}"]`);var r;const n=(()=>{const e="\n      cursor: -webkit-grab;\n      cursor: grab;\n    ";return{selector:t(gr.contextId),styles:{always:"\n          -webkit-touch-callout: none;\n          -webkit-tap-highlight-color: rgba(0,0,0,0);\n          touch-action: manipulation;\n        ",resting:e,dragging:"pointer-events: none;",dropAnimating:e}}})(),o=[(()=>{const e=`\n      transition: ${It.outOfTheWay};\n    `;return{selector:t(mr.contextId),styles:{dragging:e,dropAnimating:e,userCancel:e}}})(),n,{selector:t(fr.contextId),styles:{always:"overflow-anchor: none;"}},{selector:"body",styles:{dragging:"\n        cursor: grabbing;\n        cursor: -webkit-grabbing;\n        user-select: none;\n        -webkit-user-select: none;\n        -moz-user-select: none;\n        -ms-user-select: none;\n        overflow-anchor: none;\n      "}}];return{always:hr(o,"always"),resting:hr(o,"resting"),dragging:hr(o,"dragging"),dropAnimating:hr(o,"dropAnimating"),userCancel:hr(o,"userCancel")}};const Ir="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?n.useLayoutEffect:n.useEffect,yr=()=>{const e=document.querySelector("head");return e||v(),e},Dr=e=>{const t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.type="text/css",t};function xr(e,t){return Array.from(e.querySelectorAll(t))}var Ar=e=>e&&e.ownerDocument&&e.ownerDocument.defaultView?e.ownerDocument.defaultView:window;function Er(e){return e instanceof Ar(e).HTMLElement}function wr(e,t){const r=`[${gr.contextId}="${e}"]`,n=xr(document,r);if(!n.length)return null;const o=n.find((e=>e.getAttribute(gr.draggableId)===t));return o&&Er(o)?o:null}function Cr(){const e={draggables:{},droppables:{}},t=[];function r(e){t.length&&t.forEach((t=>t(e)))}function n(t){return e.draggables[t]||null}function o(t){return e.droppables[t]||null}return{draggable:{register:t=>{e.draggables[t.descriptor.id]=t,r({type:"ADDITION",value:t})},update:(t,r)=>{const n=e.draggables[r.descriptor.id];n&&n.uniqueId===t.uniqueId&&(delete e.draggables[r.descriptor.id],e.draggables[t.descriptor.id]=t)},unregister:t=>{const o=t.descriptor.id,i=n(o);i&&t.uniqueId===i.uniqueId&&(delete e.draggables[o],e.droppables[t.descriptor.droppableId]&&r({type:"REMOVAL",value:t}))},getById:function(e){const t=n(e);return t||v(),t},findById:n,exists:e=>Boolean(n(e)),getAllByType:t=>Object.values(e.draggables).filter((e=>e.descriptor.type===t))},droppable:{register:t=>{e.droppables[t.descriptor.id]=t},unregister:t=>{const r=o(t.descriptor.id);r&&t.uniqueId===r.uniqueId&&delete e.droppables[t.descriptor.id]},getById:function(e){const t=o(e);return t||v(),t},findById:o,exists:e=>Boolean(o(e)),getAllByType:t=>Object.values(e.droppables).filter((e=>e.descriptor.type===t))},subscribe:function(e){return t.push(e),function(){const r=t.indexOf(e);-1!==r&&t.splice(r,1)}},clean:function(){e.draggables={},e.droppables={},t.length=0}}}var Sr=n.createContext(null),Br=()=>{const e=document.body;return e||v(),e};const Or={position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0 0 0 0)","clip-path":"inset(100%)"},Rr=e=>`rfd-announcement-${e}`;const Nr={separator:"::"};function Pr(e,t=Nr){const r=n.useId();return(0,s.Kr)((()=>`${e}${t.separator}${r}`),[t.separator,e,r])}var Tr=n.createContext(null);function Lr(e){0}function Gr(e,t){Lr()}function Mr(e){const t=(0,n.useRef)(e);return(0,n.useEffect)((()=>{t.current=e})),t}function _r(e){return"IDLE"!==e.phase&&"DROP_ANIMATING"!==e.phase&&e.isDragging}const Fr=9,kr=13,Wr=33,Ur=34,$r=35,Hr=36,jr={[kr]:!0,[Fr]:!0};var Vr=e=>{jr[e.keyCode]&&e.preventDefault()};const Kr=(()=>{const e="visibilitychange";if("undefined"==typeof document)return e;return[e,`ms${e}`,`webkit${e}`,`moz${e}`,`o${e}`].find((e=>`on${e}`in document))||e})();const qr={type:"IDLE"};function zr({cancel:e,completed:t,getPhase:r,setPhase:n}){return[{eventName:"mousemove",fn:e=>{const{button:t,clientX:o,clientY:i}=e;if(0!==t)return;const a={x:o,y:i},s=r();if("DRAGGING"===s.type)return e.preventDefault(),void s.actions.move(a);"PENDING"!==s.type&&v();const l=s.point;if(c=l,d=a,!(Math.abs(d.x-c.x)>=5||Math.abs(d.y-c.y)>=5))return;var c,d;e.preventDefault();const p=s.actions.fluidLift(a);n({type:"DRAGGING",actions:p})}},{eventName:"mouseup",fn:n=>{const o=r();"DRAGGING"===o.type?(n.preventDefault(),o.actions.drop({shouldBlockNextClick:!0}),t()):e()}},{eventName:"mousedown",fn:t=>{"DRAGGING"===r().type&&t.preventDefault(),e()}},{eventName:"keydown",fn:t=>{if("PENDING"!==r().type)return 27===t.keyCode?(t.preventDefault(),void e()):void Vr(t);e()}},{eventName:"resize",fn:e},{eventName:"scroll",options:{passive:!0,capture:!1},fn:()=>{"PENDING"===r().type&&e()}},{eventName:"webkitmouseforcedown",fn:t=>{const n=r();"IDLE"===n.type&&v(),n.actions.shouldRespectForcePress()?e():t.preventDefault()}},{eventName:Kr,fn:e}]}function Yr(){}const Jr={[Ur]:!0,[Wr]:!0,[Hr]:!0,[$r]:!0};function Xr(e,t){function r(){t(),e.cancel()}return[{eventName:"keydown",fn:n=>27===n.keyCode?(n.preventDefault(),void r()):32===n.keyCode?(n.preventDefault(),t(),void e.drop()):40===n.keyCode?(n.preventDefault(),void e.moveDown()):38===n.keyCode?(n.preventDefault(),void e.moveUp()):39===n.keyCode?(n.preventDefault(),void e.moveRight()):37===n.keyCode?(n.preventDefault(),void e.moveLeft()):void(Jr[n.keyCode]?n.preventDefault():Vr(n))},{eventName:"mousedown",fn:r},{eventName:"mouseup",fn:r},{eventName:"click",fn:r},{eventName:"touchstart",fn:r},{eventName:"resize",fn:r},{eventName:"wheel",fn:r,options:{passive:!0}},{eventName:Kr,fn:r}]}const Qr={type:"IDLE"};const Zr=["input","button","textarea","select","option","optgroup","video","audio"];function en(e,t){if(null==t)return!1;if(Zr.includes(t.tagName.toLowerCase()))return!0;const r=t.getAttribute("contenteditable");return"true"===r||""===r||t!==e&&en(e,t.parentElement)}function tn(e,t){const r=t.target;return!!Er(r)&&en(e,r)}var rn=e=>(0,l.l)(e.getBoundingClientRect()).center;const nn=(()=>{const e="matches";if("undefined"==typeof document)return e;return[e,"msMatchesSelector","webkitMatchesSelector"].find((e=>e in Element.prototype))||e})();function on(e,t){return null==e?null:e[nn](t)?e:on(e.parentElement,t)}function an(e,t){return e.closest?e.closest(t):on(e,t)}function sn(e,t){const r=t.target;if(!((n=r)instanceof Ar(n).Element))return null;var n;const o=function(e){return`[${gr.contextId}="${e}"]`}(e),i=an(r,o);return i&&Er(i)?i:null}function ln(e){e.preventDefault()}function cn({expected:e,phase:t,isLockActive:r,shouldWarn:n}){return!!r()&&e===t}function dn({lockAPI:e,store:t,registry:r,draggableId:n}){if(e.isClaimed())return!1;const o=r.draggable.findById(n);return!!o&&(!!o.options.isEnabled&&!!qt(t.getState(),n))}function pn({lockAPI:e,contextId:t,store:r,registry:n,draggableId:o,forceSensorStop:i,sourceEvent:a}){if(!dn({lockAPI:e,store:r,registry:n,draggableId:o}))return null;const s=n.draggable.getById(o),l=function(e,t){const r=`[${mr.contextId}="${e}"]`,n=xr(document,r).find((e=>e.getAttribute(mr.id)===t));return n&&Er(n)?n:null}(t,s.descriptor.id);if(!l)return null;if(a&&!s.options.canDragInteractiveElements&&tn(l,a))return null;const c=e.claim(i||g);let p="PRE_DRAG";function u(){return s.options.shouldRespectForcePress}function f(){return e.isActive(c)}const b=function(e,t){cn({expected:e,phase:p,isLockActive:f,shouldWarn:!0})&&r.dispatch(t())}.bind(null,"DRAGGING");function h(t){function n(){e.release(),p="COMPLETED"}function o(e,o={shouldBlockNextClick:!1}){if(t.cleanup(),o.shouldBlockNextClick){const e=m(window,[{eventName:"click",fn:ln,options:{once:!0,passive:!1,capture:!0}}]);setTimeout(e)}n(),r.dispatch(ut({reason:e}))}return"PRE_DRAG"!==p&&(n(),v()),r.dispatch(Ze(t.liftActionArgs)),p="DRAGGING",{isActive:()=>cn({expected:"DRAGGING",phase:p,isLockActive:f,shouldWarn:!1}),shouldRespectForcePress:u,drop:e=>o("DROP",e),cancel:e=>o("CANCEL",e),...t.actions}}return{isActive:()=>cn({expected:"PRE_DRAG",phase:p,isLockActive:f,shouldWarn:!1}),shouldRespectForcePress:u,fluidLift:function(e){const t=(0,d.A)((e=>{b((()=>it({client:e})))}));return{...h({liftActionArgs:{id:o,clientSelection:e,movementMode:"FLUID"},cleanup:()=>t.cancel(),actions:{move:t}}),move:t}},snapLift:function(){const e={moveUp:()=>b(at),moveRight:()=>b(lt),moveDown:()=>b(st),moveLeft:()=>b(ct)};return h({liftActionArgs:{id:o,clientSelection:rn(l),movementMode:"SNAP"},cleanup:g,actions:e})},abort:function(){cn({expected:"PRE_DRAG",phase:p,isLockActive:f,shouldWarn:!0})&&e.release()}}}const un=[function(e){const t=(0,n.useRef)(qr),r=(0,n.useRef)(g),o=(0,s.Kr)((()=>({eventName:"mousedown",fn:function(t){if(t.defaultPrevented)return;if(0!==t.button)return;if(t.ctrlKey||t.metaKey||t.shiftKey||t.altKey)return;const n=e.findClosestDraggableId(t);if(!n)return;const o=e.tryGetLock(n,l,{sourceEvent:t});if(!o)return;t.preventDefault();const i={x:t.clientX,y:t.clientY};r.current(),p(o,i)}})),[e]),i=(0,s.Kr)((()=>({eventName:"webkitmouseforcewillbegin",fn:t=>{if(t.defaultPrevented)return;const r=e.findClosestDraggableId(t);if(!r)return;const n=e.findOptionsForDraggable(r);n&&(n.shouldRespectForcePress||e.canGetLock(r)&&t.preventDefault())}})),[e]),a=(0,s.hb)((function(){r.current=m(window,[i,o],{passive:!1,capture:!0})}),[i,o]),l=(0,s.hb)((()=>{"IDLE"!==t.current.type&&(t.current=qr,r.current(),a())}),[a]),c=(0,s.hb)((()=>{const e=t.current;l(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()}),[l]),d=(0,s.hb)((function(){const e=zr({cancel:c,completed:l,getPhase:()=>t.current,setPhase:e=>{t.current=e}});r.current=m(window,e,{capture:!0,passive:!1})}),[c,l]),p=(0,s.hb)((function(e,r){"IDLE"!==t.current.type&&v(),t.current={type:"PENDING",point:r,actions:e},d()}),[d]);Ir((function(){return a(),function(){r.current()}}),[a])},function(e){const t=(0,n.useRef)(Yr),r=(0,s.Kr)((()=>({eventName:"keydown",fn:function(r){if(r.defaultPrevented)return;if(32!==r.keyCode)return;const n=e.findClosestDraggableId(r);if(!n)return;const i=e.tryGetLock(n,l,{sourceEvent:r});if(!i)return;r.preventDefault();let a=!0;const s=i.snapLift();function l(){a||v(),a=!1,t.current(),o()}t.current(),t.current=m(window,Xr(s,l),{capture:!0,passive:!1})}})),[e]),o=(0,s.hb)((function(){t.current=m(window,[r],{passive:!1,capture:!0})}),[r]);Ir((function(){return o(),function(){t.current()}}),[o])},function(e){const t=(0,n.useRef)(Qr),r=(0,n.useRef)(g),o=(0,s.hb)((function(){return t.current}),[]),i=(0,s.hb)((function(e){t.current=e}),[]),a=(0,s.Kr)((()=>({eventName:"touchstart",fn:function(t){if(t.defaultPrevented)return;const n=e.findClosestDraggableId(t);if(!n)return;const o=e.tryGetLock(n,c,{sourceEvent:t});if(!o)return;const i=t.touches[0],{clientX:a,clientY:s}=i,l={x:a,y:s};r.current(),f(o,l)}})),[e]),l=(0,s.hb)((function(){r.current=m(window,[a],{capture:!0,passive:!1})}),[a]),c=(0,s.hb)((()=>{const e=t.current;"IDLE"!==e.type&&("PENDING"===e.type&&clearTimeout(e.longPressTimerId),i(Qr),r.current(),l())}),[l,i]),d=(0,s.hb)((()=>{const e=t.current;c(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()}),[c]),p=(0,s.hb)((function(){const e={capture:!0,passive:!1},t={cancel:d,completed:c,getPhase:o},n=m(window,function({cancel:e,completed:t,getPhase:r}){return[{eventName:"touchmove",options:{capture:!1},fn:t=>{const n=r();if("DRAGGING"!==n.type)return void e();n.hasMoved=!0;const{clientX:o,clientY:i}=t.touches[0],a={x:o,y:i};t.preventDefault(),n.actions.move(a)}},{eventName:"touchend",fn:n=>{const o=r();"DRAGGING"===o.type?(n.preventDefault(),o.actions.drop({shouldBlockNextClick:!0}),t()):e()}},{eventName:"touchcancel",fn:t=>{"DRAGGING"===r().type?(t.preventDefault(),e()):e()}},{eventName:"touchforcechange",fn:t=>{const n=r();"IDLE"===n.type&&v();const o=t.touches[0];if(!o)return;if(!(o.force>=.15))return;const i=n.actions.shouldRespectForcePress();if("PENDING"!==n.type)return i?n.hasMoved?void t.preventDefault():void e():void t.preventDefault();i&&e()}},{eventName:Kr,fn:e}]}(t),e),i=m(window,function({cancel:e,getPhase:t}){return[{eventName:"orientationchange",fn:e},{eventName:"resize",fn:e},{eventName:"contextmenu",fn:e=>{e.preventDefault()}},{eventName:"keydown",fn:r=>{"DRAGGING"===t().type?(27===r.keyCode&&r.preventDefault(),e()):e()}},{eventName:Kr,fn:e}]}(t),e);r.current=function(){n(),i()}}),[d,o,c]),u=(0,s.hb)((function(){const e=o();"PENDING"!==e.type&&v();const t=e.actions.fluidLift(e.point);i({type:"DRAGGING",actions:t,hasMoved:!1})}),[o,i]),f=(0,s.hb)((function(e,t){"IDLE"!==o().type&&v();const r=setTimeout(u,120);i({type:"PENDING",point:t,actions:e,longPressTimerId:r}),p()}),[p,o,i,u]);Ir((function(){return l(),function(){r.current();const e=o();"PENDING"===e.type&&(clearTimeout(e.longPressTimerId),i(Qr))}}),[o,l,i]),Ir((function(){return m(window,[{eventName:"touchmove",fn:()=>{},options:{capture:!1,passive:!1}}])}),[])}];function gn({contextId:e,store:t,registry:r,customSensors:o,enableDefaultSensors:i}){const a=[...i?un:[],...o||[]],l=(0,n.useState)((()=>function(){let e=null;function t(){e||v(),e=null}return{isClaimed:function(){return Boolean(e)},isActive:function(t){return t===e},claim:function(t){e&&v();const r={abandon:t};return e=r,r},release:t,tryAbandon:function(){e&&(e.abandon(),t())}}}()))[0],c=(0,s.hb)((function(e,t){_r(e)&&!_r(t)&&l.tryAbandon()}),[l]);Ir((function(){let e=t.getState();return t.subscribe((()=>{const r=t.getState();c(e,r),e=r}))}),[l,t,c]),Ir((()=>l.tryAbandon),[l.tryAbandon]);const d=(0,s.hb)((e=>dn({lockAPI:l,registry:r,store:t,draggableId:e})),[l,r,t]),p=(0,s.hb)(((n,o,i)=>pn({lockAPI:l,registry:r,contextId:e,store:t,draggableId:n,forceSensorStop:o||null,sourceEvent:i&&i.sourceEvent?i.sourceEvent:null})),[e,l,r,t]),u=(0,s.hb)((t=>function(e,t){const r=sn(e,t);return r?r.getAttribute(gr.draggableId):null}(e,t)),[e]),g=(0,s.hb)((e=>{const t=r.draggable.findById(e);return t?t.options:null}),[r.draggable]),m=(0,s.hb)((function(){l.isClaimed()&&(l.tryAbandon(),"IDLE"!==t.getState().phase&&t.dispatch(dt()))}),[l,t]),f=(0,s.hb)((()=>l.isClaimed()),[l]),b=(0,s.Kr)((()=>({canGetLock:d,tryGetLock:p,findClosestDraggableId:u,findOptionsForDraggable:g,tryReleaseLock:m,isLockClaimed:f})),[d,p,u,g,m,f]);Lr();for(let n=0;n<a.length;n++)a[n](b)}const mn=e=>({onBeforeCapture:t=>{(0,o.flushSync)((()=>{e.onBeforeCapture&&e.onBeforeCapture(t)}))},onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragEnd:e.onDragEnd,onDragUpdate:e.onDragUpdate}),fn=e=>({...Xt,...e.autoScrollerOptions,durationDampening:{...Xt.durationDampening,...e.autoScrollerOptions}});function bn(e){return e.current||v(),e.current}function hn(e){const{contextId:t,setCallbacks:r,sensors:o,nonce:l,dragHandleUsageInstructions:d}=e,u=(0,n.useRef)(null);Gr();const g=Mr(e),m=(0,s.hb)((()=>mn(g.current)),[g]),f=(0,s.hb)((()=>fn(g.current)),[g]),b=function(e){const t=(0,s.Kr)((()=>Rr(e)),[e]),r=(0,n.useRef)(null);return(0,n.useEffect)((function(){const e=document.createElement("div");return r.current=e,e.id=t,e.setAttribute("aria-live","assertive"),e.setAttribute("aria-atomic","true"),(0,p.A)(e.style,Or),Br().appendChild(e),function(){setTimeout((function(){const t=Br();t.contains(e)&&t.removeChild(e),e===r.current&&(r.current=null)}))}}),[t]),(0,s.hb)((e=>{const t=r.current;t&&(t.textContent=e)}),[])}(t),h=function({contextId:e,text:t}){const r=Pr("hidden-text",{separator:"-"}),o=(0,s.Kr)((()=>function({contextId:e,uniqueId:t}){return`rfd-hidden-text-${e}-${t}`}({contextId:e,uniqueId:r})),[r,e]);return(0,n.useEffect)((function(){const e=document.createElement("div");return e.id=o,e.textContent=t,e.style.display="none",Br().appendChild(e),function(){const t=Br();t.contains(e)&&t.removeChild(e)}}),[o,t]),o}({contextId:t,text:d}),I=function(e,t){const r=(0,s.Kr)((()=>vr(e)),[e]),o=(0,n.useRef)(null),i=(0,n.useRef)(null),a=(0,s.hb)((0,c.A)((e=>{const t=i.current;t||v(),t.textContent=e})),[]),l=(0,s.hb)((e=>{const t=o.current;t||v(),t.textContent=e}),[]);Ir((()=>{(o.current||i.current)&&v();const n=Dr(t),s=Dr(t);return o.current=n,i.current=s,n.setAttribute(`${ur}-always`,e),s.setAttribute(`${ur}-dynamic`,e),yr().appendChild(n),yr().appendChild(s),l(r.always),a(r.resting),()=>{const e=e=>{const t=e.current;t||v(),yr().removeChild(t),e.current=null};e(o),e(i)}}),[t,l,a,r.always,r.resting,e]);const d=(0,s.hb)((()=>a(r.dragging)),[a,r.dragging]),p=(0,s.hb)((e=>{a("DROP"!==e?r.userCancel:r.dropAnimating)}),[a,r.dropAnimating,r.userCancel]),u=(0,s.hb)((()=>{i.current&&a(r.resting)}),[a,r.resting]);return(0,s.Kr)((()=>({dragging:d,dropping:p,resting:u})),[d,p,u])}(t,l),y=(0,s.hb)((e=>{bn(u).dispatch(e)}),[]),D=(0,s.Kr)((()=>(0,i.zH)({publishWhileDragging:et,updateDroppableScroll:rt,updateDroppableIsEnabled:nt,updateDroppableIsCombineEnabled:ot,collectionStarting:tt},y)),[y]),x=function(){const e=(0,s.Kr)(Cr,[]);return(0,n.useEffect)((()=>function(){e.clean()}),[e]),e}(),A=(0,s.Kr)((()=>Kt(x,D)),[x,D]),E=(0,s.Kr)((()=>pr({scrollWindow:zt,scrollDroppable:A.scrollDroppable,getAutoScrollerOptions:f,...(0,i.zH)({move:it},y)})),[A.scrollDroppable,y,f]),w=function(e){const t=(0,n.useRef)({}),r=(0,n.useRef)(null),o=(0,n.useRef)(null),i=(0,n.useRef)(!1),a=(0,s.hb)((function(e,r){const n={id:e,focus:r};return t.current[e]=n,function(){const r=t.current;r[e]!==n&&delete r[e]}}),[]),l=(0,s.hb)((function(t){const r=wr(e,t);r&&r!==document.activeElement&&r.focus()}),[e]),c=(0,s.hb)((function(e,t){r.current===e&&(r.current=t)}),[]),d=(0,s.hb)((function(){o.current||i.current&&(o.current=requestAnimationFrame((()=>{o.current=null;const e=r.current;e&&l(e)})))}),[l]),p=(0,s.hb)((function(e){r.current=null;const t=document.activeElement;t&&t.getAttribute(gr.draggableId)===e&&(r.current=e)}),[]);return Ir((()=>(i.current=!0,function(){i.current=!1;const e=o.current;e&&cancelAnimationFrame(e)})),[]),(0,s.Kr)((()=>({register:a,tryRecordFocus:p,tryRestoreFocusRecorded:d,tryShiftRecord:c})),[a,p,d,c])}(t),C=(0,s.Kr)((()=>Wt({announce:b,autoScroller:E,dimensionMarshal:A,focusMarshal:w,getResponders:m,styleMarshal:I})),[b,E,A,w,m,I]);u.current=C;const S=(0,s.hb)((()=>{const e=bn(u);"IDLE"!==e.getState().phase&&e.dispatch(dt())}),[]),B=(0,s.hb)((()=>{const e=bn(u).getState();return"DROP_ANIMATING"===e.phase||"IDLE"!==e.phase&&e.isDragging}),[]);r((0,s.Kr)((()=>({isDragging:B,tryAbort:S})),[B,S]));const O=(0,s.hb)((e=>qt(bn(u).getState(),e)),[]),R=(0,s.hb)((()=>Ne(bn(u).getState())),[]),N=(0,s.Kr)((()=>({marshal:A,focus:w,contextId:t,canLift:O,isMovementAllowed:R,dragHandleUsageInstructionsId:h,registry:x})),[t,A,h,w,O,R,x]);return gn({contextId:t,store:C,registry:x,customSensors:o||null,enableDefaultSensors:!1!==e.enableDefaultSensors}),(0,n.useEffect)((()=>S),[S]),n.createElement(Tr.Provider,{value:N},n.createElement(a.Kq,{context:Sr,store:C},e.children))}function vn(e){const t=n.useId(),r=e.dragHandleUsageInstructions||E.dragHandleUsageInstructions;return n.createElement(I,null,(o=>n.createElement(hn,{nonce:e.nonce,contextId:t,setCallbacks:o,dragHandleUsageInstructions:r,enableDefaultSensors:e.enableDefaultSensors,sensors:e.sensors,onBeforeCapture:e.onBeforeCapture,onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragUpdate:e.onDragUpdate,onDragEnd:e.onDragEnd,autoScrollerOptions:e.autoScrollerOptions},e.children)))}const In=5e3,yn=4500,Dn=(e,t)=>t?It.drop(t.duration):e?It.snap:It.fluid,xn=(e,t)=>{if(e)return t?ft.drop:ft.combining};function An(e){return"DRAGGING"===e.type?function(e){const t=e.dimension.client,{offset:r,combineWith:n,dropping:o}=e,i=Boolean(n),a=(e=>null!=e.forceShouldAnimate?e.forceShouldAnimate:"SNAP"===e.mode)(e),s=Boolean(o),l=s?xt(r,i):Dt(r);return{position:"fixed",top:t.marginBox.top,left:t.marginBox.left,boxSizing:"border-box",width:t.borderBox.width,height:t.borderBox.height,transition:Dn(a,o),transform:l,opacity:xn(i,s),zIndex:s?yn:In,pointerEvents:"none"}}(e):{transform:Dt((t=e).offset),transition:t.shouldAnimateDisplacement?void 0:"none"};var t}function En(e){const t=Pr("draggable"),{descriptor:r,registry:o,getDraggableRef:i,canDragInteractiveElements:a,shouldRespectForcePress:c,isEnabled:d}=e,p=(0,s.Kr)((()=>({canDragInteractiveElements:a,shouldRespectForcePress:c,isEnabled:d})),[a,d,c]),u=(0,s.hb)((e=>{const t=i();return t||v(),function(e,t,r=w){const n=window.getComputedStyle(t),o=t.getBoundingClientRect(),i=(0,l.a)(o,n),a=(0,l.SQ)(i,r);return{descriptor:e,placeholder:{client:i,tagName:t.tagName.toLowerCase(),display:n.display},displaceBy:{x:i.marginBox.width,y:i.marginBox.height},client:i,page:a}}(r,t,e)}),[r,i]),g=(0,s.Kr)((()=>({uniqueId:t,descriptor:r,options:p,getDimension:u})),[r,u,p,t]),m=(0,n.useRef)(g),f=(0,n.useRef)(!0);Ir((()=>(o.draggable.register(m.current),()=>o.draggable.unregister(m.current))),[o.draggable]),Ir((()=>{if(f.current)return void(f.current=!1);const e=m.current;m.current=g,o.draggable.update(g,e)}),[g,o.draggable])}var wn=n.createContext(null);function Cn(e){const t=(0,n.useContext)(e);return t||v(),t}function Sn(e){e.preventDefault()}var Bn=(e,t)=>e===t,On=e=>{const{combine:t,destination:r}=e;return r?r.droppableId:t?t.droppableId:null};function Rn(e=null){return{isDragging:!1,isDropAnimating:!1,isClone:!1,dropAnimation:null,mode:null,draggingOver:null,combineTargetFor:e,combineWith:null}}const Nn={mapped:{type:"SECONDARY",offset:w,combineTargetFor:null,shouldAnimateDisplacement:!0,snapshot:Rn(null)}};const Pn={dropAnimationFinished:gt},Tn=(0,a.Ng)((()=>{const e=function(){const e=(0,c.A)(((e,t)=>({x:e,y:t}))),t=(0,c.A)(((e,t,r=null,n=null,o=null)=>({isDragging:!0,isClone:t,isDropAnimating:Boolean(o),dropAnimation:o,mode:e,draggingOver:r,combineWith:n,combineTargetFor:null}))),r=(0,c.A)(((e,r,n,o,i=null,a=null,s=null)=>({mapped:{type:"DRAGGING",dropping:null,draggingOver:i,combineWith:a,mode:r,offset:e,dimension:n,forceShouldAnimate:s,snapshot:t(r,o,i,a,null)}})));return(n,o)=>{if(_r(n)){if(n.critical.draggable.id!==o.draggableId)return null;const t=n.current.client.offset,a=n.dimensions.draggables[o.draggableId],s=Oe(n.impact),l=(i=n.impact).at&&"COMBINE"===i.at.type?i.at.combine.draggableId:null,c=n.forceShouldAnimate;return r(e(t.x,t.y),n.movementMode,a,o.isClone,s,l,c)}var i;if("DROP_ANIMATING"===n.phase){const e=n.completed;if(e.result.draggableId!==o.draggableId)return null;const r=o.isClone,i=n.dimensions.draggables[o.draggableId],a=e.result,s=a.mode,l=On(a),c=(e=>e.combine?e.combine.draggableId:null)(a),d={duration:n.dropDuration,curve:mt,moveTo:n.newHomeClientOffset,opacity:c?ft.drop:null,scale:c?bt.drop:null};return{mapped:{type:"DRAGGING",offset:n.newHomeClientOffset,dimension:i,dropping:d,draggingOver:l,combineWith:c,mode:s,forceShouldAnimate:null,snapshot:t(s,r,l,c,d)}}}return null}}(),t=function(){const e=(0,c.A)(((e,t)=>({x:e,y:t}))),t=(0,c.A)(Rn),r=(0,c.A)(((e,r=null,n)=>({mapped:{type:"SECONDARY",offset:e,combineTargetFor:r,shouldAnimateDisplacement:n,snapshot:t(r)}}))),n=e=>e?r(w,e,!0):null,o=(t,o,i,a)=>{const s=i.displaced.visible[t],l=Boolean(a.inVirtualList&&a.effected[t]),c=V(i),d=c&&c.draggableId===t?o:null;if(!s){if(!l)return n(d);if(i.displaced.invisible[t])return null;const o=O(a.displacedBy.point),s=e(o.x,o.y);return r(s,d,!0)}if(l)return n(d);const p=i.displacedBy.point,u=e(p.x,p.y);return r(u,d,s.shouldAnimate)};return(e,t)=>{if(_r(e))return e.critical.draggable.id===t.draggableId?null:o(t.draggableId,e.critical.draggable.id,e.impact,e.afterCritical);if("DROP_ANIMATING"===e.phase){const r=e.completed;return r.result.draggableId===t.draggableId?null:o(t.draggableId,r.result.draggableId,r.impact,r.afterCritical)}return null}}();return(r,n)=>e(r,n)||t(r,n)||Nn}),Pn,null,{context:Sr,areStatePropsEqual:Bn})((e=>{const t=(0,n.useRef)(null),r=(0,s.hb)(((e=null)=>{t.current=e}),[]),i=(0,s.hb)((()=>t.current),[]),{contextId:a,dragHandleUsageInstructionsId:l,registry:c}=Cn(Tr),{type:d,droppableId:p}=Cn(wn),u=(0,s.Kr)((()=>({id:e.draggableId,index:e.index,type:d,droppableId:p})),[e.draggableId,e.index,d,p]),{children:g,draggableId:m,isEnabled:f,shouldRespectForcePress:b,canDragInteractiveElements:h,isClone:v,mapped:I,dropAnimationFinished:y}=e;if(Gr(),Lr(),!v){En((0,s.Kr)((()=>({descriptor:u,registry:c,getDraggableRef:i,canDragInteractiveElements:h,shouldRespectForcePress:b,isEnabled:f})),[u,c,i,h,b,f]))}const D=(0,s.Kr)((()=>f?{tabIndex:0,role:"button","aria-describedby":l,"data-rfd-drag-handle-draggable-id":m,"data-rfd-drag-handle-context-id":a,draggable:!1,onDragStart:Sn}:null),[a,l,m,f]),x=(0,s.hb)((e=>{"DRAGGING"===I.type&&I.dropping&&"transform"===e.propertyName&&(0,o.flushSync)(y)}),[y,I]),A=(0,s.Kr)((()=>{const e=An(I),t="DRAGGING"===I.type&&I.dropping?x:void 0;return{innerRef:r,draggableProps:{"data-rfd-draggable-context-id":a,"data-rfd-draggable-id":m,style:e,onTransitionEnd:t},dragHandleProps:D}}),[a,D,m,I,x,r]),E=(0,s.Kr)((()=>({draggableId:u.id,type:u.type,source:{index:u.index,droppableId:u.droppableId}})),[u.droppableId,u.id,u.index,u.type]);return n.createElement(n.Fragment,null,g(A,I.snapshot,E))}));function Ln(e){return Cn(wn).isUsingCloneFor!==e.draggableId||e.isClone?n.createElement(Tn,e):null}function Gn(e){const t="boolean"!=typeof e.isDragDisabled||!e.isDragDisabled,r=Boolean(e.disableInteractiveElementBlocking),o=Boolean(e.shouldRespectForcePress);return n.createElement(Ln,(0,p.A)({},e,{isClone:!1,isEnabled:t,canDragInteractiveElements:r,shouldRespectForcePress:o}))}const Mn=e=>t=>e===t,_n=Mn("scroll"),Fn=Mn("auto"),kn=(Mn("visible"),(e,t)=>t(e.overflowX)||t(e.overflowY)),Wn=e=>{const t=window.getComputedStyle(e),r={overflowX:t.overflowX,overflowY:t.overflowY};return kn(r,_n)||kn(r,Fn)},Un=e=>null==e||e===document.body||e===document.documentElement?null:Wn(e)?e:Un(e.parentElement);var $n=e=>({x:e.scrollLeft,y:e.scrollTop});const Hn=e=>{if(!e)return!1;return"fixed"===window.getComputedStyle(e).position||Hn(e.parentElement)};var jn=({ref:e,descriptor:t,env:r,windowScroll:n,direction:o,isDropDisabled:i,isCombineEnabled:a,shouldClipSubject:s})=>{const c=r.closestScrollable,d=((e,t)=>{const r=(0,l.YH)(e);if(!t)return r;if(e!==t)return r;const n=r.paddingBox.top-t.scrollTop,o=r.paddingBox.left-t.scrollLeft,i=n+t.scrollHeight,a={top:n,right:o+t.scrollWidth,bottom:i,left:o},s=(0,l.fT)(a,r.border);return(0,l.ge)({borderBox:s,margin:r.margin,border:r.border,padding:r.padding})})(e,c),p=(0,l.SQ)(d,n),u=(()=>{if(!c)return null;const e=(0,l.YH)(c),t={scrollHeight:c.scrollHeight,scrollWidth:c.scrollWidth};return{client:e,page:(0,l.SQ)(e,n),scroll:$n(c),scrollSize:t,shouldClipSubject:s}})(),g=(({descriptor:e,isEnabled:t,isCombineEnabled:r,isFixedOnPage:n,direction:o,client:i,page:a,closest:s})=>{const l=(()=>{if(!s)return null;const{scrollSize:e,client:t}=s,r=Ut({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,height:t.paddingBox.height,width:t.paddingBox.width});return{pageMarginBox:s.page.marginBox,frameClient:t,scrollSize:e,shouldClipSubject:s.shouldClipSubject,scroll:{initial:s.scroll,current:s.scroll,max:r,diff:{value:w,displacement:w}}}})(),c="vertical"===o?ee:te;return{descriptor:e,isCombineEnabled:r,isFixedOnPage:n,axis:c,isEnabled:t,client:i,page:a,frame:l,subject:_({page:a,withPlaceholder:null,axis:c,frame:l})}})({descriptor:t,isEnabled:!i,isCombineEnabled:a,isFixedOnPage:r.isFixedOnPage,direction:o,client:d,page:p,closest:u});return g};const Vn={passive:!1},Kn={passive:!0};var qn=e=>e.shouldPublishImmediately?Vn:Kn;const zn=e=>e&&e.env.closestScrollable||null;function Yn(e){const t=(0,n.useRef)(null),r=Cn(Tr),o=Pr("droppable"),{registry:i,marshal:a}=r,l=Mr(e),p=(0,s.Kr)((()=>({id:e.droppableId,type:e.type,mode:e.mode})),[e.droppableId,e.mode,e.type]),u=(0,n.useRef)(p),g=(0,s.Kr)((()=>(0,c.A)(((e,r)=>{t.current||v();const n={x:e,y:r};a.updateDroppableScroll(p.id,n)}))),[p.id,a]),m=(0,s.hb)((()=>{const e=t.current;return e&&e.env.closestScrollable?$n(e.env.closestScrollable):w}),[]),f=(0,s.hb)((()=>{const e=m();g(e.x,e.y)}),[m,g]),b=(0,s.Kr)((()=>(0,d.A)(f)),[f]),h=(0,s.hb)((()=>{const e=t.current,r=zn(e);e&&r||v();e.scrollOptions.shouldPublishImmediately?f():b()}),[b,f]),I=(0,s.hb)(((e,n)=>{t.current&&v();const o=l.current,i=o.getDroppableRef();i||v();const a=(e=>({closestScrollable:Un(e),isFixedOnPage:Hn(e)}))(i),s={ref:i,descriptor:p,env:a,scrollOptions:n};t.current=s;const c=jn({ref:i,descriptor:p,env:a,windowScroll:e,direction:o.direction,isDropDisabled:o.isDropDisabled,isCombineEnabled:o.isCombineEnabled,shouldClipSubject:!o.ignoreContainerClipping}),d=a.closestScrollable;return d&&(d.setAttribute(br.contextId,r.contextId),d.addEventListener("scroll",h,qn(s.scrollOptions))),c}),[r.contextId,p,h,l]),y=(0,s.hb)((()=>{const e=t.current,r=zn(e);return e&&r||v(),$n(r)}),[]),D=(0,s.hb)((()=>{const e=t.current;e||v();const r=zn(e);t.current=null,r&&(b.cancel(),r.removeAttribute(br.contextId),r.removeEventListener("scroll",h,qn(e.scrollOptions)))}),[h,b]),x=(0,s.hb)((e=>{const r=t.current;r||v();const n=zn(r);n||v(),n.scrollTop+=e.y,n.scrollLeft+=e.x}),[]),A=(0,s.Kr)((()=>({getDimensionAndWatchScroll:I,getScrollWhileDragging:y,dragStopped:D,scroll:x})),[D,I,y,x]),E=(0,s.Kr)((()=>({uniqueId:o,descriptor:p,callbacks:A})),[A,p,o]);Ir((()=>(u.current=E.descriptor,i.droppable.register(E),()=>{t.current&&D(),i.droppable.unregister(E)})),[A,p,D,E,a,i.droppable]),Ir((()=>{t.current&&a.updateDroppableIsEnabled(u.current.id,!e.isDropDisabled)}),[e.isDropDisabled,a]),Ir((()=>{t.current&&a.updateDroppableIsCombineEnabled(u.current.id,e.isCombineEnabled)}),[e.isCombineEnabled,a])}function Jn(){}const Xn={width:0,height:0,margin:{top:0,right:0,bottom:0,left:0}},Qn=({isAnimatingOpenOnMount:e,placeholder:t,animate:r})=>{const n=(({isAnimatingOpenOnMount:e,placeholder:t,animate:r})=>e||"close"===r?Xn:{height:t.client.borderBox.height,width:t.client.borderBox.width,margin:t.client.margin})({isAnimatingOpenOnMount:e,placeholder:t,animate:r});return{display:t.display,boxSizing:"border-box",width:n.width,height:n.height,marginTop:n.margin.top,marginRight:n.margin.right,marginBottom:n.margin.bottom,marginLeft:n.margin.left,flexShrink:"0",flexGrow:"0",pointerEvents:"none",transition:"none"!==r?It.placeholder:null}};var Zn=n.memo((e=>{const t=(0,n.useRef)(null),r=(0,s.hb)((()=>{t.current&&(clearTimeout(t.current),t.current=null)}),[]),{animate:o,onTransitionEnd:i,onClose:a,contextId:l}=e,[c,d]=(0,n.useState)("open"===e.animate);(0,n.useEffect)((()=>c?"open"!==o?(r(),d(!1),Jn):t.current?Jn:(t.current=setTimeout((()=>{t.current=null,d(!1)})),r):Jn),[o,c,r]);const p=(0,s.hb)((e=>{"height"===e.propertyName&&(i(),"close"===o&&a())}),[o,a,i]),u=Qn({isAnimatingOpenOnMount:c,animate:e.animate,placeholder:e.placeholder});return n.createElement(e.placeholder.tagName,{style:u,"data-rfd-placeholder-context-id":l,onTransitionEnd:p,ref:e.innerRef})}));class eo extends n.PureComponent{constructor(...e){super(...e),this.state={isVisible:Boolean(this.props.on),data:this.props.on,animate:this.props.shouldAnimate&&this.props.on?"open":"none"},this.onClose=()=>{"close"===this.state.animate&&this.setState({isVisible:!1})}}static getDerivedStateFromProps(e,t){return e.shouldAnimate?e.on?{isVisible:!0,data:e.on,animate:"open"}:t.isVisible?{isVisible:!0,data:t.data,animate:"close"}:{isVisible:!1,animate:"close",data:null}:{isVisible:Boolean(e.on),data:e.on,animate:"none"}}render(){if(!this.state.isVisible)return null;const e={onClose:this.onClose,data:this.state.data,animate:this.state.animate};return this.props.children(e)}}const to={mode:"standard",type:"DEFAULT",direction:"vertical",isDropDisabled:!1,isCombineEnabled:!1,ignoreContainerClipping:!1,renderClone:null,getContainerForClone:function(){return document.body||v(),document.body}},ro=e=>{let t,r={...e};for(t in to)void 0===e[t]&&(r={...r,[t]:to[t]});return r},no=(e,t)=>e===t.droppable.type,oo=(e,t)=>t.draggables[e.draggable.id],io={updateViewportMaxScroll:e=>({type:"UPDATE_VIEWPORT_MAX_SCROLL",payload:e})},ao=(0,a.Ng)((()=>{const e={placeholder:null,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:!1,draggingOverWith:null,draggingFromThisWith:null,isUsingPlaceholder:!1},useClone:null},t={...e,shouldAnimatePlaceholder:!1},r=(0,c.A)((e=>({draggableId:e.id,type:e.type,source:{index:e.index,droppableId:e.droppableId}}))),n=(0,c.A)(((n,o,i,a,s,l)=>{const c=s.descriptor.id;if(s.descriptor.droppableId===n){const e=l?{render:l,dragging:r(s.descriptor)}:null,t={isDraggingOver:i,draggingOverWith:i?c:null,draggingFromThisWith:c,isUsingPlaceholder:!0};return{placeholder:s.placeholder,shouldAnimatePlaceholder:!1,snapshot:t,useClone:e}}if(!o)return t;if(!a)return e;const d={isDraggingOver:i,draggingOverWith:c,draggingFromThisWith:null,isUsingPlaceholder:!0};return{placeholder:s.placeholder,shouldAnimatePlaceholder:!0,snapshot:d,useClone:null}}));return(r,o)=>{const i=ro(o),a=i.droppableId,s=i.type,l=!i.isDropDisabled,c=i.renderClone;if(_r(r)){const e=r.critical;if(!no(s,e))return t;const o=oo(e,r.dimensions),i=Oe(r.impact)===a;return n(a,l,i,i,o,c)}if("DROP_ANIMATING"===r.phase){const e=r.completed;if(!no(s,e.critical))return t;const o=oo(e.critical,r.dimensions);return n(a,l,On(e.result)===a,Oe(e.impact)===a,o,c)}if("IDLE"===r.phase&&r.completed&&!r.shouldFlush){const n=r.completed;if(!no(s,n.critical))return t;const o=Oe(n.impact)===a,i=Boolean(n.impact.at&&"COMBINE"===n.impact.at.type),l=n.critical.droppable.id===a;return o?i?e:t:l?e:t}return t}}),io,((e,t,r)=>({...ro(r),...e,...t})),{context:Sr,areStatePropsEqual:Bn})((e=>{const t=(0,n.useContext)(Tr);t||v();const{contextId:r,isMovementAllowed:i}=t,a=(0,n.useRef)(null),l=(0,n.useRef)(null),{children:c,droppableId:d,type:p,mode:u,direction:g,ignoreContainerClipping:m,isDropDisabled:f,isCombineEnabled:b,snapshot:h,useClone:I,updateViewportMaxScroll:y,getContainerForClone:D}=e,x=(0,s.hb)((()=>a.current),[]),A=(0,s.hb)(((e=null)=>{a.current=e}),[]),E=((0,s.hb)((()=>l.current),[]),(0,s.hb)(((e=null)=>{l.current=e}),[]));Gr();const w=(0,s.hb)((()=>{i()&&y({maxScroll:Ht()})}),[i,y]);Yn({droppableId:d,type:p,mode:u,direction:g,isDropDisabled:f,isCombineEnabled:b,ignoreContainerClipping:m,getDroppableRef:x});const C=(0,s.Kr)((()=>n.createElement(eo,{on:e.placeholder,shouldAnimate:e.shouldAnimatePlaceholder},(({onClose:e,data:t,animate:o})=>n.createElement(Zn,{placeholder:t,onClose:e,innerRef:E,animate:o,contextId:r,onTransitionEnd:w})))),[r,w,e.placeholder,e.shouldAnimatePlaceholder,E]),S=(0,s.Kr)((()=>({innerRef:A,placeholder:C,droppableProps:{"data-rfd-droppable-id":d,"data-rfd-droppable-context-id":r}})),[r,d,C,A]),B=I?I.dragging.draggableId:null,O=(0,s.Kr)((()=>({droppableId:d,type:p,isUsingCloneFor:B})),[d,B,p]);return n.createElement(wn.Provider,{value:O},c(S,h),function(){if(!I)return null;const{dragging:e,render:t}=I,r=n.createElement(Ln,{draggableId:e.draggableId,index:e.source.index,isClone:!0,isEnabled:!0,shouldRespectForcePress:!1,canDragInteractiveElements:!0},((r,n)=>t(r,n,e)));return o.createPortal(r,D())}())}));var so=ao}}]);
//# sourceMappingURL=957c942f-fdffc0a7bd7297d12079.js.map