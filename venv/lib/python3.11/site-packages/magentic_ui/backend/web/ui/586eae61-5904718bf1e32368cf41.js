/*! For license information please see 586eae61-5904718bf1e32368cf41.js.LICENSE.txt */
"use strict";(self.webpackChunkMagentic_UI=self.webpackChunkMagentic_UI||[]).push([[358],{76726:function(e,t,r){r.r(t),r.d(t,{VncScreen:function(){return Dt}});var n=r(74848),i=r(96540);function o(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var a,s={},u={};function c(){if(a)return u;return a=1,Object.defineProperty(u,"__esModule",{value:!0}),u.toSigned32bit=function(e){return 0|e},u.toUnsigned32bit=function(e){return e>>>0},u}var l,h={};function f(){if(l)return h;l=1,Object.defineProperty(h,"__esModule",{value:!0}),h.Warn=h.Info=h.Error=h.Debug=void 0,h.getLogging=function(){return e},h.initLogging=t;var e="warn";function t(t){if(typeof t>"u"?t=e:e=t,h.Debug=h.Info=h.Warn=h.Error=function(){},typeof window.console<"u")switch(t){case"debug":h.Debug=console.debug.bind(window.console);case"info":h.Info=console.info.bind(window.console);case"warn":h.Warn=console.warn.bind(window.console);case"error":h.Error=console.error.bind(window.console);case"none":break;default:throw new window.Error("invalid logging type '"+t+"'")}}return h.Debug=function(){},h.Info=function(){},h.Warn=function(){},h.Error=function(){},t(),h}var d,_={};function p(){if(d)return _;return d=1,Object.defineProperty(_,"__esModule",{value:!0}),_.decodeUTF8=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{return decodeURIComponent(escape(e))}catch(r){if(r instanceof URIError&&t)return e;throw r}},_.encodeUTF8=function(e){return unescape(encodeURIComponent(e))},_}var y,v={};function g(){if(y)return v;function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)}y=1,Object.defineProperty(v,"__esModule",{value:!0}),v.hasScrollbarGutter=v.dragThreshold=void 0,v.isAndroid=function(){return!!navigator.userAgent.match("Android ")},v.isBlink=function(){return!!navigator.userAgent.match("Chrome/...")},v.isChrome=function(){return!(!navigator.userAgent.match("Chrome/...")||navigator.userAgent.match("Chromium/...")||navigator.userAgent.match("Edg/...")||navigator.userAgent.match("OPR/..."))},v.isChromeOS=function(){return!!navigator.userAgent.match(" CrOS ")},v.isChromium=function(){return!!navigator.userAgent.match("Chromium/...")},v.isEdge=function(){return!!navigator.userAgent.match("Edg/...")},v.isFirefox=function(){return!!navigator.userAgent.match("Firefox/...")&&!navigator.userAgent.match("Seamonkey/...")},v.isGecko=function(){return!!navigator.userAgent.match("Gecko/...")},v.isIOS=function(){return!!/ipad/i.exec(navigator.platform)||!!/iphone/i.exec(navigator.platform)||!!/ipod/i.exec(navigator.platform)},v.isMac=function(){return!!/mac/i.exec(navigator.platform)},v.isOpera=function(){return!!navigator.userAgent.match("OPR/...")},v.isSafari=function(){return!(!navigator.userAgent.match("Safari/...")||navigator.userAgent.match("Chrome/...")||navigator.userAgent.match("Chromium/...")||navigator.userAgent.match("Epiphany/..."))},v.isTouchDevice=void 0,v.isWebKit=function(){return!!navigator.userAgent.match("AppleWebKit/...")&&!navigator.userAgent.match("Chrome/...")},v.isWindows=function(){return!!/win/i.exec(navigator.platform)},v.supportsCursorURIs=void 0;var t=function(t,n){if(t&&t.__esModule)return t;if(null===t||"object"!=e(t)&&"function"!=typeof t)return{default:t};var i=r(n);if(i&&i.has(t))return i.get(t);var o={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in t)if("default"!==s&&{}.hasOwnProperty.call(t,s)){var u=a?Object.getOwnPropertyDescriptor(t,s):null;u&&(u.get||u.set)?Object.defineProperty(o,s,u):o[s]=t[s]}return o.default=t,i&&i.set(t,o),o}(f());function r(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(r=function(e){return e?n:t})(e)}v.isTouchDevice="ontouchstart"in document.documentElement||void 0!==document.ontouchstart||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0,window.addEventListener("touchstart",(function e(){v.isTouchDevice=!0,window.removeEventListener("touchstart",e,!1)}),!1),v.dragThreshold=10*(window.devicePixelRatio||1);var n=!1;try{var i=document.createElement("canvas");i.style.cursor='url("data:image/x-icon;base64,AAACAAEACAgAAAIAAgA4AQAAFgAAACgAAAAIAAAAEAAAAAEAIAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAD/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////AAAAAAAAAAAAAAAAAAAAAA==") 2 2, default',0===i.style.cursor.indexOf("url")?(t.Info("Data URI scheme cursor supported"),n=!0):t.Warn("Data URI scheme cursor not supported")}catch(c){t.Error("Data URI scheme cursor test exception: "+c)}v.supportsCursorURIs=n;var o=!0;try{var a=document.createElement("div");a.style.visibility="hidden",a.style.overflow="scroll",document.body.appendChild(a);var s=document.createElement("div");a.appendChild(s);var u=a.offsetWidth-s.offsetWidth;a.parentNode.removeChild(a),o=0!=u}catch(c){t.Error("Scrollbar test exception: "+c)}return v.hasScrollbarGutter=o,v}var m,b={};var w,k={};function X(){if(w)return k;w=1,Object.defineProperty(k,"__esModule",{value:!0}),k.getPointerEvent=function(e){return e.changedTouches?e.changedTouches[0]:e.touches?e.touches[0]:e},k.releaseCapture=o,k.setCapture=function(e){if(e.setCapture)e.setCapture(),document.captureElement=e;else{o();var t=document.getElementById("noVNC_mouse_capture_elem");null===t&&((t=document.createElement("div")).id="noVNC_mouse_capture_elem",t.style.position="fixed",t.style.top="0px",t.style.left="0px",t.style.width="100%",t.style.height="100%",t.style.zIndex=1e4,t.style.display="none",document.body.appendChild(t),t.addEventListener("contextmenu",r),t.addEventListener("mousemove",r),t.addEventListener("mouseup",r)),document.captureElement=e,i.observe(e,{attributes:!0}),n(),t.style.display="",window.addEventListener("mousemove",r),window.addEventListener("mouseup",r)}},k.stopEvent=function(e){e.stopPropagation(),e.preventDefault()};var e=!1,t=null;function r(r){if(!e){var n=new r.constructor(r.type,r);e=!0,document.captureElement?document.captureElement.dispatchEvent(n):t.dispatchEvent(n),e=!1,r.stopPropagation(),n.defaultPrevented&&r.preventDefault(),"mouseup"===r.type&&o()}}function n(){document.getElementById("noVNC_mouse_capture_elem").style.cursor=window.getComputedStyle(document.captureElement).cursor}document.captureElement=null;var i=new MutationObserver(n);function o(){if(document.releaseCapture)document.releaseCapture(),document.captureElement=null;else{if(!document.captureElement)return;t=document.captureElement,document.captureElement=null,i.disconnect(),document.getElementById("noVNC_mouse_capture_elem").style.display="none",window.removeEventListener("mousemove",r),window.removeEventListener("mouseup",r)}}return k}var K,S={};function E(){return K||(K=1,function(e){function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(e,t,r){return t&&function(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,n(i.key),i)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function n(e){var r=function(e,r){if("object"!=t(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,r);if("object"!=t(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==t(r)?r:r+""}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,e.default=function(){return r((function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this._listeners=new Map}),[{key:"addEventListener",value:function(e,t){this._listeners.has(e)||this._listeners.set(e,new Set),this._listeners.get(e).add(t)}},{key:"removeEventListener",value:function(e,t){this._listeners.has(e)&&this._listeners.get(e).delete(t)}},{key:"dispatchEvent",value:function(e){var t=this;return!this._listeners.has(e.type)||(this._listeners.get(e.type).forEach((function(r){return r.call(t,e)})),!e.defaultPrevented)}}])}()}(S)),S}var x,C,A={},F={};function L(){return x||(x=1,function(e){function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=t(e)&&"function"!=typeof e)return{default:e};var i=n(r);if(i&&i.has(e))return i.get(e);var o={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&{}.hasOwnProperty.call(e,s)){var u=a?Object.getOwnPropertyDescriptor(e,s):null;u&&(u.get||u.set)?Object.defineProperty(o,s,u):o[s]=e[s]}return o.default=e,i&&i.set(e,o),o}(f());function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}e.default={toBase64Table:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".split(""),base64Pad:"=",encode:function(e){for(var t="",r=e.length,n=r%3,i=0;i<r-2;i+=3)t+=this.toBase64Table[e[i]>>2],t+=this.toBase64Table[((3&e[i])<<4)+(e[i+1]>>4)],t+=this.toBase64Table[((15&e[i+1])<<2)+(e[i+2]>>6)],t+=this.toBase64Table[63&e[i+2]];var o=r-n;return 2===n?(t+=this.toBase64Table[e[o]>>2],t+=this.toBase64Table[((3&e[o])<<4)+(e[o+1]>>4)],t+=this.toBase64Table[(15&e[o+1])<<2],t+=this.toBase64Table[64]):1===n&&(t+=this.toBase64Table[e[o]>>2],t+=this.toBase64Table[(3&e[o])<<4],t+=this.toBase64Table[64],t+=this.toBase64Table[64]),t},toBinaryTable:[-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,62,-1,-1,-1,63,52,53,54,55,56,57,58,59,60,61,-1,-1,-1,0,-1,-1,-1,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,-1,-1,-1,-1,-1,-1,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,-1,-1,-1,-1,-1],decode:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=e.indexOf("=")-t;n<0&&(n=e.length-t);for(var i=3*(n>>2)+Math.floor(n%4/1.5),o=new Array(i),a=0,s=0,u=0,c=t;c<e.length;c++){var l=this.toBinaryTable[127&e.charCodeAt(c)],h=e.charAt(c)===this.base64Pad;-1!==l?(s=s<<6|l,(a+=6)>=8&&(a-=8,h||(o[u++]=s>>a&255),s&=(1<<a)-1)):r.Error("Illegal character code "+e.charCodeAt(c)+" at position "+c)}if(a){var f=new Error("Corrupted base64 string");throw f.name="Base64-Error",f}return o}}}(F)),F}function P(){return C||(C=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var t,r=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var r=o(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&{}.hasOwnProperty.call(e,s)){var u=i?Object.getOwnPropertyDescriptor(e,s):null;u&&(u.get||u.set)?Object.defineProperty(n,s,u):n[s]=e[s]}return n.default=e,r&&r.set(e,n),n}(f()),n=(t=L())&&t.__esModule?t:{default:t},i=c();function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(o=function(e){return e?r:t})(e)}function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function s(e,t,r){return t&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,u(n.key),n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function u(e){var t=function(e,t){if("object"!=a(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==a(t)?t:t+""}e.default=function(){return s((function e(t){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this._drawCtx=null,this._renderQ=[],this._flushPromise=null,this._fbWidth=0,this._fbHeight=0,this._prevDrawStyle="",r.Debug(">> Display.constructor"),this._target=t,!this._target)throw new Error("Target must be set");if("string"==typeof this._target)throw new Error("target must be a DOM element");if(!this._target.getContext)throw new Error("no getContext method");this._targetCtx=this._target.getContext("2d"),this._viewportLoc={x:0,y:0,w:this._target.width,h:this._target.height},this._backbuffer=document.createElement("canvas"),this._drawCtx=this._backbuffer.getContext("2d"),this._damageBounds={left:0,top:0,right:this._backbuffer.width,bottom:this._backbuffer.height},r.Debug("User Agent: "+navigator.userAgent),r.Debug("<< Display.constructor"),this._scale=1,this._clipViewport=!1}),[{key:"scale",get:function(){return this._scale},set:function(e){this._rescale(e)}},{key:"clipViewport",get:function(){return this._clipViewport},set:function(e){this._clipViewport=e;var t=this._viewportLoc;this.viewportChangeSize(t.w,t.h),this.viewportChangePos(0,0)}},{key:"width",get:function(){return this._fbWidth}},{key:"height",get:function(){return this._fbHeight}},{key:"viewportChangePos",value:function(e,t){var n=this._viewportLoc;e=Math.floor(e),t=Math.floor(t),this._clipViewport||(e=-n.w,t=-n.h);var i=n.x+n.w-1,o=n.y+n.h-1;e<0&&n.x+e<0&&(e=-n.x),i+e>=this._fbWidth&&(e-=i+e-this._fbWidth+1),n.y+t<0&&(t=-n.y),o+t>=this._fbHeight&&(t-=o+t-this._fbHeight+1),(0!==e||0!==t)&&(r.Debug("viewportChange deltaX: "+e+", deltaY: "+t),n.x+=e,n.y+=t,this._damage(n.x,n.y,n.w,n.h),this.flip())}},{key:"viewportChangeSize",value:function(e,t){(!this._clipViewport||typeof e>"u"||typeof t>"u")&&(r.Debug("Setting viewport to full display region"),e=this._fbWidth,t=this._fbHeight),e=Math.floor(e),t=Math.floor(t),e>this._fbWidth&&(e=this._fbWidth),t>this._fbHeight&&(t=this._fbHeight);var n=this._viewportLoc;if(n.w!==e||n.h!==t){n.w=e,n.h=t;var i=this._target;i.width=e,i.height=t,this.viewportChangePos(0,0),this._damage(n.x,n.y,n.w,n.h),this.flip(),this._rescale(this._scale)}}},{key:"absX",value:function(e){return 0===this._scale?0:(0,i.toSigned32bit)(e/this._scale+this._viewportLoc.x)}},{key:"absY",value:function(e){return 0===this._scale?0:(0,i.toSigned32bit)(e/this._scale+this._viewportLoc.y)}},{key:"resize",value:function(e,t){this._prevDrawStyle="",this._fbWidth=e,this._fbHeight=t;var r=this._backbuffer;if(r.width!==e||r.height!==t){var n=null;r.width>0&&r.height>0&&(n=this._drawCtx.getImageData(0,0,r.width,r.height)),r.width!==e&&(r.width=e),r.height!==t&&(r.height=t),n&&this._drawCtx.putImageData(n,0,0)}var i=this._viewportLoc;this.viewportChangeSize(i.w,i.h),this.viewportChangePos(0,0)}},{key:"getImageData",value:function(){return this._drawCtx.getImageData(0,0,this.width,this.height)}},{key:"toDataURL",value:function(e,t){return this._backbuffer.toDataURL(e,t)}},{key:"toBlob",value:function(e,t,r){return this._backbuffer.toBlob(e,t,r)}},{key:"_damage",value:function(e,t,r,n){e<this._damageBounds.left&&(this._damageBounds.left=e),t<this._damageBounds.top&&(this._damageBounds.top=t),e+r>this._damageBounds.right&&(this._damageBounds.right=e+r),t+n>this._damageBounds.bottom&&(this._damageBounds.bottom=t+n)}},{key:"flip",value:function(e){if(0===this._renderQ.length||e){var t=this._damageBounds.left,r=this._damageBounds.top,n=this._damageBounds.right-t,i=this._damageBounds.bottom-r,o=t-this._viewportLoc.x,a=r-this._viewportLoc.y;o<0&&(n+=o,t-=o,o=0),a<0&&(i+=a,r-=a,a=0),o+n>this._viewportLoc.w&&(n=this._viewportLoc.w-o),a+i>this._viewportLoc.h&&(i=this._viewportLoc.h-a),n>0&&i>0&&this._targetCtx.drawImage(this._backbuffer,t,r,n,i,o,a,n,i),this._damageBounds.left=this._damageBounds.top=65535,this._damageBounds.right=this._damageBounds.bottom=0}else this._renderQPush({type:"flip"})}},{key:"pending",value:function(){return this._renderQ.length>0}},{key:"flush",value:function(){var e=this;return 0===this._renderQ.length?Promise.resolve():(null===this._flushPromise&&(this._flushPromise=new Promise((function(t){e._flushResolve=t}))),this._flushPromise)}},{key:"fillRect",value:function(e,t,r,n,i,o){0===this._renderQ.length||o?(this._setFillColor(i),this._drawCtx.fillRect(e,t,r,n),this._damage(e,t,r,n)):this._renderQPush({type:"fill",x:e,y:t,width:r,height:n,color:i})}},{key:"copyImage",value:function(e,t,r,n,i,o,a){0===this._renderQ.length||a?(this._drawCtx.mozImageSmoothingEnabled=!1,this._drawCtx.webkitImageSmoothingEnabled=!1,this._drawCtx.msImageSmoothingEnabled=!1,this._drawCtx.imageSmoothingEnabled=!1,this._drawCtx.drawImage(this._backbuffer,e,t,i,o,r,n,i,o),this._damage(r,n,i,o)):this._renderQPush({type:"copy",oldX:e,oldY:t,x:r,y:n,width:i,height:o})}},{key:"imageRect",value:function(e,t,r,i,o,a){if(0!==r&&0!==i){var s=new Image;s.src="data: "+o+";base64,"+n.default.encode(a),this._renderQPush({type:"img",img:s,x:e,y:t,width:r,height:i})}}},{key:"blitImage",value:function(e,t,r,n,i,o,a){if(0===this._renderQ.length||a){var s=new Uint8ClampedArray(i.buffer,i.byteOffset+o,r*n*4),u=new ImageData(s,r,n);this._drawCtx.putImageData(u,e,t),this._damage(e,t,r,n)}else{var c=new Uint8Array(r*n*4);c.set(new Uint8Array(i.buffer,0,c.length)),this._renderQPush({type:"blit",data:c,x:e,y:t,width:r,height:n})}}},{key:"drawImage",value:function(e,t,r){this._drawCtx.drawImage(e,t,r),this._damage(t,r,e.width,e.height)}},{key:"autoscale",value:function(e,t){var r;if(0===e||0===t)r=0;else{var n=this._viewportLoc,i=e/t;r=n.w/n.h>=i?e/n.w:t/n.h}this._rescale(r)}},{key:"_rescale",value:function(e){this._scale=e;var t=this._viewportLoc,r=e*t.w+"px",n=e*t.h+"px";(this._target.style.width!==r||this._target.style.height!==n)&&(this._target.style.width=r,this._target.style.height=n)}},{key:"_setFillColor",value:function(e){var t="rgb("+e[0]+","+e[1]+","+e[2]+")";t!==this._prevDrawStyle&&(this._drawCtx.fillStyle=t,this._prevDrawStyle=t)}},{key:"_renderQPush",value:function(e){this._renderQ.push(e),1===this._renderQ.length&&this._scanRenderQ()}},{key:"_resumeRenderQ",value:function(){this.removeEventListener("load",this._noVNCDisplay._resumeRenderQ),this._noVNCDisplay._scanRenderQ()}},{key:"_scanRenderQ",value:function(){for(var e=!0;e&&this._renderQ.length>0;){var t=this._renderQ[0];switch(t.type){case"flip":this.flip(!0);break;case"copy":this.copyImage(t.oldX,t.oldY,t.x,t.y,t.width,t.height,!0);break;case"fill":this.fillRect(t.x,t.y,t.width,t.height,t.color,!0);break;case"blit":this.blitImage(t.x,t.y,t.width,t.height,t.data,0,!0);break;case"img":if(t.img.complete){if(t.img.width!==t.width||t.img.height!==t.height)return void r.Error("Decoded image has incorrect dimensions. Got "+t.img.width+"x"+t.img.height+". Expected "+t.width+"x"+t.height+".");this.drawImage(t.img,t.x,t.y)}else t.img._noVNCDisplay=this,t.img.addEventListener("load",this._resumeRenderQ),e=!1}e&&this._renderQ.shift()}0===this._renderQ.length&&null!==this._flushPromise&&(this._flushResolve(),this._flushPromise=null,this._flushResolve=null)}}])}()}(A)),A}var M,T={},Q={},R={};function O(){if(M)return R;return M=1,Object.defineProperty(R,"__esModule",{value:!0}),R.Buf8=R.Buf32=R.Buf16=void 0,R.arraySet=function(e,t,r,n,i){if(t.subarray&&e.subarray)return void e.set(t.subarray(r,r+n),i);for(var o=0;o<n;o++)e[i+o]=t[r+o]},R.flattenChunks=function(e){var t,r,n,i,o,a;for(n=0,t=0,r=e.length;t<r;t++)n+=e[t].length;for(a=new Uint8Array(n),i=0,t=0,r=e.length;t<r;t++)o=e[t],a.set(o,i),i+=o.length;return a},R.shrinkBuf=function(e,t){return e.length===t?e:e.subarray?e.subarray(0,t):(e.length=t,e)},R.Buf8=Uint8Array,R.Buf16=Uint16Array,R.Buf32=Int32Array,R}var B,D={};function j(){return B||(B=1,e=D,Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(e,t,r,n){for(var i=65535&e,o=e>>>16&65535,a=0;0!==r;){r-=a=r>2e3?2e3:r;do{o=o+(i=i+t[n++]|0)|0}while(--a);i%=65521,o%=65521}return i|o<<16}),D;var e}var I,U={};function N(){return I||(I=1,function(e){function t(){for(var e,t=[],r=0;r<256;r++){e=r;for(var n=0;n<8;n++)e=1&e?3988292384^e>>>1:e>>>1;t[r]=e}return t}Object.defineProperty(e,"__esModule",{value:!0}),e.default=t,t()}(U)),U}var z,H={};var V,W,G={};function Z(){if(W)return Q;function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)}W=1,Object.defineProperty(Q,"__esModule",{value:!0}),Q.Z_TREES=Q.Z_STREAM_ERROR=Q.Z_STREAM_END=Q.Z_OK=Q.Z_NEED_DICT=Q.Z_MEM_ERROR=Q.Z_FINISH=Q.Z_DEFLATED=Q.Z_DATA_ERROR=Q.Z_BUF_ERROR=Q.Z_BLOCK=void 0,Q.inflate=function(e,a){var s,Q,O,j,N,z,H,V,W,G,ue,ce,le,fe,de,_e,pe,ye,ve,ge,me,ke,Xe,Ke,Se=0,Ee=new t.Buf8(4),xe=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!e||!e.state||!e.output||!e.input&&0!==e.avail_in)return v;(s=e.state).mode===M&&(s.mode=T),N=e.next_out,O=e.output,H=e.avail_out,j=e.next_in,Q=e.input,z=e.avail_in,V=s.hold,W=s.bits,G=z,ue=H,ke=_;e:for(;;)switch(s.mode){case k:if(0===s.wrap){s.mode=T;break}for(;W<16;){if(0===z)break e;z--,V+=Q[j++]<<W,W+=8}if(2&s.wrap&&35615===V){s.check=0,Ee[0]=255&V,Ee[1]=V>>>8&255,s.check=(0,n.default)(s.check,Ee,2,0),V=0,W=0,s.mode=X;break}if(s.flags=0,s.head&&(s.head.done=!1),!(1&s.wrap)||(((255&V)<<8)+(V>>8))%31){e.msg="incorrect header check",s.mode=ae;break}if((15&V)!==w){e.msg="unknown compression method",s.mode=ae;break}if(W-=4,me=8+(15&(V>>>=4)),0===s.wbits)s.wbits=me;else if(me>s.wbits){e.msg="invalid window size",s.mode=ae;break}s.dmax=1<<me,e.adler=s.check=1,s.mode=512&V?L:M,V=0,W=0;break;case X:for(;W<16;){if(0===z)break e;z--,V+=Q[j++]<<W,W+=8}if(s.flags=V,(255&s.flags)!==w){e.msg="unknown compression method",s.mode=ae;break}if(57344&s.flags){e.msg="unknown header flags set",s.mode=ae;break}s.head&&(s.head.text=V>>8&1),512&s.flags&&(Ee[0]=255&V,Ee[1]=V>>>8&255,s.check=(0,n.default)(s.check,Ee,2,0)),V=0,W=0,s.mode=K;case K:for(;W<32;){if(0===z)break e;z--,V+=Q[j++]<<W,W+=8}s.head&&(s.head.time=V),512&s.flags&&(Ee[0]=255&V,Ee[1]=V>>>8&255,Ee[2]=V>>>16&255,Ee[3]=V>>>24&255,s.check=(0,n.default)(s.check,Ee,4,0)),V=0,W=0,s.mode=S;case S:for(;W<16;){if(0===z)break e;z--,V+=Q[j++]<<W,W+=8}s.head&&(s.head.xflags=255&V,s.head.os=V>>8),512&s.flags&&(Ee[0]=255&V,Ee[1]=V>>>8&255,s.check=(0,n.default)(s.check,Ee,2,0)),V=0,W=0,s.mode=E;case E:if(1024&s.flags){for(;W<16;){if(0===z)break e;z--,V+=Q[j++]<<W,W+=8}s.length=V,s.head&&(s.head.extra_len=V),512&s.flags&&(Ee[0]=255&V,Ee[1]=V>>>8&255,s.check=(0,n.default)(s.check,Ee,2,0)),V=0,W=0}else s.head&&(s.head.extra=null);s.mode=x;case x:if(1024&s.flags&&((ce=s.length)>z&&(ce=z),ce&&(s.head&&(me=s.head.extra_len-s.length,s.head.extra||(s.head.extra=new Array(s.head.extra_len)),t.arraySet(s.head.extra,Q,j,ce,me)),512&s.flags&&(s.check=(0,n.default)(s.check,Q,ce,j)),z-=ce,j+=ce,s.length-=ce),s.length))break e;s.length=0,s.mode=C;case C:if(2048&s.flags){if(0===z)break e;ce=0;do{me=Q[j+ce++],s.head&&me&&s.length<65536&&(s.head.name+=String.fromCharCode(me))}while(me&&ce<z);if(512&s.flags&&(s.check=(0,n.default)(s.check,Q,ce,j)),z-=ce,j+=ce,me)break e}else s.head&&(s.head.name=null);s.length=0,s.mode=A;case A:if(4096&s.flags){if(0===z)break e;ce=0;do{me=Q[j+ce++],s.head&&me&&s.length<65536&&(s.head.comment+=String.fromCharCode(me))}while(me&&ce<z);if(512&s.flags&&(s.check=(0,n.default)(s.check,Q,ce,j)),z-=ce,j+=ce,me)break e}else s.head&&(s.head.comment=null);s.mode=F;case F:if(512&s.flags){for(;W<16;){if(0===z)break e;z--,V+=Q[j++]<<W,W+=8}if(V!==(65535&s.check)){e.msg="header crc mismatch",s.mode=ae;break}V=0,W=0}s.head&&(s.head.hcrc=s.flags>>9&1,s.head.done=!0),e.adler=s.check=0,s.mode=M;break;case L:for(;W<32;){if(0===z)break e;z--,V+=Q[j++]<<W,W+=8}e.adler=s.check=he(V),V=0,W=0,s.mode=P;case P:if(0===s.havedict)return e.next_out=N,e.avail_out=H,e.next_in=j,e.avail_in=z,s.hold=V,s.bits=W,y;e.adler=s.check=1,s.mode=M;case M:if(a===f||a===d)break e;case T:if(s.last){V>>>=7&W,W-=7&W,s.mode=ne;break}for(;W<3;){if(0===z)break e;z--,V+=Q[j++]<<W,W+=8}switch(s.last=1&V,W-=1,3&(V>>>=1)){case 0:s.mode=R;break;case 1:if(be(s),s.mode=Y,a===d){V>>>=2,W-=2;break e}break;case 2:s.mode=I;break;case 3:e.msg="invalid block type",s.mode=ae}V>>>=2,W-=2;break;case R:for(V>>>=7&W,W-=7&W;W<32;){if(0===z)break e;z--,V+=Q[j++]<<W,W+=8}if((65535&V)!=(V>>>16^65535)){e.msg="invalid stored block lengths",s.mode=ae;break}if(s.length=65535&V,V=0,W=0,s.mode=B,a===d)break e;case B:s.mode=D;case D:if(ce=s.length){if(ce>z&&(ce=z),ce>H&&(ce=H),0===ce)break e;t.arraySet(O,Q,j,ce,N),z-=ce,j+=ce,H-=ce,N+=ce,s.length-=ce;break}s.mode=M;break;case I:for(;W<14;){if(0===z)break e;z--,V+=Q[j++]<<W,W+=8}if(s.nlen=257+(31&V),V>>>=5,W-=5,s.ndist=1+(31&V),V>>>=5,W-=5,s.ncode=4+(15&V),V>>>=4,W-=4,s.nlen>286||s.ndist>30){e.msg="too many length or distance symbols",s.mode=ae;break}s.have=0,s.mode=U;case U:for(;s.have<s.ncode;){for(;W<3;){if(0===z)break e;z--,V+=Q[j++]<<W,W+=8}s.lens[xe[s.have++]]=7&V,V>>>=3,W-=3}for(;s.have<19;)s.lens[xe[s.have++]]=0;if(s.lencode=s.lendyn,s.lenbits=7,Xe={bits:s.lenbits},ke=(0,o.default)(u,s.lens,0,19,s.lencode,0,s.work,Xe),s.lenbits=Xe.bits,ke){e.msg="invalid code lengths set",s.mode=ae;break}s.have=0,s.mode=Z;case Z:for(;s.have<s.nlen+s.ndist;){for(;_e=(Se=s.lencode[V&(1<<s.lenbits)-1])>>>16&255,pe=65535&Se,!((de=Se>>>24)<=W);){if(0===z)break e;z--,V+=Q[j++]<<W,W+=8}if(pe<16)V>>>=de,W-=de,s.lens[s.have++]=pe;else{if(16===pe){for(Ke=de+2;W<Ke;){if(0===z)break e;z--,V+=Q[j++]<<W,W+=8}if(V>>>=de,W-=de,0===s.have){e.msg="invalid bit length repeat",s.mode=ae;break}me=s.lens[s.have-1],ce=3+(3&V),V>>>=2,W-=2}else if(17===pe){for(Ke=de+3;W<Ke;){if(0===z)break e;z--,V+=Q[j++]<<W,W+=8}W-=de,me=0,ce=3+(7&(V>>>=de)),V>>>=3,W-=3}else{for(Ke=de+7;W<Ke;){if(0===z)break e;z--,V+=Q[j++]<<W,W+=8}W-=de,me=0,ce=11+(127&(V>>>=de)),V>>>=7,W-=7}if(s.have+ce>s.nlen+s.ndist){e.msg="invalid bit length repeat",s.mode=ae;break}for(;ce--;)s.lens[s.have++]=me}}if(s.mode===ae)break;if(0===s.lens[256]){e.msg="invalid code -- missing end-of-block",s.mode=ae;break}if(s.lenbits=9,Xe={bits:s.lenbits},ke=(0,o.default)(c,s.lens,0,s.nlen,s.lencode,0,s.work,Xe),s.lenbits=Xe.bits,ke){e.msg="invalid literal/lengths set",s.mode=ae;break}if(s.distbits=6,s.distcode=s.distdyn,Xe={bits:s.distbits},ke=(0,o.default)(l,s.lens,s.nlen,s.ndist,s.distcode,0,s.work,Xe),s.distbits=Xe.bits,ke){e.msg="invalid distances set",s.mode=ae;break}if(s.mode=Y,a===d)break e;case Y:s.mode=q;case q:if(z>=6&&H>=258){e.next_out=N,e.avail_out=H,e.next_in=j,e.avail_in=z,s.hold=V,s.bits=W,(0,i.default)(e,ue),N=e.next_out,O=e.output,H=e.avail_out,j=e.next_in,Q=e.input,z=e.avail_in,V=s.hold,W=s.bits,s.mode===M&&(s.back=-1);break}for(s.back=0;_e=(Se=s.lencode[V&(1<<s.lenbits)-1])>>>16&255,pe=65535&Se,!((de=Se>>>24)<=W);){if(0===z)break e;z--,V+=Q[j++]<<W,W+=8}if(_e&&!(240&_e)){for(ye=de,ve=_e,ge=pe;_e=(Se=s.lencode[ge+((V&(1<<ye+ve)-1)>>ye)])>>>16&255,pe=65535&Se,!(ye+(de=Se>>>24)<=W);){if(0===z)break e;z--,V+=Q[j++]<<W,W+=8}V>>>=ye,W-=ye,s.back+=ye}if(V>>>=de,W-=de,s.back+=de,s.length=pe,0===_e){s.mode=re;break}if(32&_e){s.back=-1,s.mode=M;break}if(64&_e){e.msg="invalid literal/length code",s.mode=ae;break}s.extra=15&_e,s.mode=J;case J:if(s.extra){for(Ke=s.extra;W<Ke;){if(0===z)break e;z--,V+=Q[j++]<<W,W+=8}s.length+=V&(1<<s.extra)-1,V>>>=s.extra,W-=s.extra,s.back+=s.extra}s.was=s.length,s.mode=$;case $:for(;_e=(Se=s.distcode[V&(1<<s.distbits)-1])>>>16&255,pe=65535&Se,!((de=Se>>>24)<=W);){if(0===z)break e;z--,V+=Q[j++]<<W,W+=8}if(!(240&_e)){for(ye=de,ve=_e,ge=pe;_e=(Se=s.distcode[ge+((V&(1<<ye+ve)-1)>>ye)])>>>16&255,pe=65535&Se,!(ye+(de=Se>>>24)<=W);){if(0===z)break e;z--,V+=Q[j++]<<W,W+=8}V>>>=ye,W-=ye,s.back+=ye}if(V>>>=de,W-=de,s.back+=de,64&_e){e.msg="invalid distance code",s.mode=ae;break}s.offset=pe,s.extra=15&_e,s.mode=ee;case ee:if(s.extra){for(Ke=s.extra;W<Ke;){if(0===z)break e;z--,V+=Q[j++]<<W,W+=8}s.offset+=V&(1<<s.extra)-1,V>>>=s.extra,W-=s.extra,s.back+=s.extra}if(s.offset>s.dmax){e.msg="invalid distance too far back",s.mode=ae;break}s.mode=te;case te:if(0===H)break e;if(ce=ue-H,s.offset>ce){if((ce=s.offset-ce)>s.whave&&s.sane){e.msg="invalid distance too far back",s.mode=ae;break}ce>s.wnext?(ce-=s.wnext,le=s.wsize-ce):le=s.wnext-ce,ce>s.length&&(ce=s.length),fe=s.window}else fe=O,le=N-s.offset,ce=s.length;ce>H&&(ce=H),H-=ce,s.length-=ce;do{O[N++]=fe[le++]}while(--ce);0===s.length&&(s.mode=q);break;case re:if(0===H)break e;O[N++]=s.length,H--,s.mode=q;break;case ne:if(s.wrap){for(;W<32;){if(0===z)break e;z--,V|=Q[j++]<<W,W+=8}if(ue-=H,e.total_out+=ue,s.total+=ue,ue&&(e.adler=s.check=s.flags?(0,n.default)(s.check,O,ue,N-ue):(0,r.default)(s.check,O,ue,N-ue)),ue=H,(s.flags?V:he(V))!==s.check){e.msg="incorrect data check",s.mode=ae;break}V=0,W=0}s.mode=ie;case ie:if(s.wrap&&s.flags){for(;W<32;){if(0===z)break e;z--,V+=Q[j++]<<W,W+=8}if(V!==(4294967295&s.total)){e.msg="incorrect length check",s.mode=ae;break}V=0,W=0}s.mode=oe;case oe:ke=p;break e;case ae:ke=g;break e;case se:return m;default:return v}return e.next_out=N,e.avail_out=H,e.next_in=j,e.avail_in=z,s.hold=V,s.bits=W,(s.wsize||ue!==e.avail_out&&s.mode<ae&&(s.mode<ne||a!==h))&&we(e,e.output,e.next_out,ue-e.avail_out),G-=e.avail_in,ue-=e.avail_out,e.total_in+=G,e.total_out+=ue,s.total+=ue,s.wrap&&ue&&(e.adler=s.check=s.flags?(0,n.default)(s.check,O,ue,e.next_out-ue):(0,r.default)(s.check,O,ue,e.next_out-ue)),e.data_type=s.bits+(s.last?64:0)+(s.mode===M?128:0)+(s.mode===Y||s.mode===B?256:0),(0===G&&0===ue||a===h)&&ke===_&&(ke=b),ke},Q.inflateEnd=function(e){if(!e||!e.state)return v;var t=e.state;return t.window&&(t.window=null),e.state=null,_},Q.inflateGetHeader=function(e,t){var r;return e&&e.state&&2&(r=e.state).wrap?(r.head=t,t.done=!1,_):v},Q.inflateInfo=void 0,Q.inflateInit=function(e){return ye(e,le)},Q.inflateInit2=ye,Q.inflateReset=_e,Q.inflateReset2=pe,Q.inflateResetKeep=de,Q.inflateSetDictionary=function(e,t){var n,i,o=t.length;return!e||!e.state||0!==(n=e.state).wrap&&n.mode!==P?v:n.mode===P&&(i=1,(i=(0,r.default)(i,t,o,0))!==n.check)?g:we(e,t,o,o)?(n.mode=se,m):(n.havedict=1,_)};var t=function(t,r){if(t&&t.__esModule)return t;if(null===t||"object"!=e(t)&&"function"!=typeof t)return{default:t};var n=s(r);if(n&&n.has(t))return n.get(t);var i={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in t)if("default"!==a&&{}.hasOwnProperty.call(t,a)){var u=o?Object.getOwnPropertyDescriptor(t,a):null;u&&(u.get||u.set)?Object.defineProperty(i,a,u):i[a]=t[a]}return i.default=t,n&&n.set(t,i),i}(O()),r=a(j()),n=a(N()),i=a((z||(z=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(e,n){var i,o,a,s,u,c,l,h,f,d,_,p,y,v,g,m,b,w,k,X,K,S,E,x,C;i=e.state,o=e.next_in,x=e.input,a=o+(e.avail_in-5),s=e.next_out,C=e.output,u=s-(n-e.avail_out),c=s+(e.avail_out-257),l=i.dmax,h=i.wsize,f=i.whave,d=i.wnext,_=i.window,p=i.hold,y=i.bits,v=i.lencode,g=i.distcode,m=(1<<i.lenbits)-1,b=(1<<i.distbits)-1;e:do{y<15&&(p+=x[o++]<<y,y+=8,p+=x[o++]<<y,y+=8),w=v[p&m];t:for(;;){if(p>>>=k=w>>>24,y-=k,0==(k=w>>>16&255))C[s++]=65535&w;else{if(!(16&k)){if(64&k){if(32&k){i.mode=r;break e}e.msg="invalid literal/length code",i.mode=t;break e}w=v[(65535&w)+(p&(1<<k)-1)];continue t}for(X=65535&w,(k&=15)&&(y<k&&(p+=x[o++]<<y,y+=8),X+=p&(1<<k)-1,p>>>=k,y-=k),y<15&&(p+=x[o++]<<y,y+=8,p+=x[o++]<<y,y+=8),w=g[p&b];;){if(p>>>=k=w>>>24,y-=k,16&(k=w>>>16&255)){if(K=65535&w,y<(k&=15)&&(p+=x[o++]<<y,(y+=8)<k&&(p+=x[o++]<<y,y+=8)),(K+=p&(1<<k)-1)>l){e.msg="invalid distance too far back",i.mode=t;break e}if(p>>>=k,y-=k,K>(k=s-u)){if((k=K-k)>f&&i.sane){e.msg="invalid distance too far back",i.mode=t;break e}if(S=0,E=_,0===d){if(S+=h-k,k<X){X-=k;do{C[s++]=_[S++]}while(--k);S=s-K,E=C}}else if(d<k){if(S+=h+d-k,(k-=d)<X){X-=k;do{C[s++]=_[S++]}while(--k);if(S=0,d<X){X-=k=d;do{C[s++]=_[S++]}while(--k);S=s-K,E=C}}}else if(S+=d-k,k<X){X-=k;do{C[s++]=_[S++]}while(--k);S=s-K,E=C}for(;X>2;)C[s++]=E[S++],C[s++]=E[S++],C[s++]=E[S++],X-=3;X&&(C[s++]=E[S++],X>1&&(C[s++]=E[S++]))}else{S=s-K;do{C[s++]=C[S++],C[s++]=C[S++],C[s++]=C[S++],X-=3}while(X>2);X&&(C[s++]=C[S++],X>1&&(C[s++]=C[S++]))}break}if(64&k){e.msg="invalid distance code",i.mode=t;break e}w=g[(65535&w)+(p&(1<<k)-1)]}}break}}while(o<a&&s<c);o-=X=y>>3,p&=(1<<(y-=X<<3))-1,e.next_in=o,e.next_out=s,e.avail_in=o<a?a-o+5:5-(o-a),e.avail_out=s<c?c-s+257:257-(s-c),i.hold=p,i.bits=y};var t=30,r=12}(H)),H)),o=a((V||(V=1,function(e){function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(e,t,n,_,p,y,v,g){var m,b,w,k,X,K,S,E,x,C=g.bits,A=0,F=0,L=0,P=0,M=0,T=0,Q=0,R=0,O=0,B=0,D=null,j=0,I=new r.Buf16(i+1),U=new r.Buf16(i+1),N=null,z=0;for(A=0;A<=i;A++)I[A]=0;for(F=0;F<_;F++)I[t[n+F]]++;for(M=C,P=i;P>=1&&0===I[P];P--);if(M>P&&(M=P),0===P)return p[y++]=20971520,p[y++]=20971520,g.bits=1,0;for(L=1;L<P&&0===I[L];L++);for(M<L&&(M=L),R=1,A=1;A<=i;A++)if(R<<=1,(R-=I[A])<0)return-1;if(R>0&&(e===s||1!==P))return-1;for(U[1]=0,A=1;A<i;A++)U[A+1]=U[A]+I[A];for(F=0;F<_;F++)0!==t[n+F]&&(v[U[t[n+F]]++]=F);if(e===s?(D=N=v,K=19):e===u?(D=l,j-=257,N=h,z-=257,K=256):(D=f,N=d,K=-1),B=0,F=0,A=L,X=y,T=M,Q=0,w=-1,k=(O=1<<M)-1,e===u&&O>o||e===c&&O>a)return 1;for(;;){S=A-Q,v[F]<K?(E=0,x=v[F]):v[F]>K?(E=N[z+v[F]],x=D[j+v[F]]):(E=96,x=0),m=1<<A-Q,L=b=1<<T;do{p[X+(B>>Q)+(b-=m)]=S<<24|E<<16|x}while(0!==b);for(m=1<<A-1;B&m;)m>>=1;if(0!==m?(B&=m-1,B+=m):B=0,F++,0==--I[A]){if(A===P)break;A=t[n+v[F]]}if(A>M&&(B&k)!==w){for(0===Q&&(Q=M),X+=L,R=1<<(T=A-Q);T+Q<P&&!((R-=I[T+Q])<=0);)T++,R<<=1;if(O+=1<<T,e===u&&O>o||e===c&&O>a)return 1;p[w=B&k]=M<<24|T<<16|X-y}}return 0!==B&&(p[X+B]=A-Q<<24|64<<16),g.bits=M,0};var r=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=t(e)&&"function"!=typeof e)return{default:e};var i=n(r);if(i&&i.has(e))return i.get(e);var o={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&{}.hasOwnProperty.call(e,s)){var u=a?Object.getOwnPropertyDescriptor(e,s):null;u&&(u.get||u.set)?Object.defineProperty(o,s,u):o[s]=e[s]}return o.default=e,i&&i.set(e,o),o}(O());function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}var i=15,o=852,a=592,s=0,u=1,c=2,l=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],h=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],f=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],d=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64]}(G)),G));function a(e){return e&&e.__esModule?e:{default:e}}function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}var u=0,c=1,l=2,h=Q.Z_FINISH=4,f=Q.Z_BLOCK=5,d=Q.Z_TREES=6,_=Q.Z_OK=0,p=Q.Z_STREAM_END=1,y=Q.Z_NEED_DICT=2,v=Q.Z_STREAM_ERROR=-2,g=Q.Z_DATA_ERROR=-3,m=Q.Z_MEM_ERROR=-4,b=Q.Z_BUF_ERROR=-5,w=Q.Z_DEFLATED=8,k=1,X=2,K=3,S=4,E=5,x=6,C=7,A=8,F=9,L=10,P=11,M=12,T=13,R=14,B=15,D=16,I=17,U=18,Z=19,Y=20,q=21,J=22,$=23,ee=24,te=25,re=26,ne=27,ie=28,oe=29,ae=30,se=31,ue=852,ce=592,le=15;function he(e){return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)}function fe(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new t.Buf16(320),this.work=new t.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function de(e){var r;return e&&e.state?(r=e.state,e.total_in=e.total_out=r.total=0,e.msg="",r.wrap&&(e.adler=1&r.wrap),r.mode=k,r.last=0,r.havedict=0,r.dmax=32768,r.head=null,r.hold=0,r.bits=0,r.lencode=r.lendyn=new t.Buf32(ue),r.distcode=r.distdyn=new t.Buf32(ce),r.sane=1,r.back=-1,_):v}function _e(e){var t;return e&&e.state?((t=e.state).wsize=0,t.whave=0,t.wnext=0,de(e)):v}function pe(e,t){var r,n;return!e||!e.state||(n=e.state,t<0?(r=0,t=-t):(r=1+(t>>4),t<48&&(t&=15)),t&&(t<8||t>15))?v:(null!==n.window&&n.wbits!==t&&(n.window=null),n.wrap=r,n.wbits=t,_e(e))}function ye(e,t){var r,n;return e?(n=new fe,e.state=n,n.window=null,(r=pe(e,t))!==_&&(e.state=null),r):v}var ve,ge,me=!0;function be(e){if(me){var r;for(ve=new t.Buf32(512),ge=new t.Buf32(32),r=0;r<144;)e.lens[r++]=8;for(;r<256;)e.lens[r++]=9;for(;r<280;)e.lens[r++]=7;for(;r<288;)e.lens[r++]=8;for((0,o.default)(c,e.lens,0,288,ve,0,e.work,{bits:9}),r=0;r<32;)e.lens[r++]=5;(0,o.default)(l,e.lens,0,32,ge,0,e.work,{bits:5}),me=!1}e.lencode=ve,e.lenbits=9,e.distcode=ge,e.distbits=5}function we(e,r,n,i){var o,a=e.state;return null===a.window&&(a.wsize=1<<a.wbits,a.wnext=0,a.whave=0,a.window=new t.Buf8(a.wsize)),i>=a.wsize?(t.arraySet(a.window,r,n-a.wsize,a.wsize,0),a.wnext=0,a.whave=a.wsize):((o=a.wsize-a.wnext)>i&&(o=i),t.arraySet(a.window,r,n-i,o,a.wnext),(i-=o)?(t.arraySet(a.window,r,n-i,i,0),a.wnext=i,a.whave=a.wsize):(a.wnext+=o,a.wnext===a.wsize&&(a.wnext=0),a.whave<a.wsize&&(a.whave+=o))),0}return Q.inflateInfo="pako inflate (from Nodeca project)",Q}var Y,q,J={};function $(){return Y||(Y=1,e=J,Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}),J;var e}function ee(){return q||(q=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var t,r=Z(),n=(t=$())&&t.__esModule?t:{default:t};function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e,t,r){return t&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,a(n.key),n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function a(e){var t=function(e,t){if("object"!=i(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==i(t)?t:t+""}e.default=function(){return o((function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.strm=new n.default,this.chunkSize=102400,this.strm.output=new Uint8Array(this.chunkSize),(0,r.inflateInit)(this.strm)}),[{key:"setInput",value:function(e){e?(this.strm.input=e,this.strm.avail_in=this.strm.input.length,this.strm.next_in=0):(this.strm.input=null,this.strm.avail_in=0,this.strm.next_in=0)}},{key:"inflate",value:function(e){if(e>this.chunkSize&&(this.chunkSize=e,this.strm.output=new Uint8Array(this.chunkSize)),this.strm.next_out=0,this.strm.avail_out=e,(0,r.inflate)(this.strm,0)<0)throw new Error("zlib inflate failed");if(this.strm.next_out!=e)throw new Error("Incomplete zlib block");return new Uint8Array(this.strm.output.buffer,0,this.strm.next_out)}},{key:"reset",value:function(){(0,r.inflateReset)(this.strm)}}])}()}(T)),T}var te,re={},ne={},ie={};function oe(){if(te)return ie;function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)}te=1,Object.defineProperty(ie,"__esModule",{value:!0}),ie._tr_align=function(e){U(e,c<<1,3),N(e,b,C),function(e){16===e.bi_valid?(I(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):e.bi_valid>=8&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)}(e)},ie._tr_flush_block=function(e,t,r,s){var u,h,d=0;e.level>0?(e.strm.data_type===a&&(e.strm.data_type=function(e){var t,r=4093624447;for(t=0;t<=31;t++,r>>>=1)if(1&r&&0!==e.dyn_ltree[2*t])return i;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return o;for(t=32;t<f;t++)if(0!==e.dyn_ltree[2*t])return o;return i}(e)),q(e,e.l_desc),q(e,e.d_desc),d=function(e){var t;for(J(e,e.dyn_ltree,e.l_desc.max_code),J(e,e.dyn_dtree,e.d_desc.max_code),q(e,e.bl_desc),t=p-1;t>=3&&0===e.bl_tree[2*x[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t}(e),u=e.opt_len+3+7>>>3,(h=e.static_len+3+7>>>3)<=u&&(u=h)):u=h=r+5,r+4<=u&&-1!==t?re(e,t,r,s):e.strategy===n||h===u?(U(e,(c<<1)+(s?1:0),3),Y(e,C,A)):(U(e,(l<<1)+(s?1:0),3),function(e,t,r,n){var i;for(U(e,t-257,5),U(e,r-1,5),U(e,n-4,4),i=0;i<n;i++)U(e,e.bl_tree[2*x[i]+1],3);$(e,e.dyn_ltree,t-1),$(e,e.dyn_dtree,r-1)}(e,e.l_desc.max_code+1,e.d_desc.max_code+1,d+1),Y(e,e.dyn_ltree,e.dyn_dtree)),V(e),s&&W(e)},ie._tr_init=function(e){ee||(function(){var e,t,r,n,i,o=new Array(v+1);for(r=0,n=0;n<h-1;n++)for(P[n]=r,e=0;e<1<<K[n];e++)L[r++]=n;for(L[r-1]=n,i=0,n=0;n<16;n++)for(R[n]=i,e=0;e<1<<S[n];e++)F[i++]=n;for(i>>=7;n<_;n++)for(R[n]=i<<7,e=0;e<1<<S[n]-7;e++)F[256+i++]=n;for(t=0;t<=v;t++)o[t]=0;for(e=0;e<=143;)C[2*e+1]=8,e++,o[8]++;for(;e<=255;)C[2*e+1]=9,e++,o[9]++;for(;e<=279;)C[2*e+1]=7,e++,o[7]++;for(;e<=287;)C[2*e+1]=8,e++,o[8]++;for(H(C,d+1,o),e=0;e<_;e++)A[2*e+1]=5,A[2*e]=z(e,5);M=new B(C,K,f+1,d,v),T=new B(A,S,0,_,v),Q=new B(new Array(0),E,0,p,m)}(),ee=!0),e.l_desc=new D(e.dyn_ltree,M),e.d_desc=new D(e.dyn_dtree,T),e.bl_desc=new D(e.bl_tree,Q),e.bi_buf=0,e.bi_valid=0,V(e)},ie._tr_stored_block=re,ie._tr_tally=function(e,t,r){return e.pending_buf[e.d_buf+2*e.last_lit]=t>>>8&255,e.pending_buf[e.d_buf+2*e.last_lit+1]=255&t,e.pending_buf[e.l_buf+e.last_lit]=255&r,e.last_lit++,0===t?e.dyn_ltree[2*r]++:(e.matches++,t--,e.dyn_ltree[2*(L[r]+f+1)]++,e.dyn_dtree[2*j(t)]++),e.last_lit===e.lit_bufsize-1};var t=function(t,n){if(t&&t.__esModule)return t;if(null===t||"object"!=e(t)&&"function"!=typeof t)return{default:t};var i=r(n);if(i&&i.has(t))return i.get(t);var o={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in t)if("default"!==s&&{}.hasOwnProperty.call(t,s)){var u=a?Object.getOwnPropertyDescriptor(t,s):null;u&&(u.get||u.set)?Object.defineProperty(o,s,u):o[s]=t[s]}return o.default=t,i&&i.set(t,o),o}(O());function r(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(r=function(e){return e?n:t})(e)}var n=4,i=0,o=1,a=2;function s(e){for(var t=e.length;--t>=0;)e[t]=0}var u=0,c=1,l=2,h=29,f=256,d=f+1+h,_=30,p=19,y=2*d+1,v=15,g=16,m=7,b=256,w=16,k=17,X=18,K=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],S=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],E=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],x=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],C=new Array(2*(d+2));s(C);var A=new Array(2*_);s(A);var F=new Array(512);s(F);var L=new Array(256);s(L);var P=new Array(h);s(P);var M,T,Q,R=new Array(_);function B(e,t,r,n,i){this.static_tree=e,this.extra_bits=t,this.extra_base=r,this.elems=n,this.max_length=i,this.has_stree=e&&e.length}function D(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}function j(e){return e<256?F[e]:F[256+(e>>>7)]}function I(e,t){e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255}function U(e,t,r){e.bi_valid>g-r?(e.bi_buf|=t<<e.bi_valid&65535,I(e,e.bi_buf),e.bi_buf=t>>g-e.bi_valid,e.bi_valid+=r-g):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=r)}function N(e,t,r){U(e,r[2*t],r[2*t+1])}function z(e,t){var r=0;do{r|=1&e,e>>>=1,r<<=1}while(--t>0);return r>>>1}function H(e,t,r){var n,i,o=new Array(v+1),a=0;for(n=1;n<=v;n++)o[n]=a=a+r[n-1]<<1;for(i=0;i<=t;i++){var s=e[2*i+1];0!==s&&(e[2*i]=z(o[s]++,s))}}function V(e){var t;for(t=0;t<d;t++)e.dyn_ltree[2*t]=0;for(t=0;t<_;t++)e.dyn_dtree[2*t]=0;for(t=0;t<p;t++)e.bl_tree[2*t]=0;e.dyn_ltree[2*b]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0}function W(e){e.bi_valid>8?I(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0}function G(e,t,r,n){var i=2*t,o=2*r;return e[i]<e[o]||e[i]===e[o]&&n[t]<=n[r]}function Z(e,t,r){for(var n=e.heap[r],i=r<<1;i<=e.heap_len&&(i<e.heap_len&&G(t,e.heap[i+1],e.heap[i],e.depth)&&i++,!G(t,n,e.heap[i],e.depth));)e.heap[r]=e.heap[i],r=i,i<<=1;e.heap[r]=n}function Y(e,t,r){var n,i,o,a,s=0;if(0!==e.last_lit)do{n=e.pending_buf[e.d_buf+2*s]<<8|e.pending_buf[e.d_buf+2*s+1],i=e.pending_buf[e.l_buf+s],s++,0===n?N(e,i,t):(N(e,(o=L[i])+f+1,t),0!==(a=K[o])&&U(e,i-=P[o],a),N(e,o=j(--n),r),0!==(a=S[o])&&U(e,n-=R[o],a))}while(s<e.last_lit);N(e,b,t)}function q(e,t){var r,n,i,o=t.dyn_tree,a=t.stat_desc.static_tree,s=t.stat_desc.has_stree,u=t.stat_desc.elems,c=-1;for(e.heap_len=0,e.heap_max=y,r=0;r<u;r++)0!==o[2*r]?(e.heap[++e.heap_len]=c=r,e.depth[r]=0):o[2*r+1]=0;for(;e.heap_len<2;)o[2*(i=e.heap[++e.heap_len]=c<2?++c:0)]=1,e.depth[i]=0,e.opt_len--,s&&(e.static_len-=a[2*i+1]);for(t.max_code=c,r=e.heap_len>>1;r>=1;r--)Z(e,o,r);i=u;do{r=e.heap[1],e.heap[1]=e.heap[e.heap_len--],Z(e,o,1),n=e.heap[1],e.heap[--e.heap_max]=r,e.heap[--e.heap_max]=n,o[2*i]=o[2*r]+o[2*n],e.depth[i]=(e.depth[r]>=e.depth[n]?e.depth[r]:e.depth[n])+1,o[2*r+1]=o[2*n+1]=i,e.heap[1]=i++,Z(e,o,1)}while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],function(e,t){var r,n,i,o,a,s,u=t.dyn_tree,c=t.max_code,l=t.stat_desc.static_tree,h=t.stat_desc.has_stree,f=t.stat_desc.extra_bits,d=t.stat_desc.extra_base,_=t.stat_desc.max_length,p=0;for(o=0;o<=v;o++)e.bl_count[o]=0;for(u[2*e.heap[e.heap_max]+1]=0,r=e.heap_max+1;r<y;r++)(o=u[2*u[2*(n=e.heap[r])+1]+1]+1)>_&&(o=_,p++),u[2*n+1]=o,!(n>c)&&(e.bl_count[o]++,a=0,n>=d&&(a=f[n-d]),s=u[2*n],e.opt_len+=s*(o+a),h&&(e.static_len+=s*(l[2*n+1]+a)));if(0!==p){do{for(o=_-1;0===e.bl_count[o];)o--;e.bl_count[o]--,e.bl_count[o+1]+=2,e.bl_count[_]--,p-=2}while(p>0);for(o=_;0!==o;o--)for(n=e.bl_count[o];0!==n;)!((i=e.heap[--r])>c)&&(u[2*i+1]!==o&&(e.opt_len+=(o-u[2*i+1])*u[2*i],u[2*i+1]=o),n--)}}(e,t),H(o,c,e.bl_count)}function J(e,t,r){var n,i,o=-1,a=t[1],s=0,u=7,c=4;for(0===a&&(u=138,c=3),t[2*(r+1)+1]=65535,n=0;n<=r;n++)i=a,a=t[2*(n+1)+1],!(++s<u&&i===a)&&(s<c?e.bl_tree[2*i]+=s:0!==i?(i!==o&&e.bl_tree[2*i]++,e.bl_tree[2*w]++):s<=10?e.bl_tree[2*k]++:e.bl_tree[2*X]++,s=0,o=i,0===a?(u=138,c=3):i===a?(u=6,c=3):(u=7,c=4))}function $(e,t,r){var n,i,o=-1,a=t[1],s=0,u=7,c=4;for(0===a&&(u=138,c=3),n=0;n<=r;n++)if(i=a,a=t[2*(n+1)+1],!(++s<u&&i===a)){if(s<c)do{N(e,i,e.bl_tree)}while(0!=--s);else 0!==i?(i!==o&&(N(e,i,e.bl_tree),s--),N(e,w,e.bl_tree),U(e,s-3,2)):s<=10?(N(e,k,e.bl_tree),U(e,s-3,3)):(N(e,X,e.bl_tree),U(e,s-11,7));s=0,o=i,0===a?(u=138,c=3):i===a?(u=6,c=3):(u=7,c=4)}}s(R);var ee=!1;function re(e,r,n,i){U(e,(u<<1)+(i?1:0),3),function(e,r,n){W(e),I(e,n),I(e,~n),t.arraySet(e.pending_buf,e.window,r,n,e.pending),e.pending+=n}(e,r,n)}return ie}var ae,se,ue,ce={};function le(){if(se)return ne;function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)}se=1,Object.defineProperty(ne,"__esModule",{value:!0}),ne.Z_UNKNOWN=ne.Z_STREAM_ERROR=ne.Z_STREAM_END=ne.Z_RLE=ne.Z_PARTIAL_FLUSH=ne.Z_OK=ne.Z_NO_FLUSH=ne.Z_HUFFMAN_ONLY=ne.Z_FULL_FLUSH=ne.Z_FIXED=ne.Z_FINISH=ne.Z_FILTERED=ne.Z_DEFLATED=ne.Z_DEFAULT_STRATEGY=ne.Z_DEFAULT_COMPRESSION=ne.Z_DATA_ERROR=ne.Z_BUF_ERROR=ne.Z_BLOCK=void 0,ne.deflate=function(e,t){var n,o,a,s;if(!e||!e.state||t>_||t<0)return e?te(e,v):v;if(o=e.state,!e.output||!e.input&&0!==e.avail_in||o.status===Z&&t!==d)return te(e,0===e.avail_out?m:v);if(o.strm=e,n=o.last_flush,o.last_flush=t,o.status===U)if(2===o.wrap)e.adler=0,he(o,31),he(o,139),he(o,8),o.gzhead?(he(o,(o.gzhead.text?1:0)+(o.gzhead.hcrc?2:0)+(o.gzhead.extra?4:0)+(o.gzhead.name?8:0)+(o.gzhead.comment?16:0)),he(o,255&o.gzhead.time),he(o,o.gzhead.time>>8&255),he(o,o.gzhead.time>>16&255),he(o,o.gzhead.time>>24&255),he(o,9===o.level?2:o.strategy>=k||o.level<2?4:0),he(o,255&o.gzhead.os),o.gzhead.extra&&o.gzhead.extra.length&&(he(o,255&o.gzhead.extra.length),he(o,o.gzhead.extra.length>>8&255)),o.gzhead.hcrc&&(e.adler=(0,i.default)(e.adler,o.pending_buf,o.pending,0)),o.gzindex=0,o.status=z):(he(o,0),he(o,0),he(o,0),he(o,0),he(o,0),he(o,9===o.level?2:o.strategy>=k||o.level<2?4:0),he(o,ee),o.status=G);else{var u=x+(o.w_bits-8<<4)<<8;u|=(o.strategy>=k||o.level<2?0:o.level<6?1:6===o.level?2:3)<<6,0!==o.strstart&&(u|=I),u+=31-u%31,o.status=G,fe(o,u),0!==o.strstart&&(fe(o,e.adler>>>16),fe(o,65535&e.adler)),e.adler=1}if(o.status===z)if(o.gzhead.extra){for(a=o.pending;o.gzindex<(65535&o.gzhead.extra.length)&&(o.pending!==o.pending_buf_size||(o.gzhead.hcrc&&o.pending>a&&(e.adler=(0,i.default)(e.adler,o.pending_buf,o.pending-a,a)),ue(e),a=o.pending,o.pending!==o.pending_buf_size));)he(o,255&o.gzhead.extra[o.gzindex]),o.gzindex++;o.gzhead.hcrc&&o.pending>a&&(e.adler=(0,i.default)(e.adler,o.pending_buf,o.pending-a,a)),o.gzindex===o.gzhead.extra.length&&(o.gzindex=0,o.status=H)}else o.status=H;if(o.status===H)if(o.gzhead.name){a=o.pending;do{if(o.pending===o.pending_buf_size&&(o.gzhead.hcrc&&o.pending>a&&(e.adler=(0,i.default)(e.adler,o.pending_buf,o.pending-a,a)),ue(e),a=o.pending,o.pending===o.pending_buf_size)){s=1;break}s=o.gzindex<o.gzhead.name.length?255&o.gzhead.name.charCodeAt(o.gzindex++):0,he(o,s)}while(0!==s);o.gzhead.hcrc&&o.pending>a&&(e.adler=(0,i.default)(e.adler,o.pending_buf,o.pending-a,a)),0===s&&(o.gzindex=0,o.status=V)}else o.status=V;if(o.status===V)if(o.gzhead.comment){a=o.pending;do{if(o.pending===o.pending_buf_size&&(o.gzhead.hcrc&&o.pending>a&&(e.adler=(0,i.default)(e.adler,o.pending_buf,o.pending-a,a)),ue(e),a=o.pending,o.pending===o.pending_buf_size)){s=1;break}s=o.gzindex<o.gzhead.comment.length?255&o.gzhead.comment.charCodeAt(o.gzindex++):0,he(o,s)}while(0!==s);o.gzhead.hcrc&&o.pending>a&&(e.adler=(0,i.default)(e.adler,o.pending_buf,o.pending-a,a)),0===s&&(o.status=W)}else o.status=W;if(o.status===W&&(o.gzhead.hcrc?(o.pending+2>o.pending_buf_size&&ue(e),o.pending+2<=o.pending_buf_size&&(he(o,255&e.adler),he(o,e.adler>>8&255),e.adler=0,o.status=G)):o.status=G),0!==o.pending){if(ue(e),0===e.avail_out)return o.last_flush=-1,p}else if(0===e.avail_in&&re(t)<=re(n)&&t!==d)return te(e,m);if(o.status===Z&&0!==e.avail_in)return te(e,m);if(0!==e.avail_in||0!==o.lookahead||t!==l&&o.status!==Z){var g=o.strategy===k?function(e,t){for(var n;;){if(0===e.lookahead&&(pe(e),0===e.lookahead)){if(t===l)return Y;break}if(e.match_length=0,n=r._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,n&&(le(e,!1),0===e.strm.avail_out))return Y}return e.insert=0,t===d?(le(e,!0),0===e.strm.avail_out?J:$):e.last_lit&&(le(e,!1),0===e.strm.avail_out)?Y:q}(o,t):o.strategy===X?function(e,t){for(var n,i,o,a,s=e.window;;){if(e.lookahead<=B){if(pe(e),e.lookahead<=B&&t===l)return Y;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=R&&e.strstart>0&&((i=s[o=e.strstart-1])===s[++o]&&i===s[++o]&&i===s[++o])){a=e.strstart+B;do{}while(i===s[++o]&&i===s[++o]&&i===s[++o]&&i===s[++o]&&i===s[++o]&&i===s[++o]&&i===s[++o]&&i===s[++o]&&o<a);e.match_length=B-(a-o),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=R?(n=r._tr_tally(e,1,e.match_length-R),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(n=r._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),n&&(le(e,!1),0===e.strm.avail_out))return Y}return e.insert=0,t===d?(le(e,!0),0===e.strm.avail_out?J:$):e.last_lit&&(le(e,!1),0===e.strm.avail_out)?Y:q}(o,t):c[o.level].func(o,t);if((g===J||g===$)&&(o.status=Z),g===Y||g===J)return 0===e.avail_out&&(o.last_flush=-1),p;if(g===q&&(t===h?r._tr_align(o):t!==_&&(r._tr_stored_block(o,0,0,!1),t===f&&(ie(o.head),0===o.lookahead&&(o.strstart=0,o.block_start=0,o.insert=0))),ue(e),0===e.avail_out))return o.last_flush=-1,p}return t!==d?p:o.wrap<=0?y:(2===o.wrap?(he(o,255&e.adler),he(o,e.adler>>8&255),he(o,e.adler>>16&255),he(o,e.adler>>24&255),he(o,255&e.total_in),he(o,e.total_in>>8&255),he(o,e.total_in>>16&255),he(o,e.total_in>>24&255)):(fe(o,e.adler>>>16),fe(o,65535&e.adler)),ue(e),o.wrap>0&&(o.wrap=-o.wrap),0!==o.pending?p:y)},ne.deflateEnd=function(e){var t;return e&&e.state?(t=e.state.status)!==U&&t!==z&&t!==H&&t!==V&&t!==W&&t!==G&&t!==Z?te(e,v):(e.state=null,t===G?te(e,g):p):v},ne.deflateInfo=void 0,ne.deflateInit=function(e,t){return ke(e,t,x,A,F,S)},ne.deflateInit2=ke,ne.deflateReset=we,ne.deflateResetKeep=be,ne.deflateSetDictionary=function(e,r){var i,o,a,s,u,c,l,h,f=r.length;if(!e||!e.state||(i=e.state,2===(s=i.wrap)||1===s&&i.status!==U||i.lookahead))return v;for(1===s&&(e.adler=(0,n.default)(e.adler,r,f,0)),i.wrap=0,f>=i.w_size&&(0===s&&(ie(i.head),i.strstart=0,i.block_start=0,i.insert=0),h=new t.Buf8(i.w_size),t.arraySet(h,r,f-i.w_size,i.w_size,0),r=h,f=i.w_size),u=e.avail_in,c=e.next_in,l=e.input,e.avail_in=f,e.next_in=0,e.input=r,pe(i);i.lookahead>=R;){o=i.strstart,a=i.lookahead-(R-1);do{i.ins_h=(i.ins_h<<i.hash_shift^i.window[o+R-1])&i.hash_mask,i.prev[o&i.w_mask]=i.head[i.ins_h],i.head[i.ins_h]=o,o++}while(--a);i.strstart=o,i.lookahead=R-1,pe(i)}return i.strstart+=i.lookahead,i.block_start=i.strstart,i.insert=i.lookahead,i.lookahead=0,i.match_length=i.prev_length=R-1,i.match_available=0,e.next_in=c,e.input=l,e.avail_in=u,i.wrap=s,p},ne.deflateSetHeader=function(e,t){return e&&e.state&&2===e.state.wrap?(e.state.gzhead=t,p):v};var t=u(O()),r=u(oe()),n=a(j()),i=a(N()),o=a(function(){return ae||(ae=1,e=ce,Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,e.default={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}),ce;var e}());function a(e){return e&&e.__esModule?e:{default:e}}function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}function u(t,r){if(t&&t.__esModule)return t;if(null===t||"object"!=e(t)&&"function"!=typeof t)return{default:t};var n=s(r);if(n&&n.has(t))return n.get(t);var i={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in t)if("default"!==a&&{}.hasOwnProperty.call(t,a)){var u=o?Object.getOwnPropertyDescriptor(t,a):null;u&&(u.get||u.set)?Object.defineProperty(i,a,u):i[a]=t[a]}return i.default=t,n&&n.set(t,i),i}var c,l=ne.Z_NO_FLUSH=0,h=ne.Z_PARTIAL_FLUSH=1,f=ne.Z_FULL_FLUSH=3,d=ne.Z_FINISH=4,_=ne.Z_BLOCK=5,p=ne.Z_OK=0,y=ne.Z_STREAM_END=1,v=ne.Z_STREAM_ERROR=-2,g=ne.Z_DATA_ERROR=-3,m=ne.Z_BUF_ERROR=-5,b=ne.Z_DEFAULT_COMPRESSION=-1,w=ne.Z_FILTERED=1,k=ne.Z_HUFFMAN_ONLY=2,X=ne.Z_RLE=3,K=ne.Z_FIXED=4,S=ne.Z_DEFAULT_STRATEGY=0,E=ne.Z_UNKNOWN=2,x=ne.Z_DEFLATED=8,C=9,A=15,F=8,L=286,P=30,M=19,T=2*L+1,Q=15,R=3,B=258,D=B+R+1,I=32,U=42,z=69,H=73,V=91,W=103,G=113,Z=666,Y=1,q=2,J=3,$=4,ee=3;function te(e,t){return e.msg=o.default[t],t}function re(e){return(e<<1)-(e>4?9:0)}function ie(e){for(var t=e.length;--t>=0;)e[t]=0}function ue(e){var r=e.state,n=r.pending;n>e.avail_out&&(n=e.avail_out),0!==n&&(t.arraySet(e.output,r.pending_buf,r.pending_out,n,e.next_out),e.next_out+=n,r.pending_out+=n,e.total_out+=n,e.avail_out-=n,r.pending-=n,0===r.pending&&(r.pending_out=0))}function le(e,t){r._tr_flush_block(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,ue(e.strm)}function he(e,t){e.pending_buf[e.pending++]=t}function fe(e,t){e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t}function de(e,r,o,a){var s=e.avail_in;return s>a&&(s=a),0===s?0:(e.avail_in-=s,t.arraySet(r,e.input,e.next_in,s,o),1===e.state.wrap?e.adler=(0,n.default)(e.adler,r,s,o):2===e.state.wrap&&(e.adler=(0,i.default)(e.adler,r,s,o)),e.next_in+=s,e.total_in+=s,s)}function _e(e,t){var r,n,i=e.max_chain_length,o=e.strstart,a=e.prev_length,s=e.nice_match,u=e.strstart>e.w_size-D?e.strstart-(e.w_size-D):0,c=e.window,l=e.w_mask,h=e.prev,f=e.strstart+B,d=c[o+a-1],_=c[o+a];e.prev_length>=e.good_match&&(i>>=2),s>e.lookahead&&(s=e.lookahead);do{if(c[(r=t)+a]===_&&c[r+a-1]===d&&c[r]===c[o]&&c[++r]===c[o+1]){o+=2,r++;do{}while(c[++o]===c[++r]&&c[++o]===c[++r]&&c[++o]===c[++r]&&c[++o]===c[++r]&&c[++o]===c[++r]&&c[++o]===c[++r]&&c[++o]===c[++r]&&c[++o]===c[++r]&&o<f);if(n=B-(f-o),o=f-B,n>a){if(e.match_start=t,a=n,n>=s)break;d=c[o+a-1],_=c[o+a]}}}while((t=h[t&l])>u&&0!=--i);return a<=e.lookahead?a:e.lookahead}function pe(e){var r,n,i,o,a,s=e.w_size;do{if(o=e.window_size-e.lookahead-e.strstart,e.strstart>=s+(s-D)){t.arraySet(e.window,e.window,s,s,0),e.match_start-=s,e.strstart-=s,e.block_start-=s,r=n=e.hash_size;do{i=e.head[--r],e.head[r]=i>=s?i-s:0}while(--n);r=n=s;do{i=e.prev[--r],e.prev[r]=i>=s?i-s:0}while(--n);o+=s}if(0===e.strm.avail_in)break;if(n=de(e.strm,e.window,e.strstart+e.lookahead,o),e.lookahead+=n,e.lookahead+e.insert>=R)for(a=e.strstart-e.insert,e.ins_h=e.window[a],e.ins_h=(e.ins_h<<e.hash_shift^e.window[a+1])&e.hash_mask;e.insert&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[a+R-1])&e.hash_mask,e.prev[a&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=a,a++,e.insert--,!(e.lookahead+e.insert<R)););}while(e.lookahead<D&&0!==e.strm.avail_in)}function ye(e,t){for(var n,i;;){if(e.lookahead<D){if(pe(e),e.lookahead<D&&t===l)return Y;if(0===e.lookahead)break}if(n=0,e.lookahead>=R&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+R-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==n&&e.strstart-n<=e.w_size-D&&(e.match_length=_e(e,n)),e.match_length>=R)if(i=r._tr_tally(e,e.strstart-e.match_start,e.match_length-R),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=R){e.match_length--;do{e.strstart++,e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+R-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart}while(0!=--e.match_length);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+1])&e.hash_mask;else i=r._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(i&&(le(e,!1),0===e.strm.avail_out))return Y}return e.insert=e.strstart<R-1?e.strstart:R-1,t===d?(le(e,!0),0===e.strm.avail_out?J:$):e.last_lit&&(le(e,!1),0===e.strm.avail_out)?Y:q}function ve(e,t){for(var n,i,o;;){if(e.lookahead<D){if(pe(e),e.lookahead<D&&t===l)return Y;if(0===e.lookahead)break}if(n=0,e.lookahead>=R&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+R-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=R-1,0!==n&&e.prev_length<e.max_lazy_match&&e.strstart-n<=e.w_size-D&&(e.match_length=_e(e,n),e.match_length<=5&&(e.strategy===w||e.match_length===R&&e.strstart-e.match_start>4096)&&(e.match_length=R-1)),e.prev_length>=R&&e.match_length<=e.prev_length){o=e.strstart+e.lookahead-R,i=r._tr_tally(e,e.strstart-1-e.prev_match,e.prev_length-R),e.lookahead-=e.prev_length-1,e.prev_length-=2;do{++e.strstart<=o&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+R-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart)}while(0!=--e.prev_length);if(e.match_available=0,e.match_length=R-1,e.strstart++,i&&(le(e,!1),0===e.strm.avail_out))return Y}else if(e.match_available){if((i=r._tr_tally(e,0,e.window[e.strstart-1]))&&le(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return Y}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(i=r._tr_tally(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<R-1?e.strstart:R-1,t===d?(le(e,!0),0===e.strm.avail_out?J:$):e.last_lit&&(le(e,!1),0===e.strm.avail_out)?Y:q}function ge(e,t,r,n,i){this.good_length=e,this.max_lazy=t,this.nice_length=r,this.max_chain=n,this.func=i}function me(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=x,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new t.Buf16(2*T),this.dyn_dtree=new t.Buf16(2*(2*P+1)),this.bl_tree=new t.Buf16(2*(2*M+1)),ie(this.dyn_ltree),ie(this.dyn_dtree),ie(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new t.Buf16(Q+1),this.heap=new t.Buf16(2*L+1),ie(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new t.Buf16(2*L+1),ie(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function be(e){var t;return e&&e.state?(e.total_in=e.total_out=0,e.data_type=E,(t=e.state).pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=t.wrap?U:G,e.adler=2===t.wrap?0:1,t.last_flush=l,r._tr_init(t),p):te(e,v)}function we(e){var t=be(e);return t===p&&function(e){e.window_size=2*e.w_size,ie(e.head),e.max_lazy_match=c[e.level].max_lazy,e.good_match=c[e.level].good_length,e.nice_match=c[e.level].nice_length,e.max_chain_length=c[e.level].max_chain,e.strstart=0,e.block_start=0,e.lookahead=0,e.insert=0,e.match_length=e.prev_length=R-1,e.match_available=0,e.ins_h=0}(e.state),t}function ke(e,r,n,i,o,a){if(!e)return v;var s=1;if(r===b&&(r=6),i<0?(s=0,i=-i):i>15&&(s=2,i-=16),o<1||o>C||n!==x||i<8||i>15||r<0||r>9||a<0||a>K)return te(e,v);8===i&&(i=9);var u=new me;return e.state=u,u.strm=e,u.wrap=s,u.gzhead=null,u.w_bits=i,u.w_size=1<<u.w_bits,u.w_mask=u.w_size-1,u.hash_bits=o+7,u.hash_size=1<<u.hash_bits,u.hash_mask=u.hash_size-1,u.hash_shift=~~((u.hash_bits+R-1)/R),u.window=new t.Buf8(2*u.w_size),u.head=new t.Buf16(u.hash_size),u.prev=new t.Buf16(u.w_size),u.lit_bufsize=1<<o+6,u.pending_buf_size=4*u.lit_bufsize,u.pending_buf=new t.Buf8(u.pending_buf_size),u.d_buf=1*u.lit_bufsize,u.l_buf=3*u.lit_bufsize,u.level=r,u.strategy=a,u.method=n,we(e)}return c=[new ge(0,0,0,0,(function(e,t){var r=65535;for(r>e.pending_buf_size-5&&(r=e.pending_buf_size-5);;){if(e.lookahead<=1){if(pe(e),0===e.lookahead&&t===l)return Y;if(0===e.lookahead)break}e.strstart+=e.lookahead,e.lookahead=0;var n=e.block_start+r;if((0===e.strstart||e.strstart>=n)&&(e.lookahead=e.strstart-n,e.strstart=n,le(e,!1),0===e.strm.avail_out)||e.strstart-e.block_start>=e.w_size-D&&(le(e,!1),0===e.strm.avail_out))return Y}return e.insert=0,t===d?(le(e,!0),0===e.strm.avail_out?J:$):(e.strstart>e.block_start&&(le(e,!1),e.strm.avail_out),Y)})),new ge(4,4,8,4,ye),new ge(4,5,16,8,ye),new ge(4,6,32,32,ye),new ge(4,4,16,16,ve),new ge(8,16,32,32,ve),new ge(8,16,128,128,ve),new ge(8,32,128,256,ve),new ge(32,128,258,1024,ve),new ge(32,258,258,4096,ve)],ne.deflateInfo="pako deflate (from Nodeca project)",ne}function he(){return ue||(ue=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var t,r=le(),n=(t=$())&&t.__esModule?t:{default:t};function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e,t,r){return t&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,a(n.key),n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function a(e){var t=function(e,t){if("object"!=i(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==i(t)?t:t+""}e.default=function(){return o((function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.strm=new n.default,this.chunkSize=102400,this.outputBuffer=new Uint8Array(this.chunkSize),(0,r.deflateInit)(this.strm,r.Z_DEFAULT_COMPRESSION)}),[{key:"deflate",value:function(e){this.strm.input=e,this.strm.avail_in=this.strm.input.length,this.strm.next_in=0,this.strm.output=this.outputBuffer,this.strm.avail_out=this.chunkSize,this.strm.next_out=0;var t=(0,r.deflate)(this.strm,r.Z_FULL_FLUSH),n=new Uint8Array(this.strm.output.buffer,0,this.strm.next_out);if(t<0)throw new Error("zlib deflate failed");if(this.strm.avail_in>0){var i=[n],o=n.length;do{if(this.strm.output=new Uint8Array(this.chunkSize),this.strm.next_out=0,this.strm.avail_out=this.chunkSize,(t=(0,r.deflate)(this.strm,r.Z_FULL_FLUSH))<0)throw new Error("zlib deflate failed");var a=new Uint8Array(this.strm.output.buffer,0,this.strm.next_out);o+=a.length,i.push(a)}while(this.strm.avail_in>0);for(var s=new Uint8Array(o),u=0,c=0;c<i.length;c++)s.set(i[c],u),u+=i[c].length;n=s}return this.strm.input=null,this.strm.avail_in=0,this.strm.next_in=0,n}}])}()}(re)),re}var fe,de={},_e={},pe={};function ye(){return fe||(fe=1,e=pe,Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,e.default={XK_VoidSymbol:16777215,XK_BackSpace:65288,XK_Tab:65289,XK_Linefeed:65290,XK_Clear:65291,XK_Return:65293,XK_Pause:65299,XK_Scroll_Lock:65300,XK_Sys_Req:65301,XK_Escape:65307,XK_Delete:65535,XK_Multi_key:65312,XK_Codeinput:65335,XK_SingleCandidate:65340,XK_MultipleCandidate:65341,XK_PreviousCandidate:65342,XK_Kanji:65313,XK_Muhenkan:65314,XK_Henkan_Mode:65315,XK_Henkan:65315,XK_Romaji:65316,XK_Hiragana:65317,XK_Katakana:65318,XK_Hiragana_Katakana:65319,XK_Zenkaku:65320,XK_Hankaku:65321,XK_Zenkaku_Hankaku:65322,XK_Touroku:65323,XK_Massyo:65324,XK_Kana_Lock:65325,XK_Kana_Shift:65326,XK_Eisu_Shift:65327,XK_Eisu_toggle:65328,XK_Kanji_Bangou:65335,XK_Zen_Koho:65341,XK_Mae_Koho:65342,XK_Home:65360,XK_Left:65361,XK_Up:65362,XK_Right:65363,XK_Down:65364,XK_Prior:65365,XK_Page_Up:65365,XK_Next:65366,XK_Page_Down:65366,XK_End:65367,XK_Begin:65368,XK_Select:65376,XK_Print:65377,XK_Execute:65378,XK_Insert:65379,XK_Undo:65381,XK_Redo:65382,XK_Menu:65383,XK_Find:65384,XK_Cancel:65385,XK_Help:65386,XK_Break:65387,XK_Mode_switch:65406,XK_script_switch:65406,XK_Num_Lock:65407,XK_KP_Space:65408,XK_KP_Tab:65417,XK_KP_Enter:65421,XK_KP_F1:65425,XK_KP_F2:65426,XK_KP_F3:65427,XK_KP_F4:65428,XK_KP_Home:65429,XK_KP_Left:65430,XK_KP_Up:65431,XK_KP_Right:65432,XK_KP_Down:65433,XK_KP_Prior:65434,XK_KP_Page_Up:65434,XK_KP_Next:65435,XK_KP_Page_Down:65435,XK_KP_End:65436,XK_KP_Begin:65437,XK_KP_Insert:65438,XK_KP_Delete:65439,XK_KP_Equal:65469,XK_KP_Multiply:65450,XK_KP_Add:65451,XK_KP_Separator:65452,XK_KP_Subtract:65453,XK_KP_Decimal:65454,XK_KP_Divide:65455,XK_KP_0:65456,XK_KP_1:65457,XK_KP_2:65458,XK_KP_3:65459,XK_KP_4:65460,XK_KP_5:65461,XK_KP_6:65462,XK_KP_7:65463,XK_KP_8:65464,XK_KP_9:65465,XK_F1:65470,XK_F2:65471,XK_F3:65472,XK_F4:65473,XK_F5:65474,XK_F6:65475,XK_F7:65476,XK_F8:65477,XK_F9:65478,XK_F10:65479,XK_F11:65480,XK_L1:65480,XK_F12:65481,XK_L2:65481,XK_F13:65482,XK_L3:65482,XK_F14:65483,XK_L4:65483,XK_F15:65484,XK_L5:65484,XK_F16:65485,XK_L6:65485,XK_F17:65486,XK_L7:65486,XK_F18:65487,XK_L8:65487,XK_F19:65488,XK_L9:65488,XK_F20:65489,XK_L10:65489,XK_F21:65490,XK_R1:65490,XK_F22:65491,XK_R2:65491,XK_F23:65492,XK_R3:65492,XK_F24:65493,XK_R4:65493,XK_F25:65494,XK_R5:65494,XK_F26:65495,XK_R6:65495,XK_F27:65496,XK_R7:65496,XK_F28:65497,XK_R8:65497,XK_F29:65498,XK_R9:65498,XK_F30:65499,XK_R10:65499,XK_F31:65500,XK_R11:65500,XK_F32:65501,XK_R12:65501,XK_F33:65502,XK_R13:65502,XK_F34:65503,XK_R14:65503,XK_F35:65504,XK_R15:65504,XK_Shift_L:65505,XK_Shift_R:65506,XK_Control_L:65507,XK_Control_R:65508,XK_Caps_Lock:65509,XK_Shift_Lock:65510,XK_Meta_L:65511,XK_Meta_R:65512,XK_Alt_L:65513,XK_Alt_R:65514,XK_Super_L:65515,XK_Super_R:65516,XK_Hyper_L:65517,XK_Hyper_R:65518,XK_ISO_Level3_Shift:65027,XK_ISO_Next_Group:65032,XK_ISO_Prev_Group:65034,XK_ISO_First_Group:65036,XK_ISO_Last_Group:65038,XK_space:32,XK_exclam:33,XK_quotedbl:34,XK_numbersign:35,XK_dollar:36,XK_percent:37,XK_ampersand:38,XK_apostrophe:39,XK_quoteright:39,XK_parenleft:40,XK_parenright:41,XK_asterisk:42,XK_plus:43,XK_comma:44,XK_minus:45,XK_period:46,XK_slash:47,XK_0:48,XK_1:49,XK_2:50,XK_3:51,XK_4:52,XK_5:53,XK_6:54,XK_7:55,XK_8:56,XK_9:57,XK_colon:58,XK_semicolon:59,XK_less:60,XK_equal:61,XK_greater:62,XK_question:63,XK_at:64,XK_A:65,XK_B:66,XK_C:67,XK_D:68,XK_E:69,XK_F:70,XK_G:71,XK_H:72,XK_I:73,XK_J:74,XK_K:75,XK_L:76,XK_M:77,XK_N:78,XK_O:79,XK_P:80,XK_Q:81,XK_R:82,XK_S:83,XK_T:84,XK_U:85,XK_V:86,XK_W:87,XK_X:88,XK_Y:89,XK_Z:90,XK_bracketleft:91,XK_backslash:92,XK_bracketright:93,XK_asciicircum:94,XK_underscore:95,XK_grave:96,XK_quoteleft:96,XK_a:97,XK_b:98,XK_c:99,XK_d:100,XK_e:101,XK_f:102,XK_g:103,XK_h:104,XK_i:105,XK_j:106,XK_k:107,XK_l:108,XK_m:109,XK_n:110,XK_o:111,XK_p:112,XK_q:113,XK_r:114,XK_s:115,XK_t:116,XK_u:117,XK_v:118,XK_w:119,XK_x:120,XK_y:121,XK_z:122,XK_braceleft:123,XK_bar:124,XK_braceright:125,XK_asciitilde:126,XK_nobreakspace:160,XK_exclamdown:161,XK_cent:162,XK_sterling:163,XK_currency:164,XK_yen:165,XK_brokenbar:166,XK_section:167,XK_diaeresis:168,XK_copyright:169,XK_ordfeminine:170,XK_guillemotleft:171,XK_notsign:172,XK_hyphen:173,XK_registered:174,XK_macron:175,XK_degree:176,XK_plusminus:177,XK_twosuperior:178,XK_threesuperior:179,XK_acute:180,XK_mu:181,XK_paragraph:182,XK_periodcentered:183,XK_cedilla:184,XK_onesuperior:185,XK_masculine:186,XK_guillemotright:187,XK_onequarter:188,XK_onehalf:189,XK_threequarters:190,XK_questiondown:191,XK_Agrave:192,XK_Aacute:193,XK_Acircumflex:194,XK_Atilde:195,XK_Adiaeresis:196,XK_Aring:197,XK_AE:198,XK_Ccedilla:199,XK_Egrave:200,XK_Eacute:201,XK_Ecircumflex:202,XK_Ediaeresis:203,XK_Igrave:204,XK_Iacute:205,XK_Icircumflex:206,XK_Idiaeresis:207,XK_ETH:208,XK_Eth:208,XK_Ntilde:209,XK_Ograve:210,XK_Oacute:211,XK_Ocircumflex:212,XK_Otilde:213,XK_Odiaeresis:214,XK_multiply:215,XK_Oslash:216,XK_Ooblique:216,XK_Ugrave:217,XK_Uacute:218,XK_Ucircumflex:219,XK_Udiaeresis:220,XK_Yacute:221,XK_THORN:222,XK_Thorn:222,XK_ssharp:223,XK_agrave:224,XK_aacute:225,XK_acircumflex:226,XK_atilde:227,XK_adiaeresis:228,XK_aring:229,XK_ae:230,XK_ccedilla:231,XK_egrave:232,XK_eacute:233,XK_ecircumflex:234,XK_ediaeresis:235,XK_igrave:236,XK_iacute:237,XK_icircumflex:238,XK_idiaeresis:239,XK_eth:240,XK_ntilde:241,XK_ograve:242,XK_oacute:243,XK_ocircumflex:244,XK_otilde:245,XK_odiaeresis:246,XK_division:247,XK_oslash:248,XK_ooblique:248,XK_ugrave:249,XK_uacute:250,XK_ucircumflex:251,XK_udiaeresis:252,XK_yacute:253,XK_thorn:254,XK_ydiaeresis:255,XK_Hangul:65329,XK_Hangul_Hanja:65332,XK_Hangul_Jeonja:65336,XF86XK_ModeLock:269025025,XF86XK_MonBrightnessUp:269025026,XF86XK_MonBrightnessDown:269025027,XF86XK_KbdLightOnOff:269025028,XF86XK_KbdBrightnessUp:269025029,XF86XK_KbdBrightnessDown:269025030,XF86XK_Standby:269025040,XF86XK_AudioLowerVolume:269025041,XF86XK_AudioMute:269025042,XF86XK_AudioRaiseVolume:269025043,XF86XK_AudioPlay:269025044,XF86XK_AudioStop:269025045,XF86XK_AudioPrev:269025046,XF86XK_AudioNext:269025047,XF86XK_HomePage:269025048,XF86XK_Mail:269025049,XF86XK_Start:269025050,XF86XK_Search:269025051,XF86XK_AudioRecord:269025052,XF86XK_Calculator:269025053,XF86XK_Memo:269025054,XF86XK_ToDoList:269025055,XF86XK_Calendar:269025056,XF86XK_PowerDown:269025057,XF86XK_ContrastAdjust:269025058,XF86XK_RockerUp:269025059,XF86XK_RockerDown:269025060,XF86XK_RockerEnter:269025061,XF86XK_Back:269025062,XF86XK_Forward:269025063,XF86XK_Stop:269025064,XF86XK_Refresh:269025065,XF86XK_PowerOff:269025066,XF86XK_WakeUp:269025067,XF86XK_Eject:269025068,XF86XK_ScreenSaver:269025069,XF86XK_WWW:269025070,XF86XK_Sleep:269025071,XF86XK_Favorites:269025072,XF86XK_AudioPause:269025073,XF86XK_AudioMedia:269025074,XF86XK_MyComputer:269025075,XF86XK_VendorHome:269025076,XF86XK_LightBulb:269025077,XF86XK_Shop:269025078,XF86XK_History:269025079,XF86XK_OpenURL:269025080,XF86XK_AddFavorite:269025081,XF86XK_HotLinks:269025082,XF86XK_BrightnessAdjust:269025083,XF86XK_Finance:269025084,XF86XK_Community:269025085,XF86XK_AudioRewind:269025086,XF86XK_BackForward:269025087,XF86XK_Launch0:269025088,XF86XK_Launch1:269025089,XF86XK_Launch2:269025090,XF86XK_Launch3:269025091,XF86XK_Launch4:269025092,XF86XK_Launch5:269025093,XF86XK_Launch6:269025094,XF86XK_Launch7:269025095,XF86XK_Launch8:269025096,XF86XK_Launch9:269025097,XF86XK_LaunchA:269025098,XF86XK_LaunchB:269025099,XF86XK_LaunchC:269025100,XF86XK_LaunchD:269025101,XF86XK_LaunchE:269025102,XF86XK_LaunchF:269025103,XF86XK_ApplicationLeft:269025104,XF86XK_ApplicationRight:269025105,XF86XK_Book:269025106,XF86XK_CD:269025107,XF86XK_Calculater:269025108,XF86XK_Clear:269025109,XF86XK_Close:269025110,XF86XK_Copy:269025111,XF86XK_Cut:269025112,XF86XK_Display:269025113,XF86XK_DOS:269025114,XF86XK_Documents:269025115,XF86XK_Excel:269025116,XF86XK_Explorer:269025117,XF86XK_Game:269025118,XF86XK_Go:269025119,XF86XK_iTouch:269025120,XF86XK_LogOff:269025121,XF86XK_Market:269025122,XF86XK_Meeting:269025123,XF86XK_MenuKB:269025125,XF86XK_MenuPB:269025126,XF86XK_MySites:269025127,XF86XK_New:269025128,XF86XK_News:269025129,XF86XK_OfficeHome:269025130,XF86XK_Open:269025131,XF86XK_Option:*********,XF86XK_Paste:*********,XF86XK_Phone:*********,XF86XK_Q:*********,XF86XK_Reply:*********,XF86XK_Reload:*********,XF86XK_RotateWindows:*********,XF86XK_RotationPB:*********,XF86XK_RotationKB:*********,XF86XK_Save:*********,XF86XK_ScrollUp:*********,XF86XK_ScrollDown:*********,XF86XK_ScrollClick:*********,XF86XK_Send:*********,XF86XK_Spell:*********,XF86XK_SplitScreen:*********,XF86XK_Support:*********,XF86XK_TaskPane:*********,XF86XK_Terminal:*********,XF86XK_Tools:*********,XF86XK_Travel:*********,XF86XK_UserPB:*********,XF86XK_User1KB:*********,XF86XK_User2KB:*********,XF86XK_Video:*********,XF86XK_WheelButton:*********,XF86XK_Word:*********,XF86XK_Xfer:*********,XF86XK_ZoomIn:*********,XF86XK_ZoomOut:*********,XF86XK_Away:*********,XF86XK_Messenger:*********,XF86XK_WebCam:*********,XF86XK_MailForward:*********,XF86XK_Pictures:*********,XF86XK_Music:*********,XF86XK_Battery:*********,XF86XK_Bluetooth:*********,XF86XK_WLAN:*********,XF86XK_UWB:*********,XF86XK_AudioForward:*********,XF86XK_AudioRepeat:*********,XF86XK_AudioRandomPlay:*********,XF86XK_Subtitle:*********,XF86XK_AudioCycleTrack:*********,XF86XK_CycleAngle:*********,XF86XK_FrameBack:*********,XF86XK_FrameForward:*********,XF86XK_Time:*********,XF86XK_Select:*********,XF86XK_View:*********,XF86XK_TopMenu:*********,XF86XK_Red:*********,XF86XK_Green:*********,XF86XK_Yellow:*********,XF86XK_Blue:*********,XF86XK_Suspend:*********,XF86XK_Hibernate:*********,XF86XK_TouchpadToggle:*********,XF86XK_TouchpadOn:269025200,XF86XK_TouchpadOff:269025201,XF86XK_AudioMicMute:269025202,XF86XK_Switch_VT_1:269024769,XF86XK_Switch_VT_2:269024770,XF86XK_Switch_VT_3:269024771,XF86XK_Switch_VT_4:269024772,XF86XK_Switch_VT_5:269024773,XF86XK_Switch_VT_6:269024774,XF86XK_Switch_VT_7:269024775,XF86XK_Switch_VT_8:269024776,XF86XK_Switch_VT_9:269024777,XF86XK_Switch_VT_10:269024778,XF86XK_Switch_VT_11:269024779,XF86XK_Switch_VT_12:269024780,XF86XK_Ungrab:269024800,XF86XK_ClearGrab:269024801,XF86XK_Next_VMode:269024802,XF86XK_Prev_VMode:269024803,XF86XK_LogWindowTree:269024804,XF86XK_LogGrabInfo:269024805}),pe;var e}var ve,ge={};var me,be={};var we,ke={};var Xe,Ke,Se,Ee={};function xe(){if(Ke)return _e;function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)}Ke=1,Object.defineProperty(_e,"__esModule",{value:!0}),_e.getKey=l,_e.getKeycode=c,_e.getKeysym=function(e){var n=l(e);if("Unidentified"===n)return null;if(n in o.default){var i=e.location;if("Meta"===n&&0===i&&(i=2),"Clear"===n&&3===i)"NumLock"===c(e)&&(i=0);if((void 0===i||i>3)&&(i=0),"Meta"===n){var s=c(e);if("AltLeft"===s)return t.default.XK_Meta_L;if("AltRight"===s)return t.default.XK_Meta_R}if("Clear"===n)if("NumLock"===c(e))return t.default.XK_Num_Lock;if(a.isWindows())switch(n){case"Zenkaku":case"Hankaku":return t.default.XK_Zenkaku_Hankaku;case"Romaji":case"KanaMode":return t.default.XK_Romaji}return o.default[n][i]}if(1!==n.length)return null;var u=n.charCodeAt();return u?r.default.lookup(u):null};var t=u(ye()),r=u((ve||(ve=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var t={256:960,257:992,258:451,259:483,260:417,261:433,262:454,263:486,264:710,265:742,266:709,267:741,268:456,269:488,270:463,271:495,272:464,273:496,274:938,275:954,278:972,279:1004,280:458,281:490,282:460,283:492,284:728,285:760,286:683,287:699,288:725,289:757,290:939,291:955,292:678,293:694,294:673,295:689,296:933,297:949,298:975,299:1007,302:967,303:999,304:681,305:697,308:684,309:700,310:979,311:1011,312:930,313:453,314:485,315:934,316:950,317:421,318:437,321:419,322:435,323:465,324:497,325:977,326:1009,327:466,328:498,330:957,331:959,332:978,333:1010,336:469,337:501,338:5052,339:5053,340:448,341:480,342:931,343:947,344:472,345:504,346:422,347:438,348:734,349:766,350:426,351:442,352:425,353:441,354:478,355:510,356:427,357:443,358:940,359:956,360:989,361:1021,362:990,363:1022,364:733,365:765,366:473,367:505,368:475,369:507,370:985,371:1017,376:5054,377:428,378:444,379:431,380:447,381:430,382:446,402:2294,466:16777681,711:439,728:418,729:511,731:434,733:445,901:1966,902:1953,904:1954,905:1955,906:1956,908:1959,910:1960,911:1963,912:1974,913:1985,914:1986,915:1987,916:1988,917:1989,918:1990,919:1991,920:1992,921:1993,922:1994,923:1995,924:1996,925:1997,926:1998,927:1999,928:2e3,929:2001,931:2002,932:2004,933:2005,934:2006,935:2007,936:2008,937:2009,938:1957,939:1961,940:1969,941:1970,942:1971,943:1972,944:1978,945:2017,946:2018,947:2019,948:2020,949:2021,950:2022,951:2023,952:2024,953:2025,954:2026,955:2027,956:2028,957:2029,958:2030,959:2031,960:2032,961:2033,962:2035,963:2034,964:2036,965:2037,966:2038,967:2039,968:2040,969:2041,970:1973,971:1977,972:1975,973:1976,974:1979,1025:1715,1026:1713,1027:1714,1028:1716,1029:1717,1030:1718,1031:1719,1032:1720,1033:1721,1034:1722,1035:1723,1036:1724,1038:1726,1039:1727,1040:1761,1041:1762,1042:1783,1043:1767,1044:1764,1045:1765,1046:1782,1047:1786,1048:1769,1049:1770,1050:1771,1051:1772,1052:1773,1053:1774,1054:1775,1055:1776,1056:1778,1057:1779,1058:1780,1059:1781,1060:1766,1061:1768,1062:1763,1063:1790,1064:1787,1065:1789,1066:1791,1067:1785,1068:1784,1069:1788,1070:1760,1071:1777,1072:1729,1073:1730,1074:1751,1075:1735,1076:1732,1077:1733,1078:1750,1079:1754,1080:1737,1081:1738,1082:1739,1083:1740,1084:1741,1085:1742,1086:1743,1087:1744,1088:1746,1089:1747,1090:1748,1091:1749,1092:1734,1093:1736,1094:1731,1095:1758,1096:1755,1097:1757,1098:1759,1099:1753,1100:1752,1101:1756,1102:1728,1103:1745,1105:1699,1106:1697,1107:1698,1108:1700,1109:1701,1110:1702,1111:1703,1112:1704,1113:1705,1114:1706,1115:1707,1116:1708,1118:1710,1119:1711,1168:1725,1169:1709,1488:3296,1489:3297,1490:3298,1491:3299,1492:3300,1493:3301,1494:3302,1495:3303,1496:3304,1497:3305,1498:3306,1499:3307,1500:3308,1501:3309,1502:3310,1503:3311,1504:3312,1505:3313,1506:3314,1507:3315,1508:3316,1509:3317,1510:3318,1511:3319,1512:3320,1513:3321,1514:3322,1548:1452,1563:1467,1567:1471,1569:1473,1570:1474,1571:1475,1572:1476,1573:1477,1574:1478,1575:1479,1576:1480,1577:1481,1578:1482,1579:1483,1580:1484,1581:1485,1582:1486,1583:1487,1584:1488,1585:1489,1586:1490,1587:1491,1588:1492,1589:1493,1590:1494,1591:1495,1592:1496,1593:1497,1594:1498,1600:1504,1601:1505,1602:1506,1603:1507,1604:1508,1605:1509,1606:1510,1607:1511,1608:1512,1609:1513,1610:1514,1611:1515,1612:1516,1613:1517,1614:1518,1615:1519,1616:1520,1617:1521,1618:1522,3585:3489,3586:3490,3587:3491,3588:3492,3589:3493,3590:3494,3591:3495,3592:3496,3593:3497,3594:3498,3595:3499,3596:3500,3597:3501,3598:3502,3599:3503,3600:3504,3601:3505,3602:3506,3603:3507,3604:3508,3605:3509,3606:3510,3607:3511,3608:3512,3609:3513,3610:3514,3611:3515,3612:3516,3613:3517,3614:3518,3615:3519,3616:3520,3617:3521,3618:3522,3619:3523,3620:3524,3621:3525,3622:3526,3623:3527,3624:3528,3625:3529,3626:3530,3627:3531,3628:3532,3629:3533,3630:3534,3631:3535,3632:3536,3633:3537,3634:3538,3635:3539,3636:3540,3637:3541,3638:3542,3639:3543,3640:3544,3641:3545,3642:3546,3647:3551,3648:3552,3649:3553,3650:3554,3651:3555,3652:3556,3653:3557,3654:3558,3655:3559,3656:3560,3657:3561,3658:3562,3659:3563,3660:3564,3661:3565,3664:3568,3665:3569,3666:3570,3667:3571,3668:3572,3669:3573,3670:3574,3671:3575,3672:3576,3673:3577,8194:2722,8195:2721,8196:2723,8197:2724,8199:2725,8200:2726,8201:2727,8202:2728,8210:2747,8211:2730,8212:2729,8213:1967,8215:3295,8216:2768,8217:2769,8218:2813,8220:2770,8221:2771,8222:2814,8224:2801,8225:2802,8226:2790,8229:2735,8230:2734,8240:2773,8242:2774,8243:2775,8248:2812,8254:1150,8361:3839,8364:8364,8453:2744,8470:1712,8471:2811,8478:2772,8482:2761,8531:2736,8532:2737,8533:2738,8534:2739,8535:2740,8536:2741,8537:2742,8538:2743,8539:2755,8540:2756,8541:2757,8542:2758,8592:2299,8593:2300,8594:2301,8595:2302,8658:2254,8660:2253,8706:2287,8711:2245,8728:3018,8730:2262,8733:2241,8734:2242,8743:2270,8744:2271,8745:2268,8746:2269,8747:2239,8756:2240,8764:2248,8771:2249,8773:16785992,8800:2237,8801:2255,8804:2236,8805:2238,8834:2266,8835:2267,8866:3068,8867:3036,8868:3010,8869:3022,8968:3027,8970:3012,8981:2810,8992:2212,8993:2213,9109:3020,9115:2219,9117:2220,9118:2221,9120:2222,9121:2215,9123:2216,9124:2217,9126:2218,9128:2223,9132:2224,9143:2209,9146:2543,9147:2544,9148:2546,9149:2547,9225:2530,9226:2533,9227:2537,9228:2531,9229:2532,9251:2732,9252:2536,9472:2211,9474:2214,9484:2210,9488:2539,9492:2541,9496:2538,9500:2548,9508:2549,9516:2551,9524:2550,9532:2542,9618:2529,9642:2791,9643:2785,9644:2779,9645:2786,9646:2783,9647:2767,9650:2792,9651:2787,9654:2781,9655:2765,9660:2793,9661:2788,9664:2780,9665:2764,9670:2528,9675:2766,9679:2782,9702:2784,9734:2789,9742:2809,9747:2762,9756:2794,9758:2795,9792:2808,9794:2807,9827:2796,9829:2798,9830:2797,9837:2806,9839:2805,10003:2803,10007:2804,10013:2777,10016:2800,10216:2748,10217:2750,12289:1188,12290:1185,12300:1186,12301:1187,12443:1246,12444:1247,12449:1191,12450:1201,12451:1192,12452:1202,12453:1193,12454:1203,12455:1194,12456:1204,12457:1195,12458:1205,12459:1206,12461:1207,12463:1208,12465:1209,12467:1210,12469:1211,12471:1212,12473:1213,12475:1214,12477:1215,12479:1216,12481:1217,12483:1199,12484:1218,12486:1219,12488:1220,12490:1221,12491:1222,12492:1223,12493:1224,12494:1225,12495:1226,12498:1227,12501:1228,12504:1229,12507:1230,12510:1231,12511:1232,12512:1233,12513:1234,12514:1235,12515:1196,12516:1236,12517:1197,12518:1237,12519:1198,12520:1238,12521:1239,12522:1240,12523:1241,12524:1242,12525:1243,12527:1244,12530:1190,12531:1245,12539:1189,12540:1200};e.default={lookup:function(e){if(e>=32&&e<=255)return e;var r=t[e];return void 0!==r?r:16777216|e}}}(ge)),ge)),n=u(function(){return me||(me=1,e=be,Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,e.default={8:"Backspace",9:"Tab",10:"NumpadClear",13:"Enter",16:"ShiftLeft",17:"ControlLeft",18:"AltLeft",19:"Pause",20:"CapsLock",21:"Lang1",25:"Lang2",27:"Escape",28:"Convert",29:"NonConvert",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",41:"Select",44:"PrintScreen",45:"Insert",46:"Delete",47:"Help",48:"Digit0",49:"Digit1",50:"Digit2",51:"Digit3",52:"Digit4",53:"Digit5",54:"Digit6",55:"Digit7",56:"Digit8",57:"Digit9",91:"MetaLeft",92:"MetaRight",93:"ContextMenu",95:"Sleep",96:"Numpad0",97:"Numpad1",98:"Numpad2",99:"Numpad3",100:"Numpad4",101:"Numpad5",102:"Numpad6",103:"Numpad7",104:"Numpad8",105:"Numpad9",106:"NumpadMultiply",107:"NumpadAdd",108:"NumpadDecimal",109:"NumpadSubtract",110:"NumpadDecimal",111:"NumpadDivide",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",124:"F13",125:"F14",126:"F15",127:"F16",128:"F17",129:"F18",130:"F19",131:"F20",132:"F21",133:"F22",134:"F23",135:"F24",144:"NumLock",145:"ScrollLock",166:"BrowserBack",167:"BrowserForward",168:"BrowserRefresh",169:"BrowserStop",170:"BrowserSearch",171:"BrowserFavorites",172:"BrowserHome",173:"AudioVolumeMute",174:"AudioVolumeDown",175:"AudioVolumeUp",176:"MediaTrackNext",177:"MediaTrackPrevious",178:"MediaStop",179:"MediaPlayPause",180:"LaunchMail",181:"MediaSelect",182:"LaunchApp1",183:"LaunchApp2",225:"AltRight"}),be;var e}()),i=u(function(){return we||(we=1,e=ke,Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,e.default={Backspace:"Backspace",AltLeft:"Alt",AltRight:"Alt",CapsLock:"CapsLock",ContextMenu:"ContextMenu",ControlLeft:"Control",ControlRight:"Control",Enter:"Enter",MetaLeft:"Meta",MetaRight:"Meta",ShiftLeft:"Shift",ShiftRight:"Shift",Tab:"Tab",Delete:"Delete",End:"End",Help:"Help",Home:"Home",Insert:"Insert",PageDown:"PageDown",PageUp:"PageUp",ArrowDown:"ArrowDown",ArrowLeft:"ArrowLeft",ArrowRight:"ArrowRight",ArrowUp:"ArrowUp",NumLock:"NumLock",NumpadBackspace:"Backspace",NumpadClear:"Clear",Escape:"Escape",F1:"F1",F2:"F2",F3:"F3",F4:"F4",F5:"F5",F6:"F6",F7:"F7",F8:"F8",F9:"F9",F10:"F10",F11:"F11",F12:"F12",F13:"F13",F14:"F14",F15:"F15",F16:"F16",F17:"F17",F18:"F18",F19:"F19",F20:"F20",F21:"F21",F22:"F22",F23:"F23",F24:"F24",F25:"F25",F26:"F26",F27:"F27",F28:"F28",F29:"F29",F30:"F30",F31:"F31",F32:"F32",F33:"F33",F34:"F34",F35:"F35",PrintScreen:"PrintScreen",ScrollLock:"ScrollLock",Pause:"Pause",BrowserBack:"BrowserBack",BrowserFavorites:"BrowserFavorites",BrowserForward:"BrowserForward",BrowserHome:"BrowserHome",BrowserRefresh:"BrowserRefresh",BrowserSearch:"BrowserSearch",BrowserStop:"BrowserStop",Eject:"Eject",LaunchApp1:"LaunchMyComputer",LaunchApp2:"LaunchCalendar",LaunchMail:"LaunchMail",MediaPlayPause:"MediaPlay",MediaStop:"MediaStop",MediaTrackNext:"MediaTrackNext",MediaTrackPrevious:"MediaTrackPrevious",Power:"Power",Sleep:"Sleep",AudioVolumeDown:"AudioVolumeDown",AudioVolumeMute:"AudioVolumeMute",AudioVolumeUp:"AudioVolumeUp",WakeUp:"WakeUp"}),ke;var e}()),o=u((Xe||(Xe=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var t,r=(t=ye())&&t.__esModule?t:{default:t},n={};function i(e,t){if(void 0===t)throw new Error('Undefined keysym for key "'+e+'"');if(e in n)throw new Error('Duplicate entry for key "'+e+'"');n[e]=[t,t,t,t]}function o(e,t,r){if(void 0===t)throw new Error('Undefined keysym for key "'+e+'"');if(void 0===r)throw new Error('Undefined keysym for key "'+e+'"');if(e in n)throw new Error('Duplicate entry for key "'+e+'"');n[e]=[t,t,r,t]}function a(e,t,r){if(void 0===t)throw new Error('Undefined keysym for key "'+e+'"');if(void 0===r)throw new Error('Undefined keysym for key "'+e+'"');if(e in n)throw new Error('Duplicate entry for key "'+e+'"');n[e]=[t,t,t,r]}o("Alt",r.default.XK_Alt_L,r.default.XK_Alt_R),i("AltGraph",r.default.XK_ISO_Level3_Shift),i("CapsLock",r.default.XK_Caps_Lock),o("Control",r.default.XK_Control_L,r.default.XK_Control_R),o("Meta",r.default.XK_Super_L,r.default.XK_Super_R),i("NumLock",r.default.XK_Num_Lock),i("ScrollLock",r.default.XK_Scroll_Lock),o("Shift",r.default.XK_Shift_L,r.default.XK_Shift_R),a("Enter",r.default.XK_Return,r.default.XK_KP_Enter),i("Tab",r.default.XK_Tab),a(" ",r.default.XK_space,r.default.XK_KP_Space),a("ArrowDown",r.default.XK_Down,r.default.XK_KP_Down),a("ArrowLeft",r.default.XK_Left,r.default.XK_KP_Left),a("ArrowRight",r.default.XK_Right,r.default.XK_KP_Right),a("ArrowUp",r.default.XK_Up,r.default.XK_KP_Up),a("End",r.default.XK_End,r.default.XK_KP_End),a("Home",r.default.XK_Home,r.default.XK_KP_Home),a("PageDown",r.default.XK_Next,r.default.XK_KP_Next),a("PageUp",r.default.XK_Prior,r.default.XK_KP_Prior),i("Backspace",r.default.XK_BackSpace),a("Clear",r.default.XK_Clear,r.default.XK_KP_Begin),i("Copy",r.default.XF86XK_Copy),i("Cut",r.default.XF86XK_Cut),a("Delete",r.default.XK_Delete,r.default.XK_KP_Delete),a("Insert",r.default.XK_Insert,r.default.XK_KP_Insert),i("Paste",r.default.XF86XK_Paste),i("Redo",r.default.XK_Redo),i("Undo",r.default.XK_Undo),i("Cancel",r.default.XK_Cancel),i("ContextMenu",r.default.XK_Menu),i("Escape",r.default.XK_Escape),i("Execute",r.default.XK_Execute),i("Find",r.default.XK_Find),i("Help",r.default.XK_Help),i("Pause",r.default.XK_Pause),i("Select",r.default.XK_Select),i("ZoomIn",r.default.XF86XK_ZoomIn),i("ZoomOut",r.default.XF86XK_ZoomOut),i("BrightnessDown",r.default.XF86XK_MonBrightnessDown),i("BrightnessUp",r.default.XF86XK_MonBrightnessUp),i("Eject",r.default.XF86XK_Eject),i("LogOff",r.default.XF86XK_LogOff),i("Power",r.default.XF86XK_PowerOff),i("PowerOff",r.default.XF86XK_PowerDown),i("PrintScreen",r.default.XK_Print),i("Hibernate",r.default.XF86XK_Hibernate),i("Standby",r.default.XF86XK_Standby),i("WakeUp",r.default.XF86XK_WakeUp),i("AllCandidates",r.default.XK_MultipleCandidate),i("Alphanumeric",r.default.XK_Eisu_toggle),i("CodeInput",r.default.XK_Codeinput),i("Compose",r.default.XK_Multi_key),i("Convert",r.default.XK_Henkan),i("GroupFirst",r.default.XK_ISO_First_Group),i("GroupLast",r.default.XK_ISO_Last_Group),i("GroupNext",r.default.XK_ISO_Next_Group),i("GroupPrevious",r.default.XK_ISO_Prev_Group),i("NonConvert",r.default.XK_Muhenkan),i("PreviousCandidate",r.default.XK_PreviousCandidate),i("SingleCandidate",r.default.XK_SingleCandidate),i("HangulMode",r.default.XK_Hangul),i("HanjaMode",r.default.XK_Hangul_Hanja),i("JunjaMode",r.default.XK_Hangul_Jeonja),i("Eisu",r.default.XK_Eisu_toggle),i("Hankaku",r.default.XK_Hankaku),i("Hiragana",r.default.XK_Hiragana),i("HiraganaKatakana",r.default.XK_Hiragana_Katakana),i("KanaMode",r.default.XK_Kana_Shift),i("KanjiMode",r.default.XK_Kanji),i("Katakana",r.default.XK_Katakana),i("Romaji",r.default.XK_Romaji),i("Zenkaku",r.default.XK_Zenkaku),i("ZenkakuHankaku",r.default.XK_Zenkaku_Hankaku),i("F1",r.default.XK_F1),i("F2",r.default.XK_F2),i("F3",r.default.XK_F3),i("F4",r.default.XK_F4),i("F5",r.default.XK_F5),i("F6",r.default.XK_F6),i("F7",r.default.XK_F7),i("F8",r.default.XK_F8),i("F9",r.default.XK_F9),i("F10",r.default.XK_F10),i("F11",r.default.XK_F11),i("F12",r.default.XK_F12),i("F13",r.default.XK_F13),i("F14",r.default.XK_F14),i("F15",r.default.XK_F15),i("F16",r.default.XK_F16),i("F17",r.default.XK_F17),i("F18",r.default.XK_F18),i("F19",r.default.XK_F19),i("F20",r.default.XK_F20),i("F21",r.default.XK_F21),i("F22",r.default.XK_F22),i("F23",r.default.XK_F23),i("F24",r.default.XK_F24),i("F25",r.default.XK_F25),i("F26",r.default.XK_F26),i("F27",r.default.XK_F27),i("F28",r.default.XK_F28),i("F29",r.default.XK_F29),i("F30",r.default.XK_F30),i("F31",r.default.XK_F31),i("F32",r.default.XK_F32),i("F33",r.default.XK_F33),i("F34",r.default.XK_F34),i("F35",r.default.XK_F35),i("Close",r.default.XF86XK_Close),i("MailForward",r.default.XF86XK_MailForward),i("MailReply",r.default.XF86XK_Reply),i("MailSend",r.default.XF86XK_Send),i("MediaFastForward",r.default.XF86XK_AudioForward),i("MediaPause",r.default.XF86XK_AudioPause),i("MediaPlay",r.default.XF86XK_AudioPlay),i("MediaRecord",r.default.XF86XK_AudioRecord),i("MediaRewind",r.default.XF86XK_AudioRewind),i("MediaStop",r.default.XF86XK_AudioStop),i("MediaTrackNext",r.default.XF86XK_AudioNext),i("MediaTrackPrevious",r.default.XF86XK_AudioPrev),i("New",r.default.XF86XK_New),i("Open",r.default.XF86XK_Open),i("Print",r.default.XK_Print),i("Save",r.default.XF86XK_Save),i("SpellCheck",r.default.XF86XK_Spell),i("AudioVolumeDown",r.default.XF86XK_AudioLowerVolume),i("AudioVolumeUp",r.default.XF86XK_AudioRaiseVolume),i("AudioVolumeMute",r.default.XF86XK_AudioMute),i("MicrophoneVolumeMute",r.default.XF86XK_AudioMicMute),i("LaunchApplication1",r.default.XF86XK_MyComputer),i("LaunchApplication2",r.default.XF86XK_Calculator),i("LaunchCalendar",r.default.XF86XK_Calendar),i("LaunchMail",r.default.XF86XK_Mail),i("LaunchMediaPlayer",r.default.XF86XK_AudioMedia),i("LaunchMusicPlayer",r.default.XF86XK_Music),i("LaunchPhone",r.default.XF86XK_Phone),i("LaunchScreenSaver",r.default.XF86XK_ScreenSaver),i("LaunchSpreadsheet",r.default.XF86XK_Excel),i("LaunchWebBrowser",r.default.XF86XK_WWW),i("LaunchWebCam",r.default.XF86XK_WebCam),i("LaunchWordProcessor",r.default.XF86XK_Word),i("BrowserBack",r.default.XF86XK_Back),i("BrowserFavorites",r.default.XF86XK_Favorites),i("BrowserForward",r.default.XF86XK_Forward),i("BrowserHome",r.default.XF86XK_HomePage),i("BrowserRefresh",r.default.XF86XK_Refresh),i("BrowserSearch",r.default.XF86XK_Search),i("BrowserStop",r.default.XF86XK_Stop),i("Dimmer",r.default.XF86XK_BrightnessAdjust),i("MediaAudioTrack",r.default.XF86XK_AudioCycleTrack),i("RandomToggle",r.default.XF86XK_AudioRandomPlay),i("SplitScreenToggle",r.default.XF86XK_SplitScreen),i("Subtitle",r.default.XF86XK_Subtitle),i("VideoModeNext",r.default.XF86XK_Next_VMode),a("=",r.default.XK_equal,r.default.XK_KP_Equal),a("+",r.default.XK_plus,r.default.XK_KP_Add),a("-",r.default.XK_minus,r.default.XK_KP_Subtract),a("*",r.default.XK_asterisk,r.default.XK_KP_Multiply),a("/",r.default.XK_slash,r.default.XK_KP_Divide),a(".",r.default.XK_period,r.default.XK_KP_Decimal),a(",",r.default.XK_comma,r.default.XK_KP_Separator),a("0",r.default.XK_0,r.default.XK_KP_0),a("1",r.default.XK_1,r.default.XK_KP_1),a("2",r.default.XK_2,r.default.XK_KP_2),a("3",r.default.XK_3,r.default.XK_KP_3),a("4",r.default.XK_4,r.default.XK_KP_4),a("5",r.default.XK_5,r.default.XK_KP_5),a("6",r.default.XK_6,r.default.XK_KP_6),a("7",r.default.XK_7,r.default.XK_KP_7),a("8",r.default.XK_8,r.default.XK_KP_8),a("9",r.default.XK_9,r.default.XK_KP_9),e.default=n}(Ee)),Ee)),a=function(t,r){if(t&&t.__esModule)return t;if(null===t||"object"!=e(t)&&"function"!=typeof t)return{default:t};var n=s(r);if(n&&n.has(t))return n.get(t);var i={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in t)if("default"!==a&&{}.hasOwnProperty.call(t,a)){var u=o?Object.getOwnPropertyDescriptor(t,a):null;u&&(u.get||u.set)?Object.defineProperty(i,a,u):i[a]=t[a]}return i.default=t,n&&n.set(t,i),i}(g());function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}function u(e){return e&&e.__esModule?e:{default:e}}function c(e){if(e.code){switch(e.code){case"OSLeft":return"MetaLeft";case"OSRight":return"MetaRight"}return e.code}if(e.keyCode in n.default){var t=n.default[e.keyCode];if(a.isMac()&&"ContextMenu"===t&&(t="MetaRight"),2===e.location)switch(t){case"ShiftLeft":return"ShiftRight";case"ControlLeft":return"ControlRight";case"AltLeft":return"AltRight"}if(3===e.location)switch(t){case"Delete":return"NumpadDecimal";case"Insert":return"Numpad0";case"End":return"Numpad1";case"ArrowDown":return"Numpad2";case"PageDown":return"Numpad3";case"ArrowLeft":return"Numpad4";case"ArrowRight":return"Numpad6";case"Home":return"Numpad7";case"ArrowUp":return"Numpad8";case"PageUp":return"Numpad9";case"Enter":return"NumpadEnter"}return t}return"Unidentified"}function l(e){if(void 0!==e.key&&"Unidentified"!==e.key){switch(e.key){case"OS":return"Meta";case"LaunchMyComputer":return"LaunchApplication1";case"LaunchCalculator":return"LaunchApplication2"}switch(e.key){case"UIKeyInputUpArrow":return"ArrowUp";case"UIKeyInputDownArrow":return"ArrowDown";case"UIKeyInputLeftArrow":return"ArrowLeft";case"UIKeyInputRightArrow":return"ArrowRight";case"UIKeyInputEscape":return"Escape"}return"\0"===e.key&&"NumpadDecimal"===e.code?"Delete":e.key}var t=c(e);return t in i.default?i.default[t]:e.charCode?String.fromCharCode(e.charCode):"Unidentified"}return _e}function Ce(){return Se||(Se=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var t,r=u(f()),n=X(),i=u(xe()),o=(t=ye())&&t.__esModule?t:{default:t},a=u(g());function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}function u(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=c(e)&&"function"!=typeof e)return{default:e};var r=s(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&{}.hasOwnProperty.call(e,o)){var a=i?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}function c(e){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function l(e,t,r){return t&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,h(n.key),n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function h(e){var t=function(e,t){if("object"!=c(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==c(t)?t:t+""}e.default=function(){return l((function e(t){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this._target=t||null,this._keyDownList={},this._altGrArmed=!1,this._eventHandlers={keyup:this._handleKeyUp.bind(this),keydown:this._handleKeyDown.bind(this),blur:this._allKeysUp.bind(this)},this.onkeyevent=function(){}}),[{key:"_sendKeyEvent",value:function(e,t,n){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;if(n)this._keyDownList[t]=e;else{if(!(t in this._keyDownList))return;delete this._keyDownList[t]}r.Debug("onkeyevent "+(n?"down":"up")+", keysym: "+e,", code: "+t+", numlock: "+i+", capslock: "+o),this.onkeyevent(e,t,n,i,o)}},{key:"_getKeyCode",value:function(e){var t=i.getKeycode(e);if("Unidentified"!==t)return t;if(e.keyCode&&229!==e.keyCode)return"Platform"+e.keyCode;if(e.keyIdentifier){if("U+"!==e.keyIdentifier.substr(0,2))return e.keyIdentifier;var r=parseInt(e.keyIdentifier.substr(2),16);return"Platform"+String.fromCharCode(r).toUpperCase().charCodeAt()}return"Unidentified"}},{key:"_handleKeyDown",value:function(e){var t=this._getKeyCode(e),r=i.getKeysym(e),s=e.getModifierState("NumLock"),u=e.getModifierState("CapsLock");if((a.isMac()||a.isIOS())&&(s=null),this._altGrArmed&&(this._altGrArmed=!1,clearTimeout(this._altGrTimeout),"AltRight"===t&&e.timeStamp-this._altGrCtrlTime<50?r=o.default.XK_ISO_Level3_Shift:this._sendKeyEvent(o.default.XK_Control_L,"ControlLeft",!0,s,u)),"Unidentified"===t)return r&&(this._sendKeyEvent(r,t,!0,s,u),this._sendKeyEvent(r,t,!1,s,u)),void(0,n.stopEvent)(e);if(a.isMac()||a.isIOS())switch(r){case o.default.XK_Super_L:r=o.default.XK_Alt_L;break;case o.default.XK_Super_R:r=o.default.XK_Super_L;break;case o.default.XK_Alt_L:r=o.default.XK_Mode_switch;break;case o.default.XK_Alt_R:r=o.default.XK_ISO_Level3_Shift}if(t in this._keyDownList&&(r=this._keyDownList[t]),(a.isMac()||a.isIOS())&&e.metaKey&&"MetaLeft"!==t&&"MetaRight"!==t)return this._sendKeyEvent(r,t,!0,s,u),this._sendKeyEvent(r,t,!1,s,u),void(0,n.stopEvent)(e);if((a.isMac()||a.isIOS())&&"CapsLock"===t)return this._sendKeyEvent(o.default.XK_Caps_Lock,"CapsLock",!0,s,u),this._sendKeyEvent(o.default.XK_Caps_Lock,"CapsLock",!1,s,u),void(0,n.stopEvent)(e);var c=[o.default.XK_Zenkaku_Hankaku,o.default.XK_Eisu_toggle,o.default.XK_Katakana,o.default.XK_Hiragana,o.default.XK_Romaji];return a.isWindows()&&c.includes(r)?(this._sendKeyEvent(r,t,!0,s,u),this._sendKeyEvent(r,t,!1,s,u),void(0,n.stopEvent)(e)):((0,n.stopEvent)(e),"ControlLeft"===t&&a.isWindows()&&!("ControlLeft"in this._keyDownList)?(this._altGrArmed=!0,this._altGrTimeout=setTimeout(this._handleAltGrTimeout.bind(this),100),void(this._altGrCtrlTime=e.timeStamp)):void this._sendKeyEvent(r,t,!0,s,u))}},{key:"_handleKeyUp",value:function(e){(0,n.stopEvent)(e);var t=this._getKeyCode(e);if(this._altGrArmed&&(this._altGrArmed=!1,clearTimeout(this._altGrTimeout),this._sendKeyEvent(o.default.XK_Control_L,"ControlLeft",!0)),(a.isMac()||a.isIOS())&&"CapsLock"===t)return this._sendKeyEvent(o.default.XK_Caps_Lock,"CapsLock",!0),void this._sendKeyEvent(o.default.XK_Caps_Lock,"CapsLock",!1);this._sendKeyEvent(this._keyDownList[t],t,!1),a.isWindows()&&("ShiftLeft"===t||"ShiftRight"===t)&&("ShiftRight"in this._keyDownList&&this._sendKeyEvent(this._keyDownList.ShiftRight,"ShiftRight",!1),"ShiftLeft"in this._keyDownList&&this._sendKeyEvent(this._keyDownList.ShiftLeft,"ShiftLeft",!1))}},{key:"_handleAltGrTimeout",value:function(){this._altGrArmed=!1,clearTimeout(this._altGrTimeout),this._sendKeyEvent(o.default.XK_Control_L,"ControlLeft",!0)}},{key:"_allKeysUp",value:function(){for(var e in r.Debug(">> Keyboard.allKeysUp"),this._keyDownList)this._sendKeyEvent(this._keyDownList[e],e,!1);r.Debug("<< Keyboard.allKeysUp")}},{key:"grab",value:function(){this._target.addEventListener("keydown",this._eventHandlers.keydown),this._target.addEventListener("keyup",this._eventHandlers.keyup),window.addEventListener("blur",this._eventHandlers.blur)}},{key:"ungrab",value:function(){this._target.removeEventListener("keydown",this._eventHandlers.keydown),this._target.removeEventListener("keyup",this._eventHandlers.keyup),window.removeEventListener("blur",this._eventHandlers.blur),this._allKeysUp()}}])}()}(de)),de}var Ae,Fe={};function Le(){return Ae||(Ae=1,function(e){function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(e,t,r){return t&&function(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,n(i.key),i)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function n(e){var r=function(e,r){if("object"!=t(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,r);if("object"!=t(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==t(r)?r:r+""}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default=function(){return r((function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this._target=null,this._state=127,this._tracked=[],this._ignored=[],this._waitingRelease=!1,this._releaseStart=0,this._longpressTimeoutId=null,this._twoTouchTimeoutId=null,this._boundEventHandler=this._eventHandler.bind(this)}),[{key:"attach",value:function(e){this.detach(),this._target=e,this._target.addEventListener("touchstart",this._boundEventHandler),this._target.addEventListener("touchmove",this._boundEventHandler),this._target.addEventListener("touchend",this._boundEventHandler),this._target.addEventListener("touchcancel",this._boundEventHandler)}},{key:"detach",value:function(){this._target&&(this._stopLongpressTimeout(),this._stopTwoTouchTimeout(),this._target.removeEventListener("touchstart",this._boundEventHandler),this._target.removeEventListener("touchmove",this._boundEventHandler),this._target.removeEventListener("touchend",this._boundEventHandler),this._target.removeEventListener("touchcancel",this._boundEventHandler),this._target=null)}},{key:"_eventHandler",value:function(e){var t;switch(e.stopPropagation(),e.preventDefault(),e.type){case"touchstart":t=this._touchStart;break;case"touchmove":t=this._touchMove;break;case"touchend":case"touchcancel":t=this._touchEnd}for(var r=0;r<e.changedTouches.length;r++){var n=e.changedTouches[r];t.call(this,n.identifier,n.clientX,n.clientY)}}},{key:"_touchStart",value:function(e,t,r){if(this._hasDetectedGesture()||0===this._state)this._ignored.push(e);else{if(this._tracked.length>0&&Date.now()-this._tracked[0].started>250)return this._state=0,void this._ignored.push(e);if(this._waitingRelease)return this._state=0,void this._ignored.push(e);switch(this._tracked.push({id:e,started:Date.now(),active:!0,firstX:t,firstY:r,lastX:t,lastY:r,angle:0}),this._tracked.length){case 1:this._startLongpressTimeout();break;case 2:this._state&=-26,this._stopLongpressTimeout();break;case 3:this._state&=-99;break;default:this._state=0}}}},{key:"_touchMove",value:function(e,t,r){var n=this._tracked.find((function(t){return t.id===e}));if(void 0!==n){n.lastX=t,n.lastY=r;var i=t-n.firstX,o=r-n.firstY;if((n.firstX!==n.lastX||n.firstY!==n.lastY)&&(n.angle=180*Math.atan2(o,i)/Math.PI),!this._hasDetectedGesture()){if(Math.hypot(i,o)<50)return;if(this._state&=-24,this._stopLongpressTimeout(),1!==this._tracked.length&&(this._state&=-9),2!==this._tracked.length&&(this._state&=-97),2===this._tracked.length){var a=this._tracked.find((function(t){return t.id!==e}));if(Math.hypot(a.firstX-a.lastX,a.firstY-a.lastY)>50){var s=Math.abs(n.angle-a.angle);s=Math.abs((s+180)%360-180),this._state&=s>90?-33:-65,this._isTwoTouchTimeoutRunning()&&this._stopTwoTouchTimeout()}else this._isTwoTouchTimeoutRunning()||this._startTwoTouchTimeout()}if(!this._hasDetectedGesture())return;this._pushEvent("gesturestart")}this._pushEvent("gesturemove")}}},{key:"_touchEnd",value:function(e,t,r){if(-1!==this._ignored.indexOf(e))return this._ignored.splice(this._ignored.indexOf(e),1),void(0===this._ignored.length&&0===this._tracked.length&&(this._state=127,this._waitingRelease=!1));if(!this._hasDetectedGesture()&&this._isTwoTouchTimeoutRunning()&&(this._stopTwoTouchTimeout(),this._state=0),!this._hasDetectedGesture()&&(this._state&=-105,this._state&=-17,this._stopLongpressTimeout(),!this._waitingRelease))switch(this._releaseStart=Date.now(),this._waitingRelease=!0,this._tracked.length){case 1:this._state&=-7;break;case 2:this._state&=-6}if(this._waitingRelease)if(Date.now()-this._releaseStart>250&&(this._state=0),this._tracked.some((function(e){return Date.now()-e.started>1e3}))&&(this._state=0),this._tracked.find((function(t){return t.id===e})).active=!1,this._hasDetectedGesture())this._pushEvent("gesturestart");else if(0!==this._state)return;this._hasDetectedGesture()&&this._pushEvent("gestureend");for(var n=0;n<this._tracked.length;n++)this._tracked[n].active&&this._ignored.push(this._tracked[n].id);this._tracked=[],this._state=0,-1!==this._ignored.indexOf(e)&&this._ignored.splice(this._ignored.indexOf(e),1),0===this._ignored.length&&(this._state=127,this._waitingRelease=!1)}},{key:"_hasDetectedGesture",value:function(){return!(0===this._state||this._state&this._state-1||7&this._state&&this._tracked.some((function(e){return e.active})))}},{key:"_startLongpressTimeout",value:function(){var e=this;this._stopLongpressTimeout(),this._longpressTimeoutId=setTimeout((function(){return e._longpressTimeout()}),1e3)}},{key:"_stopLongpressTimeout",value:function(){clearTimeout(this._longpressTimeoutId),this._longpressTimeoutId=null}},{key:"_longpressTimeout",value:function(){if(this._hasDetectedGesture())throw new Error("A longpress gesture failed, conflict with a different gesture");this._state=16,this._pushEvent("gesturestart")}},{key:"_startTwoTouchTimeout",value:function(){var e=this;this._stopTwoTouchTimeout(),this._twoTouchTimeoutId=setTimeout((function(){return e._twoTouchTimeout()}),50)}},{key:"_stopTwoTouchTimeout",value:function(){clearTimeout(this._twoTouchTimeoutId),this._twoTouchTimeoutId=null}},{key:"_isTwoTouchTimeoutRunning",value:function(){return null!==this._twoTouchTimeoutId}},{key:"_twoTouchTimeout",value:function(){if(0===this._tracked.length)throw new Error("A pinch or two drag gesture failed, no tracked touches");var e=this._getAverageMovement(),t=Math.abs(e.x),r=Math.abs(e.y),n=this._getAverageDistance(),i=Math.abs(Math.hypot(n.first.x,n.first.y)-Math.hypot(n.last.x,n.last.y));this._state=r<i&&t<i?64:32,this._pushEvent("gesturestart"),this._pushEvent("gesturemove")}},{key:"_pushEvent",value:function(e){var t={type:this._stateToGesture(this._state)},r=this._getPosition(),n=r.last;switch("gesturestart"===e&&(n=r.first),this._state){case 32:case 64:n=r.first}if(t.clientX=n.x,t.clientY=n.y,64===this._state){var i=this._getAverageDistance();"gesturestart"===e?(t.magnitudeX=i.first.x,t.magnitudeY=i.first.y):(t.magnitudeX=i.last.x,t.magnitudeY=i.last.y)}else if(32===this._state)if("gesturestart"===e)t.magnitudeX=0,t.magnitudeY=0;else{var o=this._getAverageMovement();t.magnitudeX=o.x,t.magnitudeY=o.y}var a=new CustomEvent(e,{detail:t});this._target.dispatchEvent(a)}},{key:"_stateToGesture",value:function(e){switch(e){case 1:return"onetap";case 2:return"twotap";case 4:return"threetap";case 8:return"drag";case 16:return"longpress";case 32:return"twodrag";case 64:return"pinch"}throw new Error("Unknown gesture state: "+e)}},{key:"_getPosition",value:function(){if(0===this._tracked.length)throw new Error("Failed to get gesture position, no tracked touches");for(var e=this._tracked.length,t=0,r=0,n=0,i=0,o=0;o<this._tracked.length;o++)t+=this._tracked[o].firstX,r+=this._tracked[o].firstY,n+=this._tracked[o].lastX,i+=this._tracked[o].lastY;return{first:{x:t/e,y:r/e},last:{x:n/e,y:i/e}}}},{key:"_getAverageMovement",value:function(){if(0===this._tracked.length)throw new Error("Failed to get gesture movement, no tracked touches");var e,t;e=t=0;for(var r=this._tracked.length,n=0;n<this._tracked.length;n++)e+=this._tracked[n].lastX-this._tracked[n].firstX,t+=this._tracked[n].lastY-this._tracked[n].firstY;return{x:e/r,y:t/r}}},{key:"_getAverageDistance",value:function(){if(0===this._tracked.length)throw new Error("Failed to get gesture distance, no tracked touches");var e=this._tracked[0],t=this._tracked[this._tracked.length-1];return{first:{x:Math.abs(t.firstX-e.firstX),y:Math.abs(t.firstY-e.firstY)},last:{x:Math.abs(t.lastX-e.lastX),y:Math.abs(t.lastY-e.lastY)}}}}])}()}(Fe)),Fe}var Pe,Me={};function Te(){return Pe||(Pe=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var t=g();function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function n(e,t,r){return t&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,i(n.key),n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function i(e){var t=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t);if("object"!=r(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==r(t)?t:t+""}var o=!t.supportsCursorURIs||t.isTouchDevice;e.default=function(){return n((function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this._target=null,this._canvas=document.createElement("canvas"),o&&(this._canvas.style.position="fixed",this._canvas.style.zIndex="65535",this._canvas.style.pointerEvents="none",this._canvas.style.userSelect="none",this._canvas.style.WebkitUserSelect="none",this._canvas.style.visibility="hidden"),this._position={x:0,y:0},this._hotSpot={x:0,y:0},this._eventHandlers={mouseover:this._handleMouseOver.bind(this),mouseleave:this._handleMouseLeave.bind(this),mousemove:this._handleMouseMove.bind(this),mouseup:this._handleMouseUp.bind(this)}}),[{key:"attach",value:function(e){if(this._target&&this.detach(),this._target=e,o){document.body.appendChild(this._canvas);var t={capture:!0,passive:!0};this._target.addEventListener("mouseover",this._eventHandlers.mouseover,t),this._target.addEventListener("mouseleave",this._eventHandlers.mouseleave,t),this._target.addEventListener("mousemove",this._eventHandlers.mousemove,t),this._target.addEventListener("mouseup",this._eventHandlers.mouseup,t)}this.clear()}},{key:"detach",value:function(){if(this._target){if(o){var e={capture:!0,passive:!0};this._target.removeEventListener("mouseover",this._eventHandlers.mouseover,e),this._target.removeEventListener("mouseleave",this._eventHandlers.mouseleave,e),this._target.removeEventListener("mousemove",this._eventHandlers.mousemove,e),this._target.removeEventListener("mouseup",this._eventHandlers.mouseup,e),document.contains(this._canvas)&&document.body.removeChild(this._canvas)}this._target=null}}},{key:"change",value:function(e,t,r,n,i){if(0!==n&&0!==i){this._position.x=this._position.x+this._hotSpot.x-t,this._position.y=this._position.y+this._hotSpot.y-r,this._hotSpot.x=t,this._hotSpot.y=r;var a=this._canvas.getContext("2d");this._canvas.width=n,this._canvas.height=i;var s=new ImageData(new Uint8ClampedArray(e),n,i);if(a.clearRect(0,0,n,i),a.putImageData(s,0,0),o)this._updatePosition();else{var u=this._canvas.toDataURL();this._target.style.cursor="url("+u+")"+t+" "+r+", default"}}else this.clear()}},{key:"clear",value:function(){this._target.style.cursor="none",this._canvas.width=0,this._canvas.height=0,this._position.x=this._position.x+this._hotSpot.x,this._position.y=this._position.y+this._hotSpot.y,this._hotSpot.x=0,this._hotSpot.y=0}},{key:"move",value:function(e,t){if(o){window.visualViewport?(this._position.x=e+window.visualViewport.offsetLeft,this._position.y=t+window.visualViewport.offsetTop):(this._position.x=e,this._position.y=t),this._updatePosition();var r=document.elementFromPoint(e,t);this._updateVisibility(r)}}},{key:"_handleMouseOver",value:function(e){this._handleMouseMove(e)}},{key:"_handleMouseLeave",value:function(e){this._updateVisibility(e.relatedTarget)}},{key:"_handleMouseMove",value:function(e){this._updateVisibility(e.target),this._position.x=e.clientX-this._hotSpot.x,this._position.y=e.clientY-this._hotSpot.y,this._updatePosition()}},{key:"_handleMouseUp",value:function(e){var t=this,r=document.elementFromPoint(e.clientX,e.clientY);this._updateVisibility(r),this._captureIsActive()&&window.setTimeout((function(){t._target&&(r=document.elementFromPoint(e.clientX,e.clientY),t._updateVisibility(r))}),0)}},{key:"_showCursor",value:function(){"hidden"===this._canvas.style.visibility&&(this._canvas.style.visibility="")}},{key:"_hideCursor",value:function(){"hidden"!==this._canvas.style.visibility&&(this._canvas.style.visibility="hidden")}},{key:"_shouldShowCursor",value:function(e){return!!e&&(e===this._target||!(!this._target.contains(e)||"none"!==window.getComputedStyle(e).cursor))}},{key:"_updateVisibility",value:function(e){this._captureIsActive()&&(e=document.captureElement),this._shouldShowCursor(e)?this._showCursor():this._hideCursor()}},{key:"_updatePosition",value:function(){this._canvas.style.left=this._position.x+"px",this._canvas.style.top=this._position.y+"px"}},{key:"_captureIsActive",value:function(){return document.captureElement&&document.documentElement.contains(document.captureElement)}}])}()}(Me)),Me}var Qe,Re={};function Oe(){return Qe||(Qe=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var t=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=n(e)&&"function"!=typeof e)return{default:e};var i=r(t);if(i&&i.has(e))return i.get(e);var o={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&{}.hasOwnProperty.call(e,s)){var u=a?Object.getOwnPropertyDescriptor(e,s):null;u&&(u.get||u.set)?Object.defineProperty(o,s,u):o[s]=e[s]}return o.default=e,i&&i.set(e,o),o}(f());function r(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(r=function(e){return e?n:t})(e)}function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e){return function(e){if(Array.isArray(e))return o(e)}(e)||function(e){if(typeof Symbol<"u"&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return o(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?o(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function a(e,t,r){return t&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,s(n.key),n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function s(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t);if("object"!=n(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==n(t)?t:t+""}var u=41943040,c="connecting",l="open",h="closing",d="closed",_={CONNECTING:[WebSocket.CONNECTING,c],OPEN:[WebSocket.OPEN,l],CLOSING:[WebSocket.CLOSING,h],CLOSED:[WebSocket.CLOSED,d]},p=["send","close","binaryType","onerror","onmessage","onopen","protocol","readyState"];e.default=function(){return a((function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this._websocket=null,this._rQi=0,this._rQlen=0,this._rQbufferSize=4194304,this._rQ=null,this._sQbufferSize=10240,this._sQlen=0,this._sQ=null,this._eventHandlers={message:function(){},open:function(){},close:function(){},error:function(){}}}),[{key:"readyState",get:function(){var e;return null===this._websocket?"unused":(e=this._websocket.readyState,_.CONNECTING.includes(e)?"connecting":_.OPEN.includes(e)?"open":_.CLOSING.includes(e)?"closing":_.CLOSED.includes(e)?"closed":"unknown")}},{key:"rQpeek8",value:function(){return this._rQ[this._rQi]}},{key:"rQskipBytes",value:function(e){this._rQi+=e}},{key:"rQshift8",value:function(){return this._rQshift(1)}},{key:"rQshift16",value:function(){return this._rQshift(2)}},{key:"rQshift32",value:function(){return this._rQshift(4)}},{key:"_rQshift",value:function(e){for(var t=0,r=e-1;r>=0;r--)t+=this._rQ[this._rQi++]<<8*r;return t>>>0}},{key:"rQshiftStr",value:function(e){for(var t="",r=0;r<e;r+=4096){var n=this.rQshiftBytes(Math.min(4096,e-r),!1);t+=String.fromCharCode.apply(null,n)}return t}},{key:"rQshiftBytes",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return this._rQi+=e,t?this._rQ.slice(this._rQi-e,this._rQi):this._rQ.subarray(this._rQi-e,this._rQi)}},{key:"rQshiftTo",value:function(e,t){e.set(new Uint8Array(this._rQ.buffer,this._rQi,t)),this._rQi+=t}},{key:"rQpeekBytes",value:function(e){return!(arguments.length>1&&void 0!==arguments[1])||arguments[1]?this._rQ.slice(this._rQi,this._rQi+e):this._rQ.subarray(this._rQi,this._rQi+e)}},{key:"rQwait",value:function(e,t,r){if(this._rQlen-this._rQi<t){if(r){if(this._rQi<r)throw new Error("rQwait cannot backup "+r+" bytes");this._rQi-=r}return!0}return!1}},{key:"sQpush8",value:function(e){this._sQensureSpace(1),this._sQ[this._sQlen++]=e}},{key:"sQpush16",value:function(e){this._sQensureSpace(2),this._sQ[this._sQlen++]=e>>8&255,this._sQ[this._sQlen++]=255&e}},{key:"sQpush32",value:function(e){this._sQensureSpace(4),this._sQ[this._sQlen++]=e>>24&255,this._sQ[this._sQlen++]=e>>16&255,this._sQ[this._sQlen++]=e>>8&255,this._sQ[this._sQlen++]=255&e}},{key:"sQpushString",value:function(e){var t=e.split("").map((function(e){return e.charCodeAt(0)}));this.sQpushBytes(new Uint8Array(t))}},{key:"sQpushBytes",value:function(e){for(var t=0;t<e.length;){this._sQensureSpace(1);var r=this._sQbufferSize-this._sQlen;r>e.length-t&&(r=e.length-t),this._sQ.set(e.subarray(t,r),this._sQlen),this._sQlen+=r,t+=r}}},{key:"flush",value:function(){this._sQlen>0&&"open"===this.readyState&&(this._websocket.send(new Uint8Array(this._sQ.buffer,0,this._sQlen)),this._sQlen=0)}},{key:"_sQensureSpace",value:function(e){this._sQbufferSize-this._sQlen<e&&this.flush()}},{key:"off",value:function(e){this._eventHandlers[e]=function(){}}},{key:"on",value:function(e,t){this._eventHandlers[e]=t}},{key:"_allocateBuffers",value:function(){this._rQ=new Uint8Array(this._rQbufferSize),this._sQ=new Uint8Array(this._sQbufferSize)}},{key:"init",value:function(){this._allocateBuffers(),this._rQi=0,this._websocket=null}},{key:"open",value:function(e,t){this.attach(new WebSocket(e,t))}},{key:"attach",value:function(e){var r=this;this.init();for(var n=[].concat(i(Object.keys(e)),i(Object.getOwnPropertyNames(Object.getPrototypeOf(e)))),o=0;o<p.length;o++){var a=p[o];if(n.indexOf(a)<0)throw new Error("Raw channel missing property: "+a)}this._websocket=e,this._websocket.binaryType="arraybuffer",this._websocket.onmessage=this._recvMessage.bind(this),this._websocket.onopen=function(){t.Debug(">> WebSock.onopen"),r._websocket.protocol&&t.Info("Server choose sub-protocol: "+r._websocket.protocol),r._eventHandlers.open(),t.Debug("<< WebSock.onopen")},this._websocket.onclose=function(e){t.Debug(">> WebSock.onclose"),r._eventHandlers.close(e),t.Debug("<< WebSock.onclose")},this._websocket.onerror=function(e){t.Debug(">> WebSock.onerror: "+e),r._eventHandlers.error(e),t.Debug("<< WebSock.onerror: "+e)}}},{key:"close",value:function(){this._websocket&&(("connecting"===this.readyState||"open"===this.readyState)&&(t.Info("Closing WebSocket connection"),this._websocket.close()),this._websocket.onmessage=function(){})}},{key:"_expandCompactRQ",value:function(e){var t=8*(this._rQlen-this._rQi+e),r=this._rQbufferSize<t;if(r&&(this._rQbufferSize=Math.max(2*this._rQbufferSize,t)),this._rQbufferSize>u&&(this._rQbufferSize=u,this._rQbufferSize-(this._rQlen-this._rQi)<e))throw new Error("Receive Queue buffer exceeded "+u+" bytes, and the new message could not fit");if(r){var n=this._rQ.buffer;this._rQ=new Uint8Array(this._rQbufferSize),this._rQ.set(new Uint8Array(n,this._rQi,this._rQlen-this._rQi))}else this._rQ.copyWithin(0,this._rQi,this._rQlen);this._rQlen=this._rQlen-this._rQi,this._rQi=0}},{key:"_recvMessage",value:function(e){this._rQlen==this._rQi&&(this._rQlen=0,this._rQi=0);var r=new Uint8Array(e.data);r.length>this._rQbufferSize-this._rQlen&&this._expandCompactRQ(r.length),this._rQ.set(r,this._rQlen),this._rQlen+=r.length,this._rQlen-this._rQi>0?this._eventHandlers.message():t.Debug("Ignoring empty message")}}])}()}(Re)),Re}var Be,De={};var je,Ie={};var Ue,Ne={},ze={},He={};function Ve(){if(Ue)return He;function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)}function t(){t=function(){return n};var r,n={},i=Object.prototype,o=i.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},s="function"==typeof Symbol?Symbol:{},u=s.iterator||"@@iterator",c=s.asyncIterator||"@@asyncIterator",l=s.toStringTag||"@@toStringTag";function h(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{h({},"")}catch{h=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var i=t&&t.prototype instanceof m?t:m,o=Object.create(i.prototype),s=new P(n||[]);return a(o,"_invoke",{value:C(e,r,s)}),o}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(n){return{type:"throw",arg:n}}}n.wrap=f;var _="suspendedStart",p="suspendedYield",y="executing",v="completed",g={};function m(){}function b(){}function w(){}var k={};h(k,u,(function(){return this}));var X=Object.getPrototypeOf,K=X&&X(X(M([])));K&&K!==i&&o.call(K,u)&&(k=K);var S=w.prototype=m.prototype=Object.create(k);function E(e){["next","throw","return"].forEach((function(t){h(e,t,(function(e){return this._invoke(t,e)}))}))}function x(t,r){function n(i,a,s,u){var c=d(t[i],t,a);if("throw"!==c.type){var l=c.arg,h=l.value;return h&&"object"==e(h)&&o.call(h,"__await")?r.resolve(h.__await).then((function(e){n("next",e,s,u)}),(function(e){n("throw",e,s,u)})):r.resolve(h).then((function(e){l.value=e,s(l)}),(function(e){return n("throw",e,s,u)}))}u(c.arg)}var i;a(this,"_invoke",{value:function(e,t){function o(){return new r((function(r,i){n(e,t,r,i)}))}return i=i?i.then(o,o):o()}})}function C(e,t,n){var i=_;return function(o,a){if(i===y)throw Error("Generator is already running");if(i===v){if("throw"===o)throw a;return{value:r,done:!0}}for(n.method=o,n.arg=a;;){var s=n.delegate;if(s){var u=A(s,n);if(u){if(u===g)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===_)throw i=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=y;var c=d(e,t,n);if("normal"===c.type){if(i=n.done?v:p,c.arg===g)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(i=v,n.method="throw",n.arg=c.arg)}}}function A(e,t){var n=t.method,i=e.iterator[n];if(i===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=r,A(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var o=d(i,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,g;var a=o.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=r),t.delegate=null,g):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,g)}function F(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function L(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(F,this),this.reset(!0)}function M(t){if(t||""===t){var n=t[u];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,a=function e(){for(;++i<t.length;)if(o.call(t,i))return e.value=t[i],e.done=!1,e;return e.value=r,e.done=!0,e};return a.next=a}}throw new TypeError(e(t)+" is not iterable")}return b.prototype=w,a(S,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:b,configurable:!0}),b.displayName=h(w,l,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,h(e,l,"GeneratorFunction")),e.prototype=Object.create(S),e},n.awrap=function(e){return{__await:e}},E(x.prototype),h(x.prototype,c,(function(){return this})),n.AsyncIterator=x,n.async=function(e,t,r,i,o){void 0===o&&(o=Promise);var a=new x(f(e,t,r,i),o);return n.isGeneratorFunction(t)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},E(S),h(S,l,"Generator"),h(S,u,(function(){return this})),h(S,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},n.values=M,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(L),!e)for(var t in this)"t"===t.charAt(0)&&o.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=r)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(n,i){return s.type="throw",s.arg=e,t.next=n,i&&(t.method="next",t.arg=r),!!i}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var u=o.call(a,"catchLoc"),c=o.call(a,"finallyLoc");if(u&&c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),L(r),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;L(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:M(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=r),g}},n}function r(e,t,r,n,i,o,a){try{var s=e[o](a),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,i)}function n(e){return function(){var t=this,n=arguments;return new Promise((function(i,o){var a=e.apply(t,n);function s(e){r(a,i,o,s,u,"next",e)}function u(e){r(a,i,o,s,u,"throw",e)}s(void 0)}))}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,s(n.key),n)}}function a(e,t,r){return t&&o(e.prototype,t),r&&o(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function s(t){var r=function(t,r){if("object"!=e(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,r);if("object"!=e(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==e(r)?r:r+""}return Ue=1,Object.defineProperty(He,"__esModule",{value:!0}),He.AESECBCipher=He.AESEAXCipher=void 0,He.AESECBCipher=function(){function e(){i(this,e),this._key=null}return a(e,[{key:"algorithm",get:function(){return{name:"AES-ECB"}}},{key:"_importKey",value:(r=n(t().mark((function e(r,n,i){return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,window.crypto.subtle.importKey("raw",r,{name:"AES-CBC"},n,i);case 2:this._key=e.sent;case 3:case"end":return e.stop()}}),e,this)}))),function(e,t,n){return r.apply(this,arguments)})},{key:"encrypt",value:function(){var e=n(t().mark((function e(r,n){var i,o,a,s;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if((i=new Uint8Array(n)).length%16==0&&null!==this._key){e.next=3;break}return e.abrupt("return",null);case 3:o=i.length/16,a=0;case 5:if(!(a<o)){e.next=15;break}return e.t0=Uint8Array,e.next=9,window.crypto.subtle.encrypt({name:"AES-CBC",iv:new Uint8Array(16)},this._key,i.slice(16*a,16*a+16));case 9:e.t1=e.sent,s=new e.t0(e.t1).slice(0,16),i.set(s,16*a);case 12:a++,e.next=5;break;case 15:return e.abrupt("return",i);case 16:case"end":return e.stop()}}),e,this)})));return function(t,r){return e.apply(this,arguments)}}()}],[{key:"importKey",value:function(){var r=n(t().mark((function r(n,i,o,a){var s;return t().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return s=new e,t.next=3,s._importKey(n,o,a);case 3:return t.abrupt("return",s);case 4:case"end":return t.stop()}}),r)})));return function(e,t,n,i){return r.apply(this,arguments)}}()}]);var r}(),He.AESEAXCipher=function(){function e(){i(this,e),this._rawKey=null,this._ctrKey=null,this._cbcKey=null,this._zeroBlock=new Uint8Array(16),this._prefixBlock0=this._zeroBlock,this._prefixBlock1=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1]),this._prefixBlock2=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2])}return a(e,[{key:"algorithm",get:function(){return{name:"AES-EAX"}}},{key:"_encryptBlock",value:(r=n(t().mark((function e(r){var n;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,window.crypto.subtle.encrypt({name:"AES-CBC",iv:this._zeroBlock},this._cbcKey,r);case 2:return n=e.sent,e.abrupt("return",new Uint8Array(n).slice(0,16));case 4:case"end":return e.stop()}}),e,this)}))),function(e){return r.apply(this,arguments)})},{key:"_initCMAC",value:function(){var e=n(t().mark((function e(){var r,n,i,o,a;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this._encryptBlock(this._zeroBlock);case 2:for(r=e.sent,n=new Uint8Array(16),i=r[0]>>>6,o=0;o<15;o++)n[o]=r[o+1]>>6|r[o]<<2,r[o]=r[o+1]>>7|r[o]<<1;a=[0,135,14,137],n[14]^=i>>>1,n[15]=r[15]<<2^a[i],r[15]=r[15]<<1^a[i>>1],this._k1=r,this._k2=n;case 12:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"_encryptCTR",value:function(){var e=n(t().mark((function e(r,n){var i;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,window.crypto.subtle.encrypt({name:"AES-CTR",counter:n,length:128},this._ctrKey,r);case 2:return i=e.sent,e.abrupt("return",new Uint8Array(i));case 4:case"end":return e.stop()}}),e,this)})));return function(t,r){return e.apply(this,arguments)}}()},{key:"_decryptCTR",value:function(){var e=n(t().mark((function e(r,n){var i;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,window.crypto.subtle.decrypt({name:"AES-CTR",counter:n,length:128},this._ctrKey,r);case 2:return i=e.sent,e.abrupt("return",new Uint8Array(i));case 4:case"end":return e.stop()}}),e,this)})));return function(t,r){return e.apply(this,arguments)}}()},{key:"_computeCMAC",value:function(){var e=n(t().mark((function e(r,n){var i,o,a,s,u,c,l,h;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(16===n.length){e.next=2;break}return e.abrupt("return",null);case 2:if(i=Math.floor(r.length/16),o=Math.ceil(r.length/16),a=r.length-16*i,(s=new Uint8Array(16*(o+1))).set(n),s.set(r,16),0===a)for(u=0;u<16;u++)s[16*i+u]^=this._k1[u];else for(s[16*(i+1)+a]=128,c=0;c<16;c++)s[16*(i+1)+c]^=this._k2[c];return e.next=11,window.crypto.subtle.encrypt({name:"AES-CBC",iv:this._zeroBlock},this._cbcKey,s);case 11:return l=e.sent,l=new Uint8Array(l),h=l.slice(l.length-32,l.length-16),e.abrupt("return",h);case 15:case"end":return e.stop()}}),e,this)})));return function(t,r){return e.apply(this,arguments)}}()},{key:"_importKey",value:function(){var e=n(t().mark((function e(r){return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this._rawKey=r,e.next=3,window.crypto.subtle.importKey("raw",r,{name:"AES-CTR"},!1,["encrypt","decrypt"]);case 3:return this._ctrKey=e.sent,e.next=6,window.crypto.subtle.importKey("raw",r,{name:"AES-CBC"},!1,["encrypt"]);case 6:return this._cbcKey=e.sent,e.next=9,this._initCMAC();case 9:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"encrypt",value:function(){var e=n(t().mark((function e(r,n){var i,o,a,s,u,c,l,h;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=r.additionalData,o=r.iv,e.next=4,this._computeCMAC(o,this._prefixBlock0);case 4:return a=e.sent,e.next=7,this._encryptCTR(n,a);case 7:return s=e.sent,e.next=10,this._computeCMAC(i,this._prefixBlock1);case 10:return u=e.sent,e.next=13,this._computeCMAC(s,this._prefixBlock2);case 13:for(c=e.sent,l=0;l<16;l++)c[l]^=a[l]^u[l];return(h=new Uint8Array(16+s.length)).set(s),h.set(c,s.length),e.abrupt("return",h);case 19:case"end":return e.stop()}}),e,this)})));return function(t,r){return e.apply(this,arguments)}}()},{key:"decrypt",value:function(){var e=n(t().mark((function e(r,n){var i,o,a,s,u,c,l,h,f,d;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=n.slice(0,n.length-16),o=r.additionalData,a=r.iv,s=n.slice(n.length-16),e.next=6,this._computeCMAC(a,this._prefixBlock0);case 6:return u=e.sent,e.next=9,this._computeCMAC(o,this._prefixBlock1);case 9:return c=e.sent,e.next=12,this._computeCMAC(i,this._prefixBlock2);case 12:for(l=e.sent,h=0;h<16;h++)l[h]^=u[h]^c[h];if(l.length===s.length){e.next=16;break}return e.abrupt("return",null);case 16:f=0;case 17:if(!(f<s.length)){e.next=23;break}if(l[f]===s[f]){e.next=20;break}return e.abrupt("return",null);case 20:f++,e.next=17;break;case 23:return e.next=25,this._decryptCTR(i,u);case 25:return d=e.sent,e.abrupt("return",d);case 27:case"end":return e.stop()}}),e,this)})));return function(t,r){return e.apply(this,arguments)}}()}],[{key:"importKey",value:function(){var r=n(t().mark((function r(n,i,o,a){var s;return t().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return s=new e,t.next=3,s._importKey(n);case 3:return t.abrupt("return",s);case 4:case"end":return t.stop()}}),r)})));return function(e,t,n,i){return r.apply(this,arguments)}}()}]);var r}(),He}var We,Ge={};function Ze(){if(We)return Ge;function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)}function t(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function r(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,i(n.key),n)}}function n(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function i(t){var r=function(t,r){if("object"!=e(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,r);if("object"!=e(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==e(r)?r:r+""}We=1,Object.defineProperty(Ge,"__esModule",{value:!0}),Ge.DESECBCipher=Ge.DESCBCCipher=void 0;var o,a,s,u,c,l,h=[13,16,10,23,0,4,2,27,14,5,20,9,22,18,11,3,25,7,15,6,26,19,12,1,40,51,30,36,46,54,29,39,50,44,32,47,43,48,38,55,33,52,45,41,49,35,28,31],f=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],d=[(s=(o=65536)|(a=1<<24))|(c=1024),0,0|o,s|(l=(u=4)|c),s|u,o|l,0|u,0|o,0|c,s|c,s|l,0|c,a|l,s|u,0|a,0|u,0|l,a|c,a|c,o|c,o|c,0|s,0|s,a|l,o|u,a|u,a|u,o|u,0,0|l,o|l,0|a,0|o,s|l,0|u,0|s,s|c,0|a,0|a,0|c,s|u,0|o,o|c,a|u,0|c,0|u,a|l,o|l,s|l,o|u,0|s,a|l,a|u,0|l,o|l,s|c,0|l,a|c,a|c,0,o|u,o|c,0,s|u],_=[(s=(o=1<<20)|(a=1<<31))|(l=(u=32)|(c=32768)),a|c,0|c,o|l,0|o,0|u,s|u,a|l,a|u,s|l,s|c,0|a,a|c,0|o,0|u,s|u,o|c,o|u,a|l,0,0|a,0|c,o|l,0|s,o|u,a|u,0,o|c,0|l,s|c,0|s,0|l,0,o|l,s|u,0|o,a|l,0|s,s|c,0|c,0|s,a|c,0|u,s|l,o|l,0|u,0|c,0|a,0|l,s|c,0|o,a|u,o|u,a|l,a|u,o|u,o|c,0,a|c,0|l,0|a,s|u,s|l,o|c],p=[0|(l=(u=8)|(c=512)),(s=(o=1<<17)|(a=1<<27))|c,0,s|u,a|c,0,o|l,a|c,o|u,a|u,a|u,0|o,s|l,o|u,0|s,0|l,0|a,0|u,s|c,0|c,o|c,0|s,s|u,o|l,a|l,o|c,0|o,a|l,0|u,s|l,0|c,0|a,s|c,0|a,o|u,0|l,0|o,s|c,a|c,0,0|c,o|u,s|l,a|c,a|u,0|c,0,s|u,a|l,0|o,0|a,s|l,0|u,o|l,o|c,a|u,0|s,a|l,0|l,0|s,o|l,0|u,s|u,o|c],y=[(s=(o=8192)|(a=1<<23))|(u=1),o|(l=u|(c=128)),o|l,0|c,s|c,a|l,a|u,o|u,0,0|s,0|s,s|l,0|l,0,a|c,a|u,0|u,0|o,0|a,s|u,0|c,0|a,o|u,o|c,a|l,0|u,o|c,a|c,0|o,s|c,s|l,0|l,a|c,a|u,0|s,s|l,0|l,0,0,0|s,o|c,a|c,a|l,0|u,s|u,o|l,o|l,0|c,s|l,0|l,0|u,0|o,a|u,o|u,s|c,a|l,o|u,o|c,0|a,s|u,0|c,0|a,0|o,s|c],v=[0|(u=256),(o=1<<25)|(l=u|(c=1<<19)),o|c,(s=o|(a=1<<30))|u,0|c,0|u,0|a,o|c,a|l,0|c,o|u,a|l,s|u,s|c,0|l,0|a,0|o,a|c,a|c,0,a|u,s|l,s|l,o|u,s|c,a|u,0,0|s,o|l,0|o,0|s,0|l,0|c,s|u,0|u,0|o,0|a,o|c,s|u,a|l,o|u,0|a,s|c,o|l,a|l,0|u,0|o,s|c,s|l,0|l,0|s,s|l,o|c,0,a|c,0|s,0|l,o|u,a|u,0|c,0,a|c,o|l,a|u],g=[(a=1<<29)|(u=16),0|(s=(o=1<<22)|a),0|(c=16384),s|(l=u|c),0|s,0|u,s|l,0|o,a|c,o|l,0|o,a|u,o|u,a|c,0|a,0|l,0,o|u,a|l,0|c,o|c,a|l,0|u,s|u,s|u,0,o|l,s|c,0|l,o|c,s|c,0|a,a|c,0|u,s|u,o|c,s|l,0|o,0|l,a|u,0|o,a|c,0|a,0|l,a|u,s|l,o|c,0|s,o|l,s|c,0,s|u,0|u,0|c,0|s,o|l,0|c,o|u,a|l,0,s|c,0|a,o|u,a|l],m=[0|(o=1<<21),(s=o|(a=1<<26))|(u=2),a|(l=u|(c=2048)),0,0|c,a|l,o|l,s|c,s|l,0|o,0,a|u,0|u,0|a,s|u,0|l,a|c,o|l,o|u,a|c,a|u,0|s,s|c,o|u,0|s,0|c,0|l,s|l,o|c,0|u,0|a,o|c,0|a,o|c,0|o,a|l,a|l,s|u,s|u,0|u,o|u,0|a,a|c,0|o,s|c,0|l,o|l,s|c,0|l,a|u,s|l,0|s,o|c,0,0|u,s|l,0,o|l,0|s,0|c,a|u,a|c,0|c,o|u],b=[(a=1<<28)|(l=(u=64)|(c=4096)),0|c,0|(o=1<<18),(s=o|a)|l,0|a,a|l,0|u,0|a,o|u,0|s,s|l,o|c,s|c,o|l,0|c,0|u,0|s,a|u,a|c,0|l,o|c,o|u,s|u,s|c,0|l,0,0,s|u,a|u,a|c,o|l,0|o,o|l,0|o,s|c,0|c,0|u,s|u,0|c,o|l,a|c,0|u,a|u,0|s,s|u,0|a,0|o,a|l,0,s|l,o|u,a|u,0|s,a|c,a|l,0,s|l,o|c,o|c,0|l,0|l,o|u,0|a,s|c],w=function(){return n((function e(r){t(this,e),this.keys=[];for(var n=[],i=[],o=[],a=0,s=56;a<56;++a,s-=8){var u=7&(s+=s<-5?65:s<-3?31:s<-1?63:27===s?35:0);n[a]=r[s>>>3]&1<<u?1:0}for(var c=0;c<16;++c){var l=c<<1,d=l+1;o[l]=o[d]=0;for(var _=28;_<59;_+=28)for(var p=_-28;p<_;++p){var y=p+f[c];i[p]=y<_?n[y]:n[y-28]}for(var v=0;v<24;++v)0!==i[h[v]]&&(o[l]|=1<<23-v),0!==i[h[v+24]]&&(o[d]|=1<<23-v)}for(var g=0,m=0,b=0;g<16;++g){var w=o[m++],k=o[m++];this.keys[b]=(16515072&w)<<6,this.keys[b]|=(4032&w)<<10,this.keys[b]|=(16515072&k)>>>10,this.keys[b]|=(4032&k)>>>6,++b,this.keys[b]=(258048&w)<<12,this.keys[b]|=(63&w)<<16,this.keys[b]|=(258048&k)>>>4,this.keys[b]|=63&k,++b}}),[{key:"enc8",value:function(e){var t,r,n,i=e.slice(),o=0;t=i[o++]<<24|i[o++]<<16|i[o++]<<8|i[o++],r=i[o++]<<24|i[o++]<<16|i[o++]<<8|i[o++],r^=n=252645135&(t>>>4^r),r^=n=65535&((t^=n<<4)>>>16^r),r^=(n=858993459&(r>>>2^(t^=n<<16)))<<2,r=(r^=(n=16711935&(r>>>8^(t^=n)))<<8)<<1|r>>>31&1,r^=n=2863311530&((t^=n)^r),t=(t^=n)<<1|t>>>31&1;for(var a=0,s=0;a<8;++a){n=r<<28|r>>>4,n^=this.keys[s++];var u=m[63&n];u|=v[n>>>8&63],u|=p[n>>>16&63],u|=d[n>>>24&63],n=r^this.keys[s++],u|=b[63&n],u|=g[n>>>8&63],u|=y[n>>>16&63],n=(t^=u|=_[n>>>24&63])<<28|t>>>4,n^=this.keys[s++],u=m[63&n],u|=v[n>>>8&63],u|=p[n>>>16&63],u|=d[n>>>24&63],n=t^this.keys[s++],u|=b[63&n],u|=g[n>>>8&63],u|=y[n>>>16&63],r^=u|=_[n>>>24&63]}for(r=r<<31|r>>>1,r^=n=2863311530&(t^r),r^=n=16711935&((t=(t^=n)<<31|t>>>1)>>>8^r),r^=n=858993459&((t^=n<<8)>>>2^r),r^=(n=65535&(r>>>16^(t^=n<<2)))<<16,n=[r^=(n=252645135&(r>>>4^(t^=n)))<<4,t^=n],o=0;o<8;o++)i[o]=(n[o>>>2]>>>8*(3-o%4))%256,i[o]<0&&(i[o]+=256);return i}}])}();return Ge.DESECBCipher=function(){function e(){t(this,e),this._cipher=null}return n(e,[{key:"algorithm",get:function(){return{name:"DES-ECB"}}},{key:"_importKey",value:function(e,t,r){this._cipher=new w(e)}},{key:"encrypt",value:function(e,t){var r=new Uint8Array(t);if(r.length%8!=0||null===this._cipher)return null;for(var n=r.length/8,i=0;i<n;i++)r.set(this._cipher.enc8(r.slice(8*i,8*i+8)),8*i);return r}}],[{key:"importKey",value:function(t,r,n,i){var o=new e;return o._importKey(t),o}}])}(),Ge.DESCBCCipher=function(){function e(){t(this,e),this._cipher=null}return n(e,[{key:"algorithm",get:function(){return{name:"DES-CBC"}}},{key:"_importKey",value:function(e){this._cipher=new w(e)}},{key:"encrypt",value:function(e,t){var r=new Uint8Array(t),n=new Uint8Array(e.iv);if(r.length%8!=0||null===this._cipher)return null;for(var i=r.length/8,o=0;o<i;o++){for(var a=0;a<8;a++)n[a]^=t[8*o+a];n=this._cipher.enc8(n),r.set(n,8*o)}return r}}],[{key:"importKey",value:function(t,r,n,i){var o=new e;return o._importKey(t),o}}])}(),Ge}var Ye,qe,Je={},$e={};function et(){if(Ye)return $e;return Ye=1,Object.defineProperty($e,"__esModule",{value:!0}),$e.bigIntToU8Array=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=e.toString(16);0===t&&(t=Math.ceil(r.length/2));for(var n=(r=r.padStart(2*t,"0")).length/2,i=new Uint8Array(n),o=0;o<n;o++)i[o]=parseInt(r.slice(2*o,2*o+2),16);return i},$e.modPow=function(e,t,r){var n=1n;for(e%=r;t>0n;)1n===(1n&t)&&(n=n*e%r),t>>=1n,e=e*e%r;return n},$e.u8ArrayToBigInt=function(e){for(var t="0x",r=0;r<e.length;r++)t+=e[r].toString(16).padStart(2,"0");return BigInt(t)},$e}function tt(){if(qe)return Je;qe=1,Object.defineProperty(Je,"__esModule",{value:!0}),Je.RSACipher=void 0;var e,t=(e=L())&&e.__esModule?e:{default:e},r=et();function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(){i=function(){return t};var e,t={},r=Object.prototype,o=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},s="function"==typeof Symbol?Symbol:{},u=s.iterator||"@@iterator",c=s.asyncIterator||"@@asyncIterator",l=s.toStringTag||"@@toStringTag";function h(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{h({},"")}catch{h=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var i=t&&t.prototype instanceof m?t:m,o=Object.create(i.prototype),s=new P(n||[]);return a(o,"_invoke",{value:C(e,r,s)}),o}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(n){return{type:"throw",arg:n}}}t.wrap=f;var _="suspendedStart",p="suspendedYield",y="executing",v="completed",g={};function m(){}function b(){}function w(){}var k={};h(k,u,(function(){return this}));var X=Object.getPrototypeOf,K=X&&X(X(M([])));K&&K!==r&&o.call(K,u)&&(k=K);var S=w.prototype=m.prototype=Object.create(k);function E(e){["next","throw","return"].forEach((function(t){h(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function r(i,a,s,u){var c=d(e[i],e,a);if("throw"!==c.type){var l=c.arg,h=l.value;return h&&"object"==n(h)&&o.call(h,"__await")?t.resolve(h.__await).then((function(e){r("next",e,s,u)}),(function(e){r("throw",e,s,u)})):t.resolve(h).then((function(e){l.value=e,s(l)}),(function(e){return r("throw",e,s,u)}))}u(c.arg)}var i;a(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,i){r(e,n,t,i)}))}return i=i?i.then(o,o):o()}})}function C(t,r,n){var i=_;return function(o,a){if(i===y)throw Error("Generator is already running");if(i===v){if("throw"===o)throw a;return{value:e,done:!0}}for(n.method=o,n.arg=a;;){var s=n.delegate;if(s){var u=A(s,n);if(u){if(u===g)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===_)throw i=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=y;var c=d(t,r,n);if("normal"===c.type){if(i=n.done?v:p,c.arg===g)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(i=v,n.method="throw",n.arg=c.arg)}}}function A(t,r){var n=r.method,i=t.iterator[n];if(i===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,A(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var o=d(i,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,g;var a=o.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,g):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function F(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function L(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(F,this),this.reset(!0)}function M(t){if(t||""===t){var r=t[u];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,a=function r(){for(;++i<t.length;)if(o.call(t,i))return r.value=t[i],r.done=!1,r;return r.value=e,r.done=!0,r};return a.next=a}}throw new TypeError(n(t)+" is not iterable")}return b.prototype=w,a(S,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:b,configurable:!0}),b.displayName=h(w,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,h(e,l,"GeneratorFunction")),e.prototype=Object.create(S),e},t.awrap=function(e){return{__await:e}},E(x.prototype),h(x.prototype,c,(function(){return this})),t.AsyncIterator=x,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var a=new x(f(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},E(S),h(S,l,"Generator"),h(S,u,(function(){return this})),h(S,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=M,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(L),!t)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(n,i){return s.type="throw",s.arg=t,r.next=n,i&&(r.method="next",r.arg=e),!!i}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var u=o.call(a,"catchLoc"),c=o.call(a,"finallyLoc");if(u&&c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),L(r),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;L(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:M(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),g}},t}function o(e,t,r,n,i,o,a){try{var s=e[o](a),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,i)}function a(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var a=e.apply(t,r);function s(e){o(a,n,i,s,u,"next",e)}function u(e){o(a,n,i,s,u,"throw",e)}s(void 0)}))}}function s(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,u(n.key),n)}}function u(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t);if("object"!=n(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==n(t)?t:t+""}return Je.RSACipher=function(){function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this._keyLength=0,this._keyBytes=0,this._n=null,this._e=null,this._d=null,this._nBigInt=null,this._eBigInt=null,this._dBigInt=null,this._extractable=!1}return function(e,t,r){return t&&s(e.prototype,t),r&&s(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}(e,[{key:"algorithm",get:function(){return{name:"RSA-PKCS1-v1_5"}}},{key:"_base64urlDecode",value:function(e){return e=(e=e.replace(/-/g,"+").replace(/_/g,"/")).padEnd(4*Math.ceil(e.length/4),"="),t.default.decode(e)}},{key:"_padArray",value:function(e,t){var r=new Uint8Array(t);return r.set(e,t-e.length),r}},{key:"_generateKey",value:(n=a(i().mark((function e(t,n){var o,a;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this._keyLength=t.modulusLength,this._keyBytes=Math.ceil(this._keyLength/8),e.next=4,window.crypto.subtle.generateKey({name:"RSA-OAEP",modulusLength:t.modulusLength,publicExponent:t.publicExponent,hash:{name:"SHA-256"}},!0,["encrypt","decrypt"]);case 4:return o=e.sent,e.next=7,window.crypto.subtle.exportKey("jwk",o.privateKey);case 7:a=e.sent,this._n=this._padArray(this._base64urlDecode(a.n),this._keyBytes),this._nBigInt=(0,r.u8ArrayToBigInt)(this._n),this._e=this._padArray(this._base64urlDecode(a.e),this._keyBytes),this._eBigInt=(0,r.u8ArrayToBigInt)(this._e),this._d=this._padArray(this._base64urlDecode(a.d),this._keyBytes),this._dBigInt=(0,r.u8ArrayToBigInt)(this._d),this._extractable=n;case 15:case"end":return e.stop()}}),e,this)}))),function(e,t){return n.apply(this,arguments)})},{key:"_importKey",value:function(){var e=a(i().mark((function e(t,n){var o,a;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(o=t.n,a=t.e,o.length===a.length){e.next=4;break}throw new Error("the sizes of modulus and public exponent do not match");case 4:this._keyBytes=o.length,this._keyLength=8*this._keyBytes,this._n=new Uint8Array(this._keyBytes),this._e=new Uint8Array(this._keyBytes),this._n.set(o),this._e.set(a),this._nBigInt=(0,r.u8ArrayToBigInt)(this._n),this._eBigInt=(0,r.u8ArrayToBigInt)(this._e),this._extractable=n;case 13:case"end":return e.stop()}}),e,this)})));return function(t,r){return e.apply(this,arguments)}}()},{key:"encrypt",value:function(){var e=a(i().mark((function e(t,n){var o,a,s,u,c;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(n.length>this._keyBytes-11)){e.next=2;break}return e.abrupt("return",null);case 2:for(o=new Uint8Array(this._keyBytes-n.length-3),window.crypto.getRandomValues(o),a=0;a<o.length;a++)o[a]=Math.floor(254*o[a]/255+1);return(s=new Uint8Array(this._keyBytes))[1]=2,s.set(o,2),s.set(n,o.length+3),u=(0,r.u8ArrayToBigInt)(s),c=(0,r.modPow)(u,this._eBigInt,this._nBigInt),e.abrupt("return",(0,r.bigIntToU8Array)(c,this._keyBytes));case 12:case"end":return e.stop()}}),e,this)})));return function(t,r){return e.apply(this,arguments)}}()},{key:"decrypt",value:function(){var e=a(i().mark((function e(t,n){var o,a,s,u;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n.length===this._keyBytes){e.next=2;break}return e.abrupt("return",null);case 2:if(o=(0,r.u8ArrayToBigInt)(n),a=(0,r.modPow)(o,this._dBigInt,this._nBigInt),0===(s=(0,r.bigIntToU8Array)(a,this._keyBytes))[0]&&2===s[1]){e.next=7;break}return e.abrupt("return",null);case 7:u=2;case 8:if(!(u<s.length)){e.next=14;break}if(0!==s[u]){e.next=11;break}return e.abrupt("break",14);case 11:u++,e.next=8;break;case 14:if(u!==s.length){e.next=16;break}return e.abrupt("return",null);case 16:return e.abrupt("return",s.slice(u+1,s.length));case 17:case"end":return e.stop()}}),e,this)})));return function(t,r){return e.apply(this,arguments)}}()},{key:"exportKey",value:function(){var e=a(i().mark((function e(){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._extractable){e.next=2;break}throw new Error("key is not extractable");case 2:return e.abrupt("return",{n:this._n,e:this._e,d:this._d});case 3:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}],[{key:"generateKey",value:function(){var t=a(i().mark((function t(r,n,o){var a;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return a=new e,t.next=3,a._generateKey(r,n);case 3:return t.abrupt("return",{privateKey:a});case 4:case"end":return t.stop()}}),t)})));return function(e,r,n){return t.apply(this,arguments)}}()},{key:"importKey",value:function(){var t=a(i().mark((function t(r,n,o,a){var s;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(1===a.length&&"encrypt"===a[0]){t.next=2;break}throw new Error("only support importing RSA public key");case 2:return s=new e,t.next=5,s._importKey(r,o);case 5:return t.abrupt("return",s);case 6:case"end":return t.stop()}}),t)})));return function(e,r,n,i){return t.apply(this,arguments)}}()}]);var n}(),Je}var rt,nt={};function it(){if(rt)return nt;rt=1,Object.defineProperty(nt,"__esModule",{value:!0}),nt.DHCipher=void 0;var e=et();function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,o(n.key),n)}}function i(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function o(e){var r=function(e,r){if("object"!=t(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,r);if("object"!=t(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==t(r)?r:r+""}var a=function(){return i((function e(t){r(this,e),this._key=t}),[{key:"algorithm",get:function(){return{name:"DH"}}},{key:"exportKey",value:function(){return this._key}}])}();return nt.DHCipher=function(){function t(){r(this,t),this._g=null,this._p=null,this._gBigInt=null,this._pBigInt=null,this._privateKey=null}return i(t,[{key:"algorithm",get:function(){return{name:"DH"}}},{key:"_generateKey",value:function(t){var r=t.g,n=t.p;this._keyBytes=n.length,this._gBigInt=(0,e.u8ArrayToBigInt)(r),this._pBigInt=(0,e.u8ArrayToBigInt)(n),this._privateKey=window.crypto.getRandomValues(new Uint8Array(this._keyBytes)),this._privateKeyBigInt=(0,e.u8ArrayToBigInt)(this._privateKey),this._publicKey=(0,e.bigIntToU8Array)((0,e.modPow)(this._gBigInt,this._privateKeyBigInt,this._pBigInt),this._keyBytes)}},{key:"deriveBits",value:function(t,r){var n=Math.ceil(r/8),i=new Uint8Array(t.public),o=n>this._keyBytes?n:this._keyBytes,a=(0,e.modPow)((0,e.u8ArrayToBigInt)(i),this._privateKeyBigInt,this._pBigInt);return(0,e.bigIntToU8Array)(a,o).slice(0,o)}}],[{key:"generateKey",value:function(e,r){var n=new t;return n._generateKey(e),{privateKey:n,publicKey:new a(n._publicKey)}}}])}(),nt}var ot,at,st,ut={};function ct(){if(ot)return ut;function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)}function t(){t=function(){return n};var r,n={},i=Object.prototype,o=i.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},s="function"==typeof Symbol?Symbol:{},u=s.iterator||"@@iterator",c=s.asyncIterator||"@@asyncIterator",l=s.toStringTag||"@@toStringTag";function h(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{h({},"")}catch{h=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var i=t&&t.prototype instanceof m?t:m,o=Object.create(i.prototype),s=new P(n||[]);return a(o,"_invoke",{value:C(e,r,s)}),o}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(n){return{type:"throw",arg:n}}}n.wrap=f;var _="suspendedStart",p="suspendedYield",y="executing",v="completed",g={};function m(){}function b(){}function w(){}var k={};h(k,u,(function(){return this}));var X=Object.getPrototypeOf,K=X&&X(X(M([])));K&&K!==i&&o.call(K,u)&&(k=K);var S=w.prototype=m.prototype=Object.create(k);function E(e){["next","throw","return"].forEach((function(t){h(e,t,(function(e){return this._invoke(t,e)}))}))}function x(t,r){function n(i,a,s,u){var c=d(t[i],t,a);if("throw"!==c.type){var l=c.arg,h=l.value;return h&&"object"==e(h)&&o.call(h,"__await")?r.resolve(h.__await).then((function(e){n("next",e,s,u)}),(function(e){n("throw",e,s,u)})):r.resolve(h).then((function(e){l.value=e,s(l)}),(function(e){return n("throw",e,s,u)}))}u(c.arg)}var i;a(this,"_invoke",{value:function(e,t){function o(){return new r((function(r,i){n(e,t,r,i)}))}return i=i?i.then(o,o):o()}})}function C(e,t,n){var i=_;return function(o,a){if(i===y)throw Error("Generator is already running");if(i===v){if("throw"===o)throw a;return{value:r,done:!0}}for(n.method=o,n.arg=a;;){var s=n.delegate;if(s){var u=A(s,n);if(u){if(u===g)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===_)throw i=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=y;var c=d(e,t,n);if("normal"===c.type){if(i=n.done?v:p,c.arg===g)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(i=v,n.method="throw",n.arg=c.arg)}}}function A(e,t){var n=t.method,i=e.iterator[n];if(i===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=r,A(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var o=d(i,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,g;var a=o.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=r),t.delegate=null,g):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,g)}function F(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function L(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(F,this),this.reset(!0)}function M(t){if(t||""===t){var n=t[u];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,a=function e(){for(;++i<t.length;)if(o.call(t,i))return e.value=t[i],e.done=!1,e;return e.value=r,e.done=!0,e};return a.next=a}}throw new TypeError(e(t)+" is not iterable")}return b.prototype=w,a(S,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:b,configurable:!0}),b.displayName=h(w,l,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,h(e,l,"GeneratorFunction")),e.prototype=Object.create(S),e},n.awrap=function(e){return{__await:e}},E(x.prototype),h(x.prototype,c,(function(){return this})),n.AsyncIterator=x,n.async=function(e,t,r,i,o){void 0===o&&(o=Promise);var a=new x(f(e,t,r,i),o);return n.isGeneratorFunction(t)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},E(S),h(S,l,"Generator"),h(S,u,(function(){return this})),h(S,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},n.values=M,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(L),!e)for(var t in this)"t"===t.charAt(0)&&o.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=r)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(n,i){return s.type="throw",s.arg=e,t.next=n,i&&(t.method="next",t.arg=r),!!i}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var u=o.call(a,"catchLoc"),c=o.call(a,"finallyLoc");if(u&&c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),L(r),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;L(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:M(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=r),g}},n}function r(e,t,r,n,i,o,a){try{var s=e[o](a),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,i)}function n(){return e=t().mark((function e(r){var n,u;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(n="",u=0;u<r.length;u++)n+=String.fromCharCode(r[u]);return e.abrupt("return",i(a(s(o(n),8*n.length))));case 3:case"end":return e.stop()}}),e)})),n=function(){var t=this,n=arguments;return new Promise((function(i,o){var a=e.apply(t,n);function s(e){r(a,i,o,s,u,"next",e)}function u(e){r(a,i,o,s,u,"throw",e)}s(void 0)}))},n.apply(this,arguments);var e}function i(e){for(var t=new Uint8Array(e.length),r=0;r<e.length;r++)t[r]=e.charCodeAt(r);return t}function o(e){for(var t=Array(e.length>>2),r=0;r<t.length;r++)t[r]=0;for(var n=0;n<8*e.length;n+=8)t[n>>5]|=(255&e.charCodeAt(n/8))<<n%32;return t}function a(e){for(var t="",r=0;r<32*e.length;r+=8)t+=String.fromCharCode(e[r>>5]>>>r%32&255);return t}function s(e,t){e[t>>5]|=128<<t%32,e[14+(t+64>>>9<<4)]=t;for(var r=1732584193,n=-271733879,i=-1732584194,o=271733878,a=0;a<e.length;a+=16){var s=r,u=n,_=i,p=o;n=f(n=f(n=f(n=f(n=h(n=h(n=h(n=h(n=l(n=l(n=l(n=l(n=c(n=c(n=c(n=c(n,i=c(i,o=c(o,r=c(r,n,i,o,e[a+0],7,-680876936),n,i,e[a+1],12,-389564586),r,n,e[a+2],17,606105819),o,r,e[a+3],22,-1044525330),i=c(i,o=c(o,r=c(r,n,i,o,e[a+4],7,-176418897),n,i,e[a+5],12,1200080426),r,n,e[a+6],17,-1473231341),o,r,e[a+7],22,-45705983),i=c(i,o=c(o,r=c(r,n,i,o,e[a+8],7,1770035416),n,i,e[a+9],12,-1958414417),r,n,e[a+10],17,-42063),o,r,e[a+11],22,-1990404162),i=c(i,o=c(o,r=c(r,n,i,o,e[a+12],7,1804603682),n,i,e[a+13],12,-40341101),r,n,e[a+14],17,-1502002290),o,r,e[a+15],22,1236535329),i=l(i,o=l(o,r=l(r,n,i,o,e[a+1],5,-165796510),n,i,e[a+6],9,-1069501632),r,n,e[a+11],14,643717713),o,r,e[a+0],20,-373897302),i=l(i,o=l(o,r=l(r,n,i,o,e[a+5],5,-701558691),n,i,e[a+10],9,38016083),r,n,e[a+15],14,-660478335),o,r,e[a+4],20,-405537848),i=l(i,o=l(o,r=l(r,n,i,o,e[a+9],5,568446438),n,i,e[a+14],9,-1019803690),r,n,e[a+3],14,-187363961),o,r,e[a+8],20,1163531501),i=l(i,o=l(o,r=l(r,n,i,o,e[a+13],5,-1444681467),n,i,e[a+2],9,-51403784),r,n,e[a+7],14,1735328473),o,r,e[a+12],20,-1926607734),i=h(i,o=h(o,r=h(r,n,i,o,e[a+5],4,-378558),n,i,e[a+8],11,-2022574463),r,n,e[a+11],16,1839030562),o,r,e[a+14],23,-35309556),i=h(i,o=h(o,r=h(r,n,i,o,e[a+1],4,-1530992060),n,i,e[a+4],11,1272893353),r,n,e[a+7],16,-155497632),o,r,e[a+10],23,-1094730640),i=h(i,o=h(o,r=h(r,n,i,o,e[a+13],4,681279174),n,i,e[a+0],11,-358537222),r,n,e[a+3],16,-722521979),o,r,e[a+6],23,76029189),i=h(i,o=h(o,r=h(r,n,i,o,e[a+9],4,-640364487),n,i,e[a+12],11,-421815835),r,n,e[a+15],16,530742520),o,r,e[a+2],23,-995338651),i=f(i,o=f(o,r=f(r,n,i,o,e[a+0],6,-198630844),n,i,e[a+7],10,1126891415),r,n,e[a+14],15,-1416354905),o,r,e[a+5],21,-57434055),i=f(i,o=f(o,r=f(r,n,i,o,e[a+12],6,1700485571),n,i,e[a+3],10,-1894986606),r,n,e[a+10],15,-1051523),o,r,e[a+1],21,-2054922799),i=f(i,o=f(o,r=f(r,n,i,o,e[a+8],6,1873313359),n,i,e[a+15],10,-30611744),r,n,e[a+6],15,-1560198380),o,r,e[a+13],21,1309151649),i=f(i,o=f(o,r=f(r,n,i,o,e[a+4],6,-145523070),n,i,e[a+11],10,-1120210379),r,n,e[a+2],15,718787259),o,r,e[a+9],21,-343485551),r=d(r,s),n=d(n,u),i=d(i,_),o=d(o,p)}return Array(r,n,i,o)}function u(e,t,r,n,i,o){return d(function(e,t){return e<<t|e>>>32-t}(d(d(t,e),d(n,o)),i),r)}function c(e,t,r,n,i,o,a){return u(t&r|~t&n,e,t,i,o,a)}function l(e,t,r,n,i,o,a){return u(t&n|r&~n,e,t,i,o,a)}function h(e,t,r,n,i,o,a){return u(t^r^n,e,t,i,o,a)}function f(e,t,r,n,i,o,a){return u(r^(t|~n),e,t,i,o,a)}function d(e,t){var r=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(r>>16)<<16|65535&r}return ot=1,Object.defineProperty(ut,"__esModule",{value:!0}),ut.MD5=function(e){return n.apply(this,arguments)},ut}function lt(){return at||(at=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var t=Ve(),r=Ze(),n=tt(),i=it(),o=ct();function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function s(e,t,r){return t&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,u(n.key),n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function u(e){var t=function(e,t){if("object"!=a(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==a(t)?t:t+""}var c=function(){return s((function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this._algorithms={"AES-ECB":t.AESECBCipher,"AES-EAX":t.AESEAXCipher,"DES-ECB":r.DESECBCipher,"DES-CBC":r.DESCBCCipher,"RSA-PKCS1-v1_5":n.RSACipher,DH:i.DHCipher,MD5:o.MD5}}),[{key:"encrypt",value:function(e,t,r){if(t.algorithm.name!==e.name)throw new Error("algorithm does not match");if("function"!=typeof t.encrypt)throw new Error("key does not support encryption");return t.encrypt(e,r)}},{key:"decrypt",value:function(e,t,r){if(t.algorithm.name!==e.name)throw new Error("algorithm does not match");if("function"!=typeof t.decrypt)throw new Error("key does not support encryption");return t.decrypt(e,r)}},{key:"importKey",value:function(e,t,r,n,i){if("raw"!==e)throw new Error("key format is not supported");var o=this._algorithms[r.name];if(typeof o>"u"||"function"!=typeof o.importKey)throw new Error("algorithm is not supported");return o.importKey(t,r,n,i)}},{key:"generateKey",value:function(e,t,r){var n=this._algorithms[e.name];if(typeof n>"u"||"function"!=typeof n.generateKey)throw new Error("algorithm is not supported");return n.generateKey(e,t,r)}},{key:"exportKey",value:function(e,t){if("raw"!==e)throw new Error("key format is not supported");if("function"!=typeof t.exportKey)throw new Error("key does not support exportKey");return t.exportKey()}},{key:"digest",value:function(e,t){var r=this._algorithms[e];if("function"!=typeof r)throw new Error("algorithm is not supported");return r(t)}},{key:"deriveBits",value:function(e,t,r){if(t.algorithm.name!==e.name)throw new Error("algorithm does not match");if("function"!=typeof t.deriveBits)throw new Error("key does not support deriveBits");return t.deriveBits(e,r)}}])}();e.default=new c}(ze)),ze}function ht(){return st||(st=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var t=p(),r=i(E()),n=i(lt());function i(e){return e&&e.__esModule?e:{default:e}}function o(e,t,r){return t=s(t),function(e,t){if(t&&("object"==c(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,a()?Reflect.construct(t,[],s(e).constructor):t.apply(e,r))}function a(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch{}return(a=function(){return!!e})()}function s(e){return(s=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function u(e,t){return(u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function c(e){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function l(){l=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function h(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{h({},"")}catch{h=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var o=t&&t.prototype instanceof m?t:m,a=Object.create(o.prototype),s=new P(n||[]);return i(a,"_invoke",{value:C(e,r,s)}),a}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(n){return{type:"throw",arg:n}}}t.wrap=f;var _="suspendedStart",p="suspendedYield",y="executing",v="completed",g={};function m(){}function b(){}function w(){}var k={};h(k,a,(function(){return this}));var X=Object.getPrototypeOf,K=X&&X(X(M([])));K&&K!==r&&n.call(K,a)&&(k=K);var S=w.prototype=m.prototype=Object.create(k);function E(e){["next","throw","return"].forEach((function(t){h(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function r(i,o,a,s){var u=d(e[i],e,o);if("throw"!==u.type){var l=u.arg,h=l.value;return h&&"object"==c(h)&&n.call(h,"__await")?t.resolve(h.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(h).then((function(e){l.value=e,a(l)}),(function(e){return r("throw",e,a,s)}))}s(u.arg)}var o;i(this,"_invoke",{value:function(e,n){function i(){return new t((function(t,i){r(e,n,t,i)}))}return o=o?o.then(i,i):i()}})}function C(t,r,n){var i=_;return function(o,a){if(i===y)throw Error("Generator is already running");if(i===v){if("throw"===o)throw a;return{value:e,done:!0}}for(n.method=o,n.arg=a;;){var s=n.delegate;if(s){var u=A(s,n);if(u){if(u===g)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===_)throw i=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=y;var c=d(t,r,n);if("normal"===c.type){if(i=n.done?v:p,c.arg===g)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(i=v,n.method="throw",n.arg=c.arg)}}}function A(t,r){var n=r.method,i=t.iterator[n];if(i===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,A(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var o=d(i,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,g;var a=o.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,g):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function F(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function L(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(F,this),this.reset(!0)}function M(t){if(t||""===t){var r=t[a];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,o=function r(){for(;++i<t.length;)if(n.call(t,i))return r.value=t[i],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(c(t)+" is not iterable")}return b.prototype=w,i(S,"constructor",{value:w,configurable:!0}),i(w,"constructor",{value:b,configurable:!0}),b.displayName=h(w,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,h(e,u,"GeneratorFunction")),e.prototype=Object.create(S),e},t.awrap=function(e){return{__await:e}},E(x.prototype),h(x.prototype,s,(function(){return this})),t.AsyncIterator=x,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var a=new x(f(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},E(S),h(S,u,"Generator"),h(S,a,(function(){return this})),h(S,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=M,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(L),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function i(n,i){return s.type="throw",s.arg=t,r.next=n,i&&(r.method="next",r.arg=e),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(u&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),L(r),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;L(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:M(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),g}},t}function h(e,t,r,n,i,o,a){try{var s=e[o](a),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,i)}function f(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var o=e.apply(t,r);function a(e){h(o,n,i,a,s,"next",e)}function s(e){h(o,n,i,a,s,"throw",e)}a(void 0)}))}}function d(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _(e,t,r){return t&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,y(n.key),n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function y(e){var t=function(e,t){if("object"!=c(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==c(t)?t:t+""}var v=function(){return _((function e(){d(this,e),this._cipher=null,this._counter=new Uint8Array(16)}),[{key:"setKey",value:(e=f(l().mark((function e(t){return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,n.default.importKey("raw",t,{name:"AES-EAX"},!1,["encrypt, decrypt"]);case 2:this._cipher=e.sent;case 3:case"end":return e.stop()}}),e,this)}))),function(t){return e.apply(this,arguments)})},{key:"makeMessage",value:function(){var e=f(l().mark((function e(t){var r,i,o,a;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=new Uint8Array([(65280&t.length)>>>8,255&t.length]),e.next=3,n.default.encrypt({name:"AES-EAX",iv:this._counter,additionalData:r},this._cipher,t);case 3:for(i=e.sent,o=0;o<16&&255==this._counter[o]++;o++);return(a=new Uint8Array(t.length+2+16)).set(r),a.set(i,2),e.abrupt("return",a);case 9:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"receiveMessage",value:function(){var e=f(l().mark((function e(t,r){var i,o,a;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=new Uint8Array([(65280&t)>>>8,255&t]),e.next=3,n.default.decrypt({name:"AES-EAX",iv:this._counter,additionalData:i},this._cipher,r);case 3:for(o=e.sent,a=0;a<16&&255==this._counter[a]++;a++);return e.abrupt("return",o);case 6:case"end":return e.stop()}}),e,this)})));return function(t,r){return e.apply(this,arguments)}}()}]);var e}();e.default=function(e){function r(e,t){var n;return d(this,r),(n=o(this,r))._hasStarted=!1,n._checkSock=null,n._checkCredentials=null,n._approveServerResolve=null,n._sockReject=null,n._credentialsReject=null,n._approveServerReject=null,n._sock=e,n._getCredentials=t,n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&u(e,t)}(r,e),_(r,[{key:"_waitSockAsync",value:function(e){var t=this;return new Promise((function(r,n){var i=function(){return!t._sock.rQwait("RA2",e)};i()?r():(t._checkSock=function(){i()&&(r(),t._checkSock=null,t._sockReject=null)},t._sockReject=n)}))}},{key:"_waitApproveKeyAsync",value:function(){var e=this;return new Promise((function(t,r){e._approveServerResolve=t,e._approveServerReject=r}))}},{key:"_waitCredentialsAsync",value:function(e){var t=this,r=function(){return 1===e&&void 0!==t._getCredentials().username&&void 0!==t._getCredentials().password||2===e&&void 0!==t._getCredentials().password};return new Promise((function(e,n){r()?e():(t._checkCredentials=function(){r()&&(e(),t._checkCredentials=null,t._credentialsReject=null)},t._credentialsReject=n)}))}},{key:"checkInternalEvents",value:function(){null!==this._checkSock&&this._checkSock(),null!==this._checkCredentials&&this._checkCredentials()}},{key:"approveServer",value:function(){null!==this._approveServerResolve&&(this._approveServerResolve(),this._approveServerResolve=null)}},{key:"disconnect",value:function(){null!==this._sockReject&&(this._sockReject(new Error("disconnect normally")),this._sockReject=null),null!==this._credentialsReject&&(this._credentialsReject(new Error("disconnect normally")),this._credentialsReject=null),null!==this._approveServerReject&&(this._approveServerReject(new Error("disconnect normally")),this._approveServerReject=null)}},{key:"negotiateRA2neAuthAsync",value:(i=f(l().mark((function e(){var r,i,o,a,s,u,c,h,f,d,_,p,y,g,m,b,w,k,X,K,S,E,x,C,A,F,L,P,M,T,Q,R,O,B,D;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this._hasStarted=!0,e.next=3,this._waitSockAsync(4);case 3:if(r=this._sock.rQpeekBytes(4),!((i=this._sock.rQshift32())<1024)){e.next=9;break}throw new Error("RA2: server public key is too short: "+i);case 9:if(!(i>8192)){e.next=11;break}throw new Error("RA2: server public key is too long: "+i);case 11:return o=Math.ceil(i/8),e.next=14,this._waitSockAsync(2*o);case 14:return a=this._sock.rQshiftBytes(o),s=this._sock.rQshiftBytes(o),e.next=18,n.default.importKey("raw",{n:a,e:s},{name:"RSA-PKCS1-v1_5"},!1,["encrypt"]);case 18:return u=e.sent,(c=new Uint8Array(4+2*o)).set(r),c.set(a,4),c.set(s,4+o),h=this._waitApproveKeyAsync(),this.dispatchEvent(new CustomEvent("serververification",{detail:{type:"RSA",publickey:c}})),e.next=27,h;case 27:return f=2048,d=Math.ceil(f/8),e.next=31,n.default.generateKey({name:"RSA-PKCS1-v1_5",modulusLength:f,publicExponent:new Uint8Array([1,0,1])},!0,["encrypt"]);case 31:return _=e.sent.privateKey,e.next=34,n.default.exportKey("raw",_);case 34:return p=e.sent,y=p.n,g=p.e,(m=new Uint8Array(4+2*d))[0]=(4278190080&f)>>>24,m[1]=(16711680&f)>>>16,m[2]=(65280&f)>>>8,m[3]=255&f,m.set(y,4),m.set(g,4+d),this._sock.sQpushBytes(m),this._sock.flush(),b=new Uint8Array(16),window.crypto.getRandomValues(b),e.next=50,n.default.encrypt({name:"RSA-PKCS1-v1_5"},u,b);case 50:return w=e.sent,(k=new Uint8Array(2+o))[0]=(65280&o)>>>8,k[1]=255&o,k.set(w,2),this._sock.sQpushBytes(k),this._sock.flush(),e.next=59,this._waitSockAsync(2);case 59:if(this._sock.rQshift16()===d){e.next=61;break}throw new Error("RA2: wrong encrypted message length");case 61:return X=this._sock.rQshiftBytes(d),e.next=64,n.default.decrypt({name:"RSA-PKCS1-v1_5"},_,X);case 64:if(null!==(K=e.sent)&&16===K.length){e.next=67;break}throw new Error("RA2: corrupted server encrypted random");case 67:return S=new Uint8Array(32),E=new Uint8Array(32),S.set(K),S.set(b,16),E.set(b),E.set(K,16),e.next=75,window.crypto.subtle.digest("SHA-1",S);case 75:return S=e.sent,S=new Uint8Array(S).slice(0,16),e.next=79,window.crypto.subtle.digest("SHA-1",E);case 79:return E=e.sent,E=new Uint8Array(E).slice(0,16),x=new v,e.next=84,x.setKey(S);case 84:return C=new v,e.next=87,C.setKey(E);case 87:return A=new Uint8Array(8+2*o+2*d),F=new Uint8Array(8+2*o+2*d),A.set(c),A.set(m,4+2*o),F.set(m),F.set(c,4+2*d),e.next=95,window.crypto.subtle.digest("SHA-1",A);case 95:return A=e.sent,e.next=98,window.crypto.subtle.digest("SHA-1",F);case 98:return F=e.sent,A=new Uint8Array(A),F=new Uint8Array(F),e.t0=this._sock,e.next=104,x.makeMessage(F);case 104:return e.t1=e.sent,e.t0.sQpushBytes.call(e.t0,e.t1),this._sock.flush(),e.next=109,this._waitSockAsync(38);case 109:if(20===this._sock.rQshift16()){e.next=111;break}throw new Error("RA2: wrong server hash");case 111:return e.next=113,C.receiveMessage(20,this._sock.rQshiftBytes(36));case 113:if(null!==(L=e.sent)){e.next=116;break}throw new Error("RA2: failed to authenticate the message");case 116:P=0;case 117:if(!(P<20)){e.next=123;break}if(L[P]===A[P]){e.next=120;break}throw new Error("RA2: wrong server hash");case 120:P++,e.next=117;break;case 123:return e.next=125,this._waitSockAsync(19);case 125:if(1===this._sock.rQshift16()){e.next=127;break}throw new Error("RA2: wrong subtype");case 127:return e.next=129,C.receiveMessage(1,this._sock.rQshiftBytes(17));case 129:if(null!==(M=e.sent)){e.next=132;break}throw new Error("RA2: failed to authenticate the message");case 132:if(M=M[0],T=this._waitCredentialsAsync(M),1!==M){e.next=138;break}(void 0===this._getCredentials().username||void 0===this._getCredentials().password)&&this.dispatchEvent(new CustomEvent("credentialsrequired",{detail:{types:["username","password"]}})),e.next=143;break;case 138:if(2!==M){e.next=142;break}void 0===this._getCredentials().password&&this.dispatchEvent(new CustomEvent("credentialsrequired",{detail:{types:["password"]}})),e.next=143;break;case 142:throw new Error("RA2: wrong subtype");case 143:return e.next=145,T;case 145:for(Q=1===M?(0,t.encodeUTF8)(this._getCredentials().username).slice(0,255):"",R=(0,t.encodeUTF8)(this._getCredentials().password).slice(0,255),(O=new Uint8Array(Q.length+R.length+2))[0]=Q.length,O[Q.length+1]=R.length,B=0;B<Q.length;B++)O[B+1]=Q.charCodeAt(B);for(D=0;D<R.length;D++)O[Q.length+2+D]=R.charCodeAt(D);return e.t2=this._sock,e.next=155,x.makeMessage(O);case 155:e.t3=e.sent,e.t2.sQpushBytes.call(e.t2,e.t3),this._sock.flush();case 158:case"end":return e.stop()}}),e,this)}))),function(){return i.apply(this,arguments)})},{key:"hasStarted",get:function(){return this._hasStarted},set:function(e){this._hasStarted=e}}]);var i}(r.default)}(Ne)),Ne}var ft,dt={};function _t(){return ft||(ft=1,function(e){function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(e,t,r){return t&&function(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,n(i.key),i)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function n(e){var r=function(e,r){if("object"!=t(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,r);if("object"!=t(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==t(r)?r:r+""}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,e.default=function(){return r((function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this._lines=0}),[{key:"decodeRect",value:function(e,t,r,n,i,o,a){if(0===r||0===n)return!0;0===this._lines&&(this._lines=n);for(var s=r*(8==a?1:4);this._lines>0;){if(i.rQwait("RAW",s))return!1;var u=t+(n-this._lines),c=i.rQshiftBytes(s,!1);if(8==a){for(var l=new Uint8Array(4*r),h=0;h<r;h++)l[4*h+0]=255*(3&c[h])/3,l[4*h+1]=255*(c[h]>>2&3)/3,l[4*h+2]=255*(c[h]>>4&3)/3,l[4*h+3]=255;c=l}for(var f=0;f<r;f++)c[4*f+3]=255;o.blitImage(e,u,r,1,c,0),this._lines--}return!0}}])}()}(dt)),dt}var pt,yt={};function vt(){return pt||(pt=1,function(e){function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(e,t,r){return t&&function(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,n(i.key),i)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function n(e){var r=function(e,r){if("object"!=t(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,r);if("object"!=t(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==t(r)?r:r+""}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,e.default=function(){return r((function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}),[{key:"decodeRect",value:function(e,t,r,n,i,o,a){if(i.rQwait("COPYRECT",4))return!1;var s=i.rQshift16(),u=i.rQshift16();return 0===r||0===n||o.copyImage(s,u,e,t,r,n),!0}}])}()}(yt)),yt}var gt,mt={};function bt(){return gt||(gt=1,function(e){function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(e,t,r){return t&&function(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,n(i.key),i)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function n(e){var r=function(e,r){if("object"!=t(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,r);if("object"!=t(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==t(r)?r:r+""}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,e.default=function(){return r((function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this._subrects=0}),[{key:"decodeRect",value:function(e,t,r,n,i,o,a){if(0===this._subrects){if(i.rQwait("RRE",8))return!1;this._subrects=i.rQshift32();var s=i.rQshiftBytes(4);o.fillRect(e,t,r,n,s)}for(;this._subrects>0;){if(i.rQwait("RRE",12))return!1;var u=i.rQshiftBytes(4),c=i.rQshift16(),l=i.rQshift16(),h=i.rQshift16(),f=i.rQshift16();o.fillRect(e+c,t+l,h,f,u),this._subrects--}return!0}}])}()}(mt)),mt}var wt,kt={};function Xt(){return wt||(wt=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var t=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=n(e)&&"function"!=typeof e)return{default:e};var i=r(t);if(i&&i.has(e))return i.get(e);var o={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&{}.hasOwnProperty.call(e,s)){var u=a?Object.getOwnPropertyDescriptor(e,s):null;u&&(u.get||u.set)?Object.defineProperty(o,s,u):o[s]=e[s]}return o.default=e,i&&i.set(e,o),o}(f());function r(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(r=function(e){return e?n:t})(e)}function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t,r){return t&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,o(n.key),n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function o(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t);if("object"!=n(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==n(t)?t:t+""}e.default=function(){return i((function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this._tiles=0,this._lastsubencoding=0,this._tileBuffer=new Uint8Array(1024)}),[{key:"decodeRect",value:function(e,r,n,i,o,a,s){for(0===this._tiles&&(this._tilesX=Math.ceil(n/16),this._tilesY=Math.ceil(i/16),this._totalTiles=this._tilesX*this._tilesY,this._tiles=this._totalTiles);this._tiles>0;){var u=1;if(o.rQwait("HEXTILE",u))return!1;var c=o.rQpeek8();if(c>30)throw new Error("Illegal hextile subencoding (subencoding: "+c+")");var l=this._totalTiles-this._tiles,h=e+16*(l%this._tilesX),f=r+16*Math.floor(l/this._tilesX),d=Math.min(16,e+n-h),_=Math.min(16,r+i-f);if(1&c)u+=d*_*4;else if(2&c&&(u+=4),4&c&&(u+=4),8&c){if(u++,o.rQwait("HEXTILE",u))return!1;var p=o.rQpeekBytes(u).at(-1);u+=16&c?6*p:2*p}if(o.rQwait("HEXTILE",u))return!1;if(o.rQshift8(),0===c)1&this._lastsubencoding?t.Debug("     Ignoring blank after RAW"):a.fillRect(h,f,d,_,this._background);else if(1&c){for(var y=d*_,v=o.rQshiftBytes(4*y,!1),g=0;g<y;g++)v[4*g+3]=255;a.blitImage(h,f,d,_,v,0)}else{if(2&c&&(this._background=new Uint8Array(o.rQshiftBytes(4))),4&c&&(this._foreground=new Uint8Array(o.rQshiftBytes(4))),this._startTile(h,f,d,_,this._background),8&c)for(var m=o.rQshift8(),b=0;b<m;b++){var w=void 0;w=16&c?o.rQshiftBytes(4):this._foreground;var k=o.rQshift8(),X=k>>4,K=15&k,S=o.rQshift8(),E=1+(S>>4),x=1+(15&S);this._subTile(X,K,E,x,w)}this._finishTile(a)}this._lastsubencoding=c,this._tiles--}return!0}},{key:"_startTile",value:function(e,t,r,n,i){this._tileX=e,this._tileY=t,this._tileW=r,this._tileH=n;for(var o=i[0],a=i[1],s=i[2],u=this._tileBuffer,c=0;c<r*n*4;c+=4)u[c]=o,u[c+1]=a,u[c+2]=s,u[c+3]=255}},{key:"_subTile",value:function(e,t,r,n,i){for(var o=i[0],a=i[1],s=i[2],u=e+r,c=t+n,l=this._tileBuffer,h=this._tileW,f=t;f<c;f++)for(var d=e;d<u;d++){var _=4*(d+f*h);l[_]=o,l[_+1]=a,l[_+2]=s,l[_+3]=255}}},{key:"_finishTile",value:function(e){e.blitImage(this._tileX,this._tileY,this._tileW,this._tileH,this._tileBuffer,0)}}])}()}(kt)),kt}var Kt,St={};function Et(){return Kt||(Kt=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var t,r=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=o(e)&&"function"!=typeof e)return{default:e};var r=i(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&{}.hasOwnProperty.call(e,s)){var u=a?Object.getOwnPropertyDescriptor(e,s):null;u&&(u.get||u.set)?Object.defineProperty(n,s,u):n[s]=e[s]}return n.default=e,r&&r.set(e,n),n}(f()),n=(t=ee())&&t.__esModule?t:{default:t};function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function a(e,t,r){return t&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,s(n.key),n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function s(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==o(t)?t:t+""}e.default=function(){return a((function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this._ctl=null,this._filter=null,this._numColors=0,this._palette=new Uint8Array(1024),this._len=0,this._zlibs=[];for(var t=0;t<4;t++)this._zlibs[t]=new n.default}),[{key:"decodeRect",value:function(e,t,n,i,o,a,s){if(null===this._ctl){if(o.rQwait("TIGHT compression-control",1))return!1;this._ctl=o.rQshift8();for(var u=0;u<4;u++)this._ctl>>u&1&&(this._zlibs[u].reset(),r.Info("Reset zlib stream "+u));this._ctl=this._ctl>>4}var c;if(8===this._ctl)c=this._fillRect(e,t,n,i,o,a,s);else if(9===this._ctl)c=this._jpegRect(e,t,n,i,o,a,s);else if(10===this._ctl)c=this._pngRect(e,t,n,i,o,a,s);else{if(8&this._ctl)throw new Error("Illegal tight compression received (ctl: "+this._ctl+")");c=this._basicRect(this._ctl,e,t,n,i,o,a,s)}return c&&(this._ctl=null),c}},{key:"_fillRect",value:function(e,t,r,n,i,o,a){if(i.rQwait("TIGHT",3))return!1;var s=i.rQshiftBytes(3);return o.fillRect(e,t,r,n,s,!1),!0}},{key:"_jpegRect",value:function(e,t,r,n,i,o,a){var s=this._readData(i);return null!==s&&(o.imageRect(e,t,r,n,"image/jpeg",s),!0)}},{key:"_pngRect",value:function(e,t,r,n,i,o,a){throw new Error("PNG received in standard Tight rect")}},{key:"_basicRect",value:function(e,t,r,n,i,o,a,s){if(null===this._filter)if(4&e){if(o.rQwait("TIGHT",1))return!1;this._filter=o.rQshift8()}else this._filter=0;var u,c=3&e;switch(this._filter){case 0:u=this._copyFilter(c,t,r,n,i,o,a,s);break;case 1:u=this._paletteFilter(c,t,r,n,i,o,a,s);break;case 2:u=this._gradientFilter(c,t,r,n,i,o,a,s);break;default:throw new Error("Illegal tight filter received (ctl: "+this._filter+")")}return u&&(this._filter=null),u}},{key:"_copyFilter",value:function(e,t,r,n,i,o,a,s){var u,c=n*i*3;if(0===c)return!0;if(c<12){if(o.rQwait("TIGHT",c))return!1;u=o.rQshiftBytes(c)}else{if(null===(u=this._readData(o)))return!1;this._zlibs[e].setInput(u),u=this._zlibs[e].inflate(c),this._zlibs[e].setInput(null)}for(var l=new Uint8Array(n*i*4),h=0,f=0;h<n*i*4;h+=4,f+=3)l[h]=u[f],l[h+1]=u[f+1],l[h+2]=u[f+2],l[h+3]=255;return a.blitImage(t,r,n,i,l,0,!1),!0}},{key:"_paletteFilter",value:function(e,t,r,n,i,o,a,s){if(0===this._numColors){if(o.rQwait("TIGHT palette",1))return!1;var u=o.rQpeek8()+1,c=3*u;if(o.rQwait("TIGHT palette",1+c))return!1;this._numColors=u,o.rQskipBytes(1),o.rQshiftTo(this._palette,c)}var l,h=this._numColors<=2?1:8,f=Math.floor((n*h+7)/8)*i;if(0===f)return!0;if(f<12){if(o.rQwait("TIGHT",f))return!1;l=o.rQshiftBytes(f)}else{if(null===(l=this._readData(o)))return!1;this._zlibs[e].setInput(l),l=this._zlibs[e].inflate(f),this._zlibs[e].setInput(null)}return 2==this._numColors?this._monoRect(t,r,n,i,l,this._palette,a):this._paletteRect(t,r,n,i,l,this._palette,a),this._numColors=0,!0}},{key:"_monoRect",value:function(e,t,r,n,i,o,a){for(var s=this._getScratchBuffer(r*n*4),u=Math.floor((r+7)/8),c=Math.floor(r/8),l=0;l<n;l++){var h=void 0,f=void 0,d=void 0;for(d=0;d<c;d++)for(var _=7;_>=0;_--)h=4*(l*r+8*d+7-_),f=3*(i[l*u+d]>>_&1),s[h]=o[f],s[h+1]=o[f+1],s[h+2]=o[f+2],s[h+3]=255;for(var p=7;p>=8-r%8;p--)h=4*(l*r+8*d+7-p),f=3*(i[l*u+d]>>p&1),s[h]=o[f],s[h+1]=o[f+1],s[h+2]=o[f+2],s[h+3]=255}a.blitImage(e,t,r,n,s,0,!1)}},{key:"_paletteRect",value:function(e,t,r,n,i,o,a){for(var s=this._getScratchBuffer(r*n*4),u=r*n*4,c=0,l=0;c<u;c+=4,l++){var h=3*i[l];s[c]=o[h],s[c+1]=o[h+1],s[c+2]=o[h+2],s[c+3]=255}a.blitImage(e,t,r,n,s,0,!1)}},{key:"_gradientFilter",value:function(e,t,r,n,i,o,a,s){var u,c=n*i*3;if(0===c)return!0;if(c<12){if(o.rQwait("TIGHT",c))return!1;u=o.rQshiftBytes(c)}else{if(null===(u=this._readData(o)))return!1;this._zlibs[e].setInput(u),u=this._zlibs[e].inflate(c),this._zlibs[e].setInput(null)}for(var l=new Uint8Array(4*n*i),h=0,f=0,d=new Uint8Array(3),_=0;_<n;_++){for(var p=0;p<3;p++){var y=d[p],v=u[f++]+y;l[h++]=v,d[p]=v}l[h++]=255}for(var g=0,m=new Uint8Array(3),b=new Uint8Array(3),w=1;w<i;w++){d.fill(0),b.fill(0);for(var k=0;k<n;k++){for(var X=0;X<3;X++){m[X]=l[g++];var K=d[X]+m[X]-b[X];K<0?K=0:K>255&&(K=255);var S=u[f++]+K;l[h++]=S,b[X]=m[X],d[X]=S}l[h++]=255,g++}}return a.blitImage(t,r,n,i,l,0,!1),!0}},{key:"_readData",value:function(e){if(0===this._len){if(e.rQwait("TIGHT",3))return null;var t;t=e.rQshift8(),this._len=127&t,128&t&&(t=e.rQshift8(),this._len|=(127&t)<<7,128&t&&(t=e.rQshift8(),this._len|=t<<14))}if(e.rQwait("TIGHT",this._len))return null;var r=e.rQshiftBytes(this._len,!1);return this._len=0,r}},{key:"_getScratchBuffer",value:function(e){return(!this._scratchBuffer||this._scratchBuffer.length<e)&&(this._scratchBuffer=new Uint8Array(e)),this._scratchBuffer}}])}()}(St)),St}var xt,Ct={};function At(){return xt||(xt=1,function(e){function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r,n=(r=Et())&&r.__esModule?r:{default:r};function i(e,t,r){return t&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,o(n.key),n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function o(e){var r=function(e,r){if("object"!=t(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,r);if("object"!=t(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==t(r)?r:r+""}function a(e,r,n){return r=u(r),function(e,r){if(r&&("object"==t(r)||"function"==typeof r))return r;if(void 0!==r)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,s()?Reflect.construct(r,n||[],u(e).constructor):r.apply(e,n))}function s(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch{}return(s=function(){return!!e})()}function u(e){return(u=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function c(e,t){return(c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}e.default=function(e){function t(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),a(this,t,arguments)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&c(e,t)}(t,e),i(t,[{key:"_pngRect",value:function(e,t,r,n,i,o,a){var s=this._readData(i);return null!==s&&(o.imageRect(e,t,r,n,"image/png",s),!0)}},{key:"_basicRect",value:function(e,t,r,n,i,o,a,s){throw new Error("BasicCompression received in TightPNG rect")}}])}(n.default)}(Ct)),Ct}var Ft,Lt={};function Pt(){return Ft||(Ft=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var t,r=(t=ee())&&t.__esModule?t:{default:t};function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t,r){return t&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,o(n.key),n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function o(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t);if("object"!=n(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==n(t)?t:t+""}e.default=function(){return i((function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this._length=0,this._inflator=new r.default,this._pixelBuffer=new Uint8Array(16384),this._tileBuffer=new Uint8Array(16384)}),[{key:"decodeRect",value:function(e,t,r,n,i,o,a){if(0===this._length){if(i.rQwait("ZLib data length",4))return!1;this._length=i.rQshift32()}if(i.rQwait("Zlib data",this._length))return!1;var s=i.rQshiftBytes(this._length,!1);this._inflator.setInput(s);for(var u=t;u<t+n;u+=64)for(var c=Math.min(64,t+n-u),l=e;l<e+r;l+=64){var h=Math.min(64,e+r-l),f=h*c,d=this._inflator.inflate(1)[0];if(0===d){var _=this._readPixels(f);o.blitImage(l,u,h,c,_,0,!1)}else if(1===d){var p=this._readPixels(1);o.fillRect(l,u,h,c,[p[0],p[1],p[2]])}else if(d>=2&&d<=16){var y=this._decodePaletteTile(d,f,h,c);o.blitImage(l,u,h,c,y,0,!1)}else if(128===d){var v=this._decodeRLETile(f);o.blitImage(l,u,h,c,v,0,!1)}else{if(!(d>=130&&d<=255))throw new Error("Unknown subencoding: "+d);var g=this._decodeRLEPaletteTile(d-128,f);o.blitImage(l,u,h,c,g,0,!1)}}return this._length=0,!0}},{key:"_getBitsPerPixelInPalette",value:function(e){return e<=2?1:e<=4?2:e<=16?4:void 0}},{key:"_readPixels",value:function(e){for(var t=this._pixelBuffer,r=this._inflator.inflate(3*e),n=0,i=0;n<4*e;n+=4,i+=3)t[n]=r[i],t[n+1]=r[i+1],t[n+2]=r[i+2],t[n+3]=255;return t}},{key:"_decodePaletteTile",value:function(e,t,r,n){for(var i=this._tileBuffer,o=this._readPixels(e),a=this._getBitsPerPixelInPalette(e),s=(1<<a)-1,u=0,c=this._inflator.inflate(1)[0],l=0;l<n;l++){for(var h=8-a,f=0;f<r;f++){h<0&&(h=8-a,c=this._inflator.inflate(1)[0]);var d=c>>h&s;i[u]=o[4*d],i[u+1]=o[4*d+1],i[u+2]=o[4*d+2],i[u+3]=o[4*d+3],u+=4,h-=a}h<8-a&&l<n-1&&(c=this._inflator.inflate(1)[0])}return i}},{key:"_decodeRLETile",value:function(e){for(var t=this._tileBuffer,r=0;r<e;)for(var n=this._readPixels(1),i=this._readRLELength(),o=0;o<i;o++)t[4*r]=n[0],t[4*r+1]=n[1],t[4*r+2]=n[2],t[4*r+3]=n[3],r++;return t}},{key:"_decodeRLEPaletteTile",value:function(e,t){for(var r=this._tileBuffer,n=this._readPixels(e),i=0;i<t;){var o=this._inflator.inflate(1)[0],a=1;if(o>=128&&(o-=128,a=this._readRLELength()),o>e)throw new Error("Too big index in palette: "+o+", palette size: "+e);if(i+a>t)throw new Error("Too big rle length in palette mode: "+a+", allowed length is: "+(t-i));for(var s=0;s<a;s++)r[4*i]=n[4*o],r[4*i+1]=n[4*o+1],r[4*i+2]=n[4*o+2],r[4*i+3]=n[4*o+3],i++}return r}},{key:"_readRLELength",value:function(){var e=0,t=0;do{e+=t=this._inflator.inflate(1)[0]}while(255===t);return e+1}}])}()}(Lt)),Lt}var Mt,Tt,Qt={};function Rt(){return Mt||(Mt=1,function(e){function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(e){return function(e){if(Array.isArray(e))return o(e)}(e)||function(e){if(typeof Symbol<"u"&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||i(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(e,t){var r=typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=i(e))||t){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,u=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return s=e.done,e},e:function(e){u=!0,a=e},f:function(){try{s||null==r.return||r.return()}finally{if(u)throw a}}}}function i(e,t){if(e){if("string"==typeof e)return o(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?o(e,t):void 0}}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function a(e,t,r){return t&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,s(n.key),n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function s(e){var r=function(e,r){if("object"!=t(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,r);if("object"!=t(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==t(r)?r:r+""}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,e.default=function(){return a((function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this._cachedQuantTables=[],this._cachedHuffmanTables=[],this._segments=[]}),[{key:"decodeRect",value:function(e,t,i,o,a,s,u){for(;;){var c=this._readSegment(a);if(null===c)return!1;if(this._segments.push(c),217===c[1])break}var l,h=[],f=[],d=n(this._segments);try{for(d.s();!(l=d.n()).done;){var _=l.value,p=_[1];196===p?h.push(_):219===p&&f.push(_)}}catch(E){d.e(E)}finally{d.f()}var y,v,g=this._segments.findIndex((function(e){return 192==e[1]||194==e[1]}));if(-1==g)throw new Error("Illegal JPEG image without SOF");0===f.length&&(y=this._segments).splice.apply(y,[g+1,0].concat(r(this._cachedQuantTables)));0===h.length&&(v=this._segments).splice.apply(v,[g+1,0].concat(r(this._cachedHuffmanTables)));var m,b=0,w=n(this._segments);try{for(w.s();!(m=w.n()).done;){b+=m.value.length}}catch(E){w.e(E)}finally{w.f()}var k=new Uint8Array(b);b=0;var X,K=n(this._segments);try{for(K.s();!(X=K.n()).done;){var S=X.value;k.set(S,b),b+=S.length}}catch(E){K.e(E)}finally{K.f()}return s.imageRect(e,t,i,o,"image/jpeg",k),0!==h.length&&(this._cachedHuffmanTables=h),0!==f.length&&(this._cachedQuantTables=f),this._segments=[],!0}},{key:"_readSegment",value:function(e){if(e.rQwait("JPEG",2))return null;var t=e.rQshift8();if(255!=t)throw new Error("Illegal JPEG marker received (byte: "+t+")");var r=e.rQshift8();if(r>=208&&r<=217||1==r)return new Uint8Array([t,r]);if(e.rQwait("JPEG",2,2))return null;var n=e.rQshift16();if(n<2)throw new Error("Illegal JPEG length received (length: "+n+")");if(e.rQwait("JPEG",n-2,4))return null;var i=0;if(218===r)for(i+=2;;){if(e.rQwait("JPEG",n-2+i,4))return null;var o=e.rQpeekBytes(n-2+i,!1);if(255===o.at(-2)&&0!==o.at(-1)&&!(o.at(-1)>=208&&o.at(-1)<=215)){i-=2;break}i++}var a=new Uint8Array(2+n+i);return a[0]=t,a[1]=r,a[2]=n>>8,a[3]=n,a.set(e.rQshiftBytes(n-2+i,!1),4),a}}])}()}(Qt)),Qt}var Ot=(Tt||(Tt=1,function(e){function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=c(),n=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=t(e)&&"function"!=typeof e)return{default:e};var n=D(r);if(n&&n.has(e))return n.get(e);var i={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&{}.hasOwnProperty.call(e,a)){var s=o?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(i,a,s):i[a]=e[a]}return i.default=e,n&&n.set(e,i),i}(f()),i=p(),o=g(),a=(m||(m=1,Object.defineProperty(b,"__esModule",{value:!0}),b.clientToElement=function(e,t,r){var n=r.getBoundingClientRect(),i={x:0,y:0};return e<n.left?i.x=0:e>=n.right?i.x=n.width-1:i.x=e-n.left,t<n.top?i.y=0:t>=n.bottom?i.y=n.height-1:i.y=t-n.top,i}),b),s=X(),u=B(E()),l=B(P()),h=B(ee()),d=B(he()),_=B(Ce()),y=B(Le()),v=B(Te()),w=B(Oe()),k=B(ye()),K=B(function(){return Be||(Be=1,e=De,Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,e.default={Again:57349,AltLeft:56,AltRight:57400,ArrowDown:57424,ArrowLeft:57419,ArrowRight:57421,ArrowUp:57416,AudioVolumeDown:57390,AudioVolumeMute:57376,AudioVolumeUp:57392,Backquote:41,Backslash:43,Backspace:14,BracketLeft:26,BracketRight:27,BrowserBack:57450,BrowserFavorites:57446,BrowserForward:57449,BrowserHome:57394,BrowserRefresh:57447,BrowserSearch:57445,BrowserStop:57448,CapsLock:58,Comma:51,ContextMenu:57437,ControlLeft:29,ControlRight:57373,Convert:121,Copy:57464,Cut:57404,Delete:57427,Digit0:11,Digit1:2,Digit2:3,Digit3:4,Digit4:5,Digit5:6,Digit6:7,Digit7:8,Digit8:9,Digit9:10,Eject:57469,End:57423,Enter:28,Equal:13,Escape:1,F1:59,F10:68,F11:87,F12:88,F13:93,F14:94,F15:95,F16:85,F17:57347,F18:57463,F19:57348,F2:60,F20:90,F21:116,F22:57465,F23:109,F24:111,F3:61,F4:62,F5:63,F6:64,F7:65,F8:66,F9:67,Find:57409,Help:57461,Hiragana:119,Home:57415,Insert:57426,IntlBackslash:86,IntlRo:115,IntlYen:125,KanaMode:112,Katakana:120,KeyA:30,KeyB:48,KeyC:46,KeyD:32,KeyE:18,KeyF:33,KeyG:34,KeyH:35,KeyI:23,KeyJ:36,KeyK:37,KeyL:38,KeyM:50,KeyN:49,KeyO:24,KeyP:25,KeyQ:16,KeyR:19,KeyS:31,KeyT:20,KeyU:22,KeyV:47,KeyW:17,KeyX:45,KeyY:21,KeyZ:44,Lang1:114,Lang2:113,Lang3:120,Lang4:119,Lang5:118,LaunchApp1:57451,LaunchApp2:57377,LaunchMail:57452,MediaPlayPause:57378,MediaSelect:57453,MediaStop:57380,MediaTrackNext:57369,MediaTrackPrevious:57360,MetaLeft:57435,MetaRight:57436,Minus:12,NonConvert:123,NumLock:69,Numpad0:82,Numpad1:79,Numpad2:80,Numpad3:81,Numpad4:75,Numpad5:76,Numpad6:77,Numpad7:71,Numpad8:72,Numpad9:73,NumpadAdd:78,NumpadComma:126,NumpadDecimal:83,NumpadDivide:57397,NumpadEnter:57372,NumpadEqual:89,NumpadMultiply:55,NumpadParenLeft:57462,NumpadParenRight:57467,NumpadSubtract:74,Open:100,PageDown:57425,PageUp:57417,Paste:101,Pause:57414,Period:52,Power:57438,PrintScreen:84,Props:57350,Quote:40,ScrollLock:70,Semicolon:39,ShiftLeft:42,ShiftRight:54,Slash:53,Sleep:57439,Space:57,Suspend:57381,Tab:15,Undo:57351,WakeUp:57443}),De;var e}()),S=function(){if(je)return Ie;je=1,Object.defineProperty(Ie,"__esModule",{value:!0}),Ie.encodingName=function(t){switch(t){case e.encodingRaw:return"Raw";case e.encodingCopyRect:return"CopyRect";case e.encodingRRE:return"RRE";case e.encodingHextile:return"Hextile";case e.encodingTight:return"Tight";case e.encodingZRLE:return"ZRLE";case e.encodingTightPNG:return"TightPNG";case e.encodingJPEG:return"JPEG";default:return"[unknown encoding "+t+"]"}},Ie.encodings=void 0;var e=Ie.encodings={encodingRaw:0,encodingCopyRect:1,encodingRRE:2,encodingHextile:5,encodingTight:7,encodingZRLE:16,encodingTightPNG:-260,encodingJPEG:21,pseudoEncodingQualityLevel9:-23,pseudoEncodingQualityLevel0:-32,pseudoEncodingDesktopSize:-223,pseudoEncodingLastRect:-224,pseudoEncodingCursor:-239,pseudoEncodingQEMUExtendedKeyEvent:-258,pseudoEncodingQEMULedEvent:-261,pseudoEncodingDesktopName:-307,pseudoEncodingExtendedDesktopSize:-308,pseudoEncodingXvp:-309,pseudoEncodingFence:-312,pseudoEncodingContinuousUpdates:-313,pseudoEncodingCompressLevel9:-247,pseudoEncodingCompressLevel0:-256,pseudoEncodingVMwareCursor:1464686180,pseudoEncodingExtendedClipboard:3231835598};return Ie}(),x=B(ht()),C=B(lt()),A=B(_t()),F=B(vt()),L=B(bt()),M=B(Xt()),T=B(Et()),Q=B(At()),R=B(Pt()),O=B(Rt());function B(e){return e&&e.__esModule?e:{default:e}}function D(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(D=function(e){return e?r:t})(e)}function j(){j=function(){return r};var e,r={},n=Object.prototype,i=n.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},a="function"==typeof Symbol?Symbol:{},s=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function l(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch{l=function(e,t,r){return e[t]=r}}function h(e,t,r,n){var i=t&&t.prototype instanceof g?t:g,a=Object.create(i.prototype),s=new L(n||[]);return o(a,"_invoke",{value:x(e,r,s)}),a}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(n){return{type:"throw",arg:n}}}r.wrap=h;var d="suspendedStart",_="suspendedYield",p="executing",y="completed",v={};function g(){}function m(){}function b(){}var w={};l(w,s,(function(){return this}));var k=Object.getPrototypeOf,X=k&&k(k(P([])));X&&X!==n&&i.call(X,s)&&(w=X);var K=b.prototype=g.prototype=Object.create(w);function S(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,r){function n(o,a,s,u){var c=f(e[o],e,a);if("throw"!==c.type){var l=c.arg,h=l.value;return h&&"object"==t(h)&&i.call(h,"__await")?r.resolve(h.__await).then((function(e){n("next",e,s,u)}),(function(e){n("throw",e,s,u)})):r.resolve(h).then((function(e){l.value=e,s(l)}),(function(e){return n("throw",e,s,u)}))}u(c.arg)}var a;o(this,"_invoke",{value:function(e,t){function i(){return new r((function(r,i){n(e,t,r,i)}))}return a=a?a.then(i,i):i()}})}function x(t,r,n){var i=d;return function(o,a){if(i===p)throw Error("Generator is already running");if(i===y){if("throw"===o)throw a;return{value:e,done:!0}}for(n.method=o,n.arg=a;;){var s=n.delegate;if(s){var u=C(s,n);if(u){if(u===v)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===d)throw i=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=p;var c=f(t,r,n);if("normal"===c.type){if(i=n.done?y:_,c.arg===v)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(i=y,n.method="throw",n.arg=c.arg)}}}function C(t,r){var n=r.method,i=t.iterator[n];if(i===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,C(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var o=f(i,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,v;var a=o.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function A(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function F(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function L(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(A,this),this.reset(!0)}function P(r){if(r||""===r){var n=r[s];if(n)return n.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length)){var o=-1,a=function t(){for(;++o<r.length;)if(i.call(r,o))return t.value=r[o],t.done=!1,t;return t.value=e,t.done=!0,t};return a.next=a}}throw new TypeError(t(r)+" is not iterable")}return m.prototype=b,o(K,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:m,configurable:!0}),m.displayName=l(b,c,"GeneratorFunction"),r.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},r.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,l(e,c,"GeneratorFunction")),e.prototype=Object.create(K),e},r.awrap=function(e){return{__await:e}},S(E.prototype),l(E.prototype,u,(function(){return this})),r.AsyncIterator=E,r.async=function(e,t,n,i,o){void 0===o&&(o=Promise);var a=new E(h(e,t,n,i),o);return r.isGeneratorFunction(t)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},S(K),l(K,c,"Generator"),l(K,s,(function(){return this})),l(K,"toString",(function(){return"[object Generator]"})),r.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},r.values=P,L.prototype={constructor:L,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(F),!t)for(var r in this)"t"===r.charAt(0)&&i.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(n,i){return s.type="throw",s.arg=t,r.next=n,i&&(r.method="next",r.arg=e),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var u=i.call(a,"catchLoc"),c=i.call(a,"finallyLoc");if(u&&c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&i.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),F(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;F(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:P(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),v}},r}function I(e,t,r,n,i,o,a){try{var s=e[o](a),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,i)}function U(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,o,a,s=[],u=!0,c=!1;try{if(o=(r=r.call(e)).next,0!==t)for(;!(u=(n=o.call(r)).done)&&(s.push(n.value),s.length!==t);u=!0);}catch(l){c=!0,i=l}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw i}}return s}}(e,t)||z(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function N(e,t){var r=typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=z(e))||t){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==r.return||r.return()}finally{if(s)throw o}}}}function z(e,t){if(e){if("string"==typeof e)return H(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?H(e,t):void 0}}function H(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function V(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,W(n.key),n)}}function W(e){var r=function(e,r){if("object"!=t(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,r);if("object"!=t(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==t(r)?r:r+""}function G(e,r,n){return r=Y(r),function(e,r){if(r&&("object"==t(r)||"function"==typeof r))return r;if(void 0!==r)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,Z()?Reflect.construct(r,[],Y(e).constructor):r.apply(e,n))}function Z(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch{}return(Z=function(){return!!e})()}function Y(e){return(Y=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function q(e,t){return(q=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var J=50,$=1<<24,te=1<<25,re=1<<26,ne=1<<27,ie=1<<28,oe=e.default=function(e){function t(e,r,i){var o;if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),!e)throw new Error("Must specify target");if(!r)throw new Error("Must specify URL, WebSocket or RTCDataChannel");window.isSecureContext||n.Error("noVNC requires a secure context (TLS). Expect crashes!"),(o=G(this,t))._target=e,"string"==typeof r?o._url=r:(o._url=null,o._rawChannel=r),i=i||{},o._rfbCredentials=i.credentials||{},o._shared=!("shared"in i)||!!i.shared,o._repeaterID=i.repeaterID||"",o._wsProtocols=i.wsProtocols||[],o._rfbConnectionState="",o._rfbInitState="",o._rfbAuthScheme=-1,o._rfbCleanDisconnect=!0,o._rfbRSAAESAuthenticationState=null,o._rfbVersion=0,o._rfbMaxVersion=3.8,o._rfbTightVNC=!1,o._rfbVeNCryptState=0,o._rfbXvpVer=0,o._fbWidth=0,o._fbHeight=0,o._fbName="",o._capabilities={power:!1},o._supportsFence=!1,o._supportsContinuousUpdates=!1,o._enabledContinuousUpdates=!1,o._supportsSetDesktopSize=!1,o._screenID=0,o._screenFlags=0,o._qemuExtKeyEventSupported=!1,o._clipboardText=null,o._clipboardServerCapabilitiesActions={},o._clipboardServerCapabilitiesFormats={},o._sock=null,o._display=null,o._flushing=!1,o._keyboard=null,o._gestures=null,o._resizeObserver=null,o._disconnTimer=null,o._resizeTimeout=null,o._mouseMoveTimer=null,o._decoders={},o._FBU={rects:0,x:0,y:0,width:0,height:0,encoding:null},o._mousePos={},o._mouseButtonMask=0,o._mouseLastMoveTime=0,o._viewportDragging=!1,o._viewportDragPos={},o._viewportHasMoved=!1,o._accumulatedWheelDeltaX=0,o._accumulatedWheelDeltaY=0,o._gestureLastTapTime=null,o._gestureFirstDoubleTapEv=null,o._gestureLastMagnitudeX=0,o._gestureLastMagnitudeY=0,o._eventHandlers={focusCanvas:o._focusCanvas.bind(o),handleResize:o._handleResize.bind(o),handleMouse:o._handleMouse.bind(o),handleWheel:o._handleWheel.bind(o),handleGesture:o._handleGesture.bind(o),handleRSAAESCredentialsRequired:o._handleRSAAESCredentialsRequired.bind(o),handleRSAAESServerVerification:o._handleRSAAESServerVerification.bind(o)},n.Debug(">> RFB.constructor"),o._screen=document.createElement("div"),o._screen.style.display="flex",o._screen.style.width="100%",o._screen.style.height="100%",o._screen.style.overflow="auto",o._screen.style.background="rgb(40, 40, 40)",o._canvas=document.createElement("canvas"),o._canvas.style.margin="auto",o._canvas.style.outline="none",o._canvas.width=0,o._canvas.height=0,o._canvas.tabIndex=-1,o._screen.appendChild(o._canvas),o._cursor=new v.default,o._cursorImage=t.cursors.none,o._decoders[S.encodings.encodingRaw]=new A.default,o._decoders[S.encodings.encodingCopyRect]=new F.default,o._decoders[S.encodings.encodingRRE]=new L.default,o._decoders[S.encodings.encodingHextile]=new M.default,o._decoders[S.encodings.encodingTight]=new T.default,o._decoders[S.encodings.encodingTightPNG]=new Q.default,o._decoders[S.encodings.encodingZRLE]=new R.default,o._decoders[S.encodings.encodingJPEG]=new O.default;try{o._display=new l.default(o._canvas)}catch(a){throw n.Error("Display exception: "+a),a}return o._keyboard=new _.default(o._canvas),o._keyboard.onkeyevent=o._handleKeyEvent.bind(o),o._remoteCapsLock=null,o._remoteNumLock=null,o._gestures=new y.default,o._sock=new w.default,o._sock.on("open",o._socketOpen.bind(o)),o._sock.on("close",o._socketClose.bind(o)),o._sock.on("message",o._handleMessage.bind(o)),o._sock.on("error",o._socketError.bind(o)),o._expectedClientWidth=null,o._expectedClientHeight=null,o._resizeObserver=new ResizeObserver(o._eventHandlers.handleResize),o._updateConnectionState("connecting"),n.Debug("<< RFB.constructor"),o.dragViewport=!1,o.focusOnClick=!0,o._viewOnly=!1,o._clipViewport=!1,o._clippingViewport=!1,o._scaleViewport=!1,o._resizeSession=!1,o._showDotCursor=!1,void 0!==i.showDotCursor&&(n.Warn("Specifying showDotCursor as a RFB constructor argument is deprecated"),o._showDotCursor=i.showDotCursor),o._qualityLevel=6,o._compressionLevel=2,o}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&q(e,t)}(t,e),function(e,t,r){return t&&V(e.prototype,t),r&&V(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}(t,[{key:"viewOnly",get:function(){return this._viewOnly},set:function(e){this._viewOnly=e,("connecting"===this._rfbConnectionState||"connected"===this._rfbConnectionState)&&(e?this._keyboard.ungrab():this._keyboard.grab())}},{key:"capabilities",get:function(){return this._capabilities}},{key:"clippingViewport",get:function(){return this._clippingViewport}},{key:"_setClippingViewport",value:function(e){e!==this._clippingViewport&&(this._clippingViewport=e,this.dispatchEvent(new CustomEvent("clippingviewport",{detail:this._clippingViewport})))}},{key:"touchButton",get:function(){return 0},set:function(e){n.Warn("Using old API!")}},{key:"clipViewport",get:function(){return this._clipViewport},set:function(e){this._clipViewport=e,this._updateClip()}},{key:"scaleViewport",get:function(){return this._scaleViewport},set:function(e){this._scaleViewport=e,e&&this._clipViewport&&this._updateClip(),this._updateScale(),!e&&this._clipViewport&&this._updateClip()}},{key:"resizeSession",get:function(){return this._resizeSession},set:function(e){this._resizeSession=e,e&&this._requestRemoteResize()}},{key:"showDotCursor",get:function(){return this._showDotCursor},set:function(e){this._showDotCursor=e,this._refreshCursor()}},{key:"background",get:function(){return this._screen.style.background},set:function(e){this._screen.style.background=e}},{key:"qualityLevel",get:function(){return this._qualityLevel},set:function(e){!Number.isInteger(e)||e<0||e>9?n.Error("qualityLevel must be an integer between 0 and 9"):this._qualityLevel!==e&&(this._qualityLevel=e,"connected"===this._rfbConnectionState&&this._sendEncodings())}},{key:"compressionLevel",get:function(){return this._compressionLevel},set:function(e){!Number.isInteger(e)||e<0||e>9?n.Error("compressionLevel must be an integer between 0 and 9"):this._compressionLevel!==e&&(this._compressionLevel=e,"connected"===this._rfbConnectionState&&this._sendEncodings())}},{key:"disconnect",value:function(){this._updateConnectionState("disconnecting"),this._sock.off("error"),this._sock.off("message"),this._sock.off("open"),null!==this._rfbRSAAESAuthenticationState&&this._rfbRSAAESAuthenticationState.disconnect()}},{key:"approveServer",value:function(){null!==this._rfbRSAAESAuthenticationState&&this._rfbRSAAESAuthenticationState.approveServer()}},{key:"sendCredentials",value:function(e){this._rfbCredentials=e,this._resumeAuthentication()}},{key:"sendCtrlAltDel",value:function(){"connected"!==this._rfbConnectionState||this._viewOnly||(n.Info("Sending Ctrl-Alt-Del"),this.sendKey(k.default.XK_Control_L,"ControlLeft",!0),this.sendKey(k.default.XK_Alt_L,"AltLeft",!0),this.sendKey(k.default.XK_Delete,"Delete",!0),this.sendKey(k.default.XK_Delete,"Delete",!1),this.sendKey(k.default.XK_Alt_L,"AltLeft",!1),this.sendKey(k.default.XK_Control_L,"ControlLeft",!1))}},{key:"machineShutdown",value:function(){this._xvpOp(1,2)}},{key:"machineReboot",value:function(){this._xvpOp(1,3)}},{key:"machineReset",value:function(){this._xvpOp(1,4)}},{key:"sendKey",value:function(e,r,i){if("connected"===this._rfbConnectionState&&!this._viewOnly){if(void 0===i)return this.sendKey(e,r,!0),void this.sendKey(e,r,!1);var o=K.default[r];if(this._qemuExtKeyEventSupported&&o)e=e||0,n.Info("Sending key ("+(i?"down":"up")+"): keysym "+e+", scancode "+o),t.messages.QEMUExtendedKeyEvent(this._sock,e,i,o);else{if(!e)return;n.Info("Sending keysym ("+(i?"down":"up")+"): "+e),t.messages.keyEvent(this._sock,e,i?1:0)}}}},{key:"focus",value:function(e){this._canvas.focus(e)}},{key:"blur",value:function(){this._canvas.blur()}},{key:"clipboardPasteFrom",value:function(e){if("connected"===this._rfbConnectionState&&!this._viewOnly)if(this._clipboardServerCapabilitiesFormats[1]&&this._clipboardServerCapabilitiesActions[ne])this._clipboardText=e,t.messages.extendedClipboardNotify(this._sock,[1]);else{var r,n,i;r=0;var o,a=N(e);try{for(a.s();!(o=a.n()).done;)o.value,r++}catch(l){a.e(l)}finally{a.f()}i=new Uint8Array(r),n=0;var s,u=N(e);try{for(u.s();!(s=u.n()).done;){var c=s.value.codePointAt(0);c>255&&(c=63),i[n++]=c}}catch(l){u.e(l)}finally{u.f()}t.messages.clientCutText(this._sock,i)}}},{key:"getImageData",value:function(){return this._display.getImageData()}},{key:"toDataURL",value:function(e,t){return this._display.toDataURL(e,t)}},{key:"toBlob",value:function(e,t,r){return this._display.toBlob(e,t,r)}},{key:"_connect",value:function(){if(n.Debug(">> RFB.connect"),this._url)n.Info("connecting to ".concat(this._url)),this._sock.open(this._url,this._wsProtocols);else{if(n.Info("attaching ".concat(this._rawChannel," to Websock")),this._sock.attach(this._rawChannel),"closed"===this._sock.readyState)throw Error("Cannot use already closed WebSocket/RTCDataChannel");"open"===this._sock.readyState&&this._socketOpen()}this._target.appendChild(this._screen),this._gestures.attach(this._canvas),this._cursor.attach(this._canvas),this._refreshCursor(),this._resizeObserver.observe(this._screen),this._canvas.addEventListener("mousedown",this._eventHandlers.focusCanvas),this._canvas.addEventListener("touchstart",this._eventHandlers.focusCanvas),this._canvas.addEventListener("mousedown",this._eventHandlers.handleMouse),this._canvas.addEventListener("mouseup",this._eventHandlers.handleMouse),this._canvas.addEventListener("mousemove",this._eventHandlers.handleMouse),this._canvas.addEventListener("click",this._eventHandlers.handleMouse),this._canvas.addEventListener("contextmenu",this._eventHandlers.handleMouse),this._canvas.addEventListener("wheel",this._eventHandlers.handleWheel),this._canvas.addEventListener("gesturestart",this._eventHandlers.handleGesture),this._canvas.addEventListener("gesturemove",this._eventHandlers.handleGesture),this._canvas.addEventListener("gestureend",this._eventHandlers.handleGesture),n.Debug("<< RFB.connect")}},{key:"_disconnect",value:function(){n.Debug(">> RFB.disconnect"),this._cursor.detach(),this._canvas.removeEventListener("gesturestart",this._eventHandlers.handleGesture),this._canvas.removeEventListener("gesturemove",this._eventHandlers.handleGesture),this._canvas.removeEventListener("gestureend",this._eventHandlers.handleGesture),this._canvas.removeEventListener("wheel",this._eventHandlers.handleWheel),this._canvas.removeEventListener("mousedown",this._eventHandlers.handleMouse),this._canvas.removeEventListener("mouseup",this._eventHandlers.handleMouse),this._canvas.removeEventListener("mousemove",this._eventHandlers.handleMouse),this._canvas.removeEventListener("click",this._eventHandlers.handleMouse),this._canvas.removeEventListener("contextmenu",this._eventHandlers.handleMouse),this._canvas.removeEventListener("mousedown",this._eventHandlers.focusCanvas),this._canvas.removeEventListener("touchstart",this._eventHandlers.focusCanvas),this._resizeObserver.disconnect(),this._keyboard.ungrab(),this._gestures.detach(),this._sock.close();try{this._target.removeChild(this._screen)}catch(e){if("NotFoundError"!==e.name)throw e}clearTimeout(this._resizeTimeout),clearTimeout(this._mouseMoveTimer),n.Debug("<< RFB.disconnect")}},{key:"_socketOpen",value:function(){"connecting"===this._rfbConnectionState&&""===this._rfbInitState?(this._rfbInitState="ProtocolVersion",n.Debug("Starting VNC handshake")):this._fail("Unexpected server connection while "+this._rfbConnectionState)}},{key:"_socketClose",value:function(e){n.Debug("WebSocket on-close event");var t="";switch(e.code&&(t="(code: "+e.code,e.reason&&(t+=", reason: "+e.reason),t+=")"),this._rfbConnectionState){case"connecting":this._fail("Connection closed "+t);break;case"connected":this._updateConnectionState("disconnecting"),this._updateConnectionState("disconnected");break;case"disconnecting":this._updateConnectionState("disconnected");break;case"disconnected":this._fail("Unexpected server disconnect when already disconnected "+t);break;default:this._fail("Unexpected server disconnect before connecting "+t)}this._sock.off("close"),this._rawChannel=null}},{key:"_socketError",value:function(e){n.Warn("WebSocket on-error event")}},{key:"_focusCanvas",value:function(e){this.focusOnClick&&this.focus({preventScroll:!0})}},{key:"_setDesktopName",value:function(e){this._fbName=e,this.dispatchEvent(new CustomEvent("desktopname",{detail:{name:this._fbName}}))}},{key:"_saveExpectedClientSize",value:function(){this._expectedClientWidth=this._screen.clientWidth,this._expectedClientHeight=this._screen.clientHeight}},{key:"_currentClientSize",value:function(){return[this._screen.clientWidth,this._screen.clientHeight]}},{key:"_clientHasExpectedSize",value:function(){var e=U(this._currentClientSize(),2),t=e[0],r=e[1];return t==this._expectedClientWidth&&r==this._expectedClientHeight}},{key:"_handleResize",value:function(){var e=this;this._clientHasExpectedSize()||(window.requestAnimationFrame((function(){e._updateClip(),e._updateScale()})),this._resizeSession&&(clearTimeout(this._resizeTimeout),this._resizeTimeout=setTimeout(this._requestRemoteResize.bind(this),500)))}},{key:"_updateClip",value:function(){var e=this._display.clipViewport,t=this._clipViewport;if(this._scaleViewport&&(t=!1),e!==t&&(this._display.clipViewport=t),t){var r=this._screenSize();this._display.viewportChangeSize(r.w,r.h),this._fixScrollbars(),this._setClippingViewport(r.w<this._display.width||r.h<this._display.height)}else this._setClippingViewport(!1);e!==t&&this._saveExpectedClientSize()}},{key:"_updateScale",value:function(){if(this._scaleViewport){var e=this._screenSize();this._display.autoscale(e.w,e.h)}else this._display.scale=1;this._fixScrollbars()}},{key:"_requestRemoteResize",value:function(){if(clearTimeout(this._resizeTimeout),this._resizeTimeout=null,this._resizeSession&&!this._viewOnly&&this._supportsSetDesktopSize){var e=this._screenSize();t.messages.setDesktopSize(this._sock,Math.floor(e.w),Math.floor(e.h),this._screenID,this._screenFlags),n.Debug("Requested new desktop size: "+e.w+"x"+e.h)}}},{key:"_screenSize",value:function(){var e=this._screen.getBoundingClientRect();return{w:e.width,h:e.height}}},{key:"_fixScrollbars",value:function(){var e=this._screen.style.overflow;this._screen.style.overflow="hidden",this._screen.getBoundingClientRect(),this._screen.style.overflow=e}},{key:"_updateConnectionState",value:function(e){var t=this,r=this._rfbConnectionState;if(e!==r)if("disconnected"!==r){switch(e){case"connected":if("connecting"!==r)return void n.Error("Bad transition to connected state, previous connection state: "+r);break;case"disconnected":if("disconnecting"!==r)return void n.Error("Bad transition to disconnected state, previous connection state: "+r);break;case"connecting":if(""!==r)return void n.Error("Bad transition to connecting state, previous connection state: "+r);break;case"disconnecting":if("connected"!==r&&"connecting"!==r)return void n.Error("Bad transition to disconnecting state, previous connection state: "+r);break;default:return void n.Error("Unknown connection state: "+e)}switch(this._rfbConnectionState=e,n.Debug("New state '"+e+"', was '"+r+"'."),this._disconnTimer&&"disconnecting"!==e&&(n.Debug("Clearing disconnect timer"),clearTimeout(this._disconnTimer),this._disconnTimer=null,this._sock.off("close")),e){case"connecting":this._connect();break;case"connected":this.dispatchEvent(new CustomEvent("connect",{detail:{}}));break;case"disconnecting":this._disconnect(),this._disconnTimer=setTimeout((function(){n.Error("Disconnection timed out."),t._updateConnectionState("disconnected")}),3e3);break;case"disconnected":this.dispatchEvent(new CustomEvent("disconnect",{detail:{clean:this._rfbCleanDisconnect}}))}}else n.Error("Tried changing state of a disconnected RFB object");else n.Debug("Already in state '"+e+"', ignoring")}},{key:"_fail",value:function(e){switch(this._rfbConnectionState){case"disconnecting":n.Error("Failed when disconnecting: "+e);break;case"connected":n.Error("Failed while connected: "+e);break;case"connecting":n.Error("Failed when connecting: "+e);break;default:n.Error("RFB failure: "+e)}return this._rfbCleanDisconnect=!1,this._updateConnectionState("disconnecting"),this._updateConnectionState("disconnected"),!1}},{key:"_setCapability",value:function(e,t){this._capabilities[e]=t,this.dispatchEvent(new CustomEvent("capabilities",{detail:{capabilities:this._capabilities}}))}},{key:"_handleMessage",value:function(){if(this._sock.rQwait("message",1))n.Warn("handleMessage called on an empty receive queue");else switch(this._rfbConnectionState){case"disconnected":n.Error("Got data while disconnected");break;case"connected":for(;!this._flushing&&this._normalMsg()&&!this._sock.rQwait("message",1););break;case"connecting":for(;"connecting"===this._rfbConnectionState&&this._initMsg(););break;default:n.Error("Got data while in an invalid state")}}},{key:"_handleKeyEvent",value:function(e,t,r,i,o){"CapsLock"==t&&r&&(this._remoteCapsLock=null),null!==this._remoteCapsLock&&null!==o&&this._remoteCapsLock!==o&&r&&(n.Debug("Fixing remote caps lock"),this.sendKey(k.default.XK_Caps_Lock,"CapsLock",!0),this.sendKey(k.default.XK_Caps_Lock,"CapsLock",!1),this._remoteCapsLock=null),"NumLock"==t&&r&&(this._remoteNumLock=null),null!==this._remoteNumLock&&null!==i&&this._remoteNumLock!==i&&r&&(n.Debug("Fixing remote num lock"),this.sendKey(k.default.XK_Num_Lock,"NumLock",!0),this.sendKey(k.default.XK_Num_Lock,"NumLock",!1),this._remoteNumLock=null),this.sendKey(e,t,r)}},{key:"_handleMouse",value:function(e){if(("click"!==e.type||e.target===this._canvas)&&(e.stopPropagation(),e.preventDefault(),"click"!==e.type&&"contextmenu"!==e.type)){var t=(0,a.clientToElement)(e.clientX,e.clientY,this._canvas);switch(e.type){case"mousedown":(0,s.setCapture)(this._canvas),this._handleMouseButton(t.x,t.y,!0,1<<e.button);break;case"mouseup":this._handleMouseButton(t.x,t.y,!1,1<<e.button);break;case"mousemove":this._handleMouseMove(t.x,t.y)}}}},{key:"_handleMouseButton",value:function(e,t,r,n){if(this.dragViewport){if(r&&!this._viewportDragging)return this._viewportDragging=!0,this._viewportDragPos={x:e,y:t},void(this._viewportHasMoved=!1);if(this._viewportDragging=!1,this._viewportHasMoved)return;this._sendMouse(e,t,n)}null!==this._mouseMoveTimer&&(clearTimeout(this._mouseMoveTimer),this._mouseMoveTimer=null,this._sendMouse(e,t,this._mouseButtonMask)),r?this._mouseButtonMask|=n:this._mouseButtonMask&=~n,this._sendMouse(e,t,this._mouseButtonMask)}},{key:"_handleMouseMove",value:function(e,t){var r=this;if(this._viewportDragging){var n=this._viewportDragPos.x-e,i=this._viewportDragPos.y-t;(this._viewportHasMoved||Math.abs(n)>o.dragThreshold||Math.abs(i)>o.dragThreshold)&&(this._viewportHasMoved=!0,this._viewportDragPos={x:e,y:t},this._display.viewportChangePos(n,i))}else if(this._mousePos={x:e,y:t},null==this._mouseMoveTimer){var a=Date.now()-this._mouseLastMoveTime;a>17?(this._sendMouse(e,t,this._mouseButtonMask),this._mouseLastMoveTime=Date.now()):this._mouseMoveTimer=setTimeout((function(){r._handleDelayedMouseMove()}),17-a)}}},{key:"_handleDelayedMouseMove",value:function(){this._mouseMoveTimer=null,this._sendMouse(this._mousePos.x,this._mousePos.y,this._mouseButtonMask),this._mouseLastMoveTime=Date.now()}},{key:"_sendMouse",value:function(e,r,n){"connected"===this._rfbConnectionState&&(this._viewOnly||t.messages.pointerEvent(this._sock,this._display.absX(e),this._display.absY(r),n))}},{key:"_handleWheel",value:function(e){if("connected"===this._rfbConnectionState&&!this._viewOnly){e.stopPropagation(),e.preventDefault();var t=(0,a.clientToElement)(e.clientX,e.clientY,this._canvas),r=e.deltaX,n=e.deltaY;0!==e.deltaMode&&(r*=19,n*=19),this._accumulatedWheelDeltaX+=r,this._accumulatedWheelDeltaY+=n,Math.abs(this._accumulatedWheelDeltaX)>=50&&(this._accumulatedWheelDeltaX<0?(this._handleMouseButton(t.x,t.y,!0,32),this._handleMouseButton(t.x,t.y,!1,32)):this._accumulatedWheelDeltaX>0&&(this._handleMouseButton(t.x,t.y,!0,64),this._handleMouseButton(t.x,t.y,!1,64)),this._accumulatedWheelDeltaX=0),Math.abs(this._accumulatedWheelDeltaY)>=50&&(this._accumulatedWheelDeltaY<0?(this._handleMouseButton(t.x,t.y,!0,8),this._handleMouseButton(t.x,t.y,!1,8)):this._accumulatedWheelDeltaY>0&&(this._handleMouseButton(t.x,t.y,!0,16),this._handleMouseButton(t.x,t.y,!1,16)),this._accumulatedWheelDeltaY=0)}}},{key:"_fakeMouseMove",value:function(e,t,r){this._handleMouseMove(t,r),this._cursor.move(e.detail.clientX,e.detail.clientY)}},{key:"_handleTapEvent",value:function(e,t){var r=(0,a.clientToElement)(e.detail.clientX,e.detail.clientY,this._canvas);if(null!==this._gestureLastTapTime&&Date.now()-this._gestureLastTapTime<1e3&&this._gestureFirstDoubleTapEv.detail.type===e.detail.type){var n=this._gestureFirstDoubleTapEv.detail.clientX-e.detail.clientX,i=this._gestureFirstDoubleTapEv.detail.clientY-e.detail.clientY;Math.hypot(n,i)<50?r=(0,a.clientToElement)(this._gestureFirstDoubleTapEv.detail.clientX,this._gestureFirstDoubleTapEv.detail.clientY,this._canvas):this._gestureFirstDoubleTapEv=e}else this._gestureFirstDoubleTapEv=e;this._gestureLastTapTime=Date.now(),this._fakeMouseMove(this._gestureFirstDoubleTapEv,r.x,r.y),this._handleMouseButton(r.x,r.y,!0,t),this._handleMouseButton(r.x,r.y,!1,t)}},{key:"_handleGesture",value:function(e){var t,r=(0,a.clientToElement)(e.detail.clientX,e.detail.clientY,this._canvas);switch(e.type){case"gesturestart":switch(e.detail.type){case"onetap":this._handleTapEvent(e,1);break;case"twotap":this._handleTapEvent(e,4);break;case"threetap":this._handleTapEvent(e,2);break;case"drag":this._fakeMouseMove(e,r.x,r.y),this._handleMouseButton(r.x,r.y,!0,1);break;case"longpress":this._fakeMouseMove(e,r.x,r.y),this._handleMouseButton(r.x,r.y,!0,4);break;case"twodrag":this._gestureLastMagnitudeX=e.detail.magnitudeX,this._gestureLastMagnitudeY=e.detail.magnitudeY,this._fakeMouseMove(e,r.x,r.y);break;case"pinch":this._gestureLastMagnitudeX=Math.hypot(e.detail.magnitudeX,e.detail.magnitudeY),this._fakeMouseMove(e,r.x,r.y)}break;case"gesturemove":switch(e.detail.type){case"onetap":case"twotap":case"threetap":break;case"drag":case"longpress":this._fakeMouseMove(e,r.x,r.y);break;case"twodrag":for(this._fakeMouseMove(e,r.x,r.y);e.detail.magnitudeY-this._gestureLastMagnitudeY>J;)this._handleMouseButton(r.x,r.y,!0,8),this._handleMouseButton(r.x,r.y,!1,8),this._gestureLastMagnitudeY+=J;for(;e.detail.magnitudeY-this._gestureLastMagnitudeY<-50;)this._handleMouseButton(r.x,r.y,!0,16),this._handleMouseButton(r.x,r.y,!1,16),this._gestureLastMagnitudeY-=J;for(;e.detail.magnitudeX-this._gestureLastMagnitudeX>J;)this._handleMouseButton(r.x,r.y,!0,32),this._handleMouseButton(r.x,r.y,!1,32),this._gestureLastMagnitudeX+=J;for(;e.detail.magnitudeX-this._gestureLastMagnitudeX<-50;)this._handleMouseButton(r.x,r.y,!0,64),this._handleMouseButton(r.x,r.y,!1,64),this._gestureLastMagnitudeX-=J;break;case"pinch":if(this._fakeMouseMove(e,r.x,r.y),t=Math.hypot(e.detail.magnitudeX,e.detail.magnitudeY),Math.abs(t-this._gestureLastMagnitudeX)>75){for(this._handleKeyEvent(k.default.XK_Control_L,"ControlLeft",!0);t-this._gestureLastMagnitudeX>75;)this._handleMouseButton(r.x,r.y,!0,8),this._handleMouseButton(r.x,r.y,!1,8),this._gestureLastMagnitudeX+=75;for(;t-this._gestureLastMagnitudeX<-75;)this._handleMouseButton(r.x,r.y,!0,16),this._handleMouseButton(r.x,r.y,!1,16),this._gestureLastMagnitudeX-=75}this._handleKeyEvent(k.default.XK_Control_L,"ControlLeft",!1)}break;case"gestureend":switch(e.detail.type){case"onetap":case"twotap":case"threetap":case"pinch":case"twodrag":break;case"drag":this._fakeMouseMove(e,r.x,r.y),this._handleMouseButton(r.x,r.y,!1,1);break;case"longpress":this._fakeMouseMove(e,r.x,r.y),this._handleMouseButton(r.x,r.y,!1,4)}}}},{key:"_negotiateProtocolVersion",value:function(){if(this._sock.rQwait("version",12))return!1;var e=this._sock.rQshiftStr(12).substr(4,7);n.Info("Server ProtocolVersion: "+e);var t=0;switch(e){case"000.000":t=1;break;case"003.003":case"003.006":this._rfbVersion=3.3;break;case"003.007":this._rfbVersion=3.7;break;case"003.008":case"003.889":case"004.000":case"004.001":case"005.000":this._rfbVersion=3.8;break;default:return this._fail("Invalid server version "+e)}if(t){for(var r="ID:"+this._repeaterID;r.length<250;)r+="\0";return this._sock.sQpushString(r),this._sock.flush(),!0}this._rfbVersion>this._rfbMaxVersion&&(this._rfbVersion=this._rfbMaxVersion);var i="00"+parseInt(this._rfbVersion,10)+".00"+10*this._rfbVersion%10;this._sock.sQpushString("RFB "+i+"\n"),this._sock.flush(),n.Debug("Sent ProtocolVersion: "+i),this._rfbInitState="Security"}},{key:"_isSupportedSecurityType",value:function(e){return[1,2,6,16,19,22,30,113,256].includes(e)}},{key:"_negotiateSecurity",value:function(){if(this._rfbVersion>=3.7){var e=this._sock.rQshift8();if(this._sock.rQwait("security type",e,1))return!1;if(0===e)return this._rfbInitState="SecurityReason",this._securityContext="no security types",this._securityStatus=1,!0;var t=this._sock.rQshiftBytes(e);n.Debug("Server security types: "+t),this._rfbAuthScheme=-1;var r,i=N(t);try{for(i.s();!(r=i.n()).done;){var o=r.value;if(this._isSupportedSecurityType(o)){this._rfbAuthScheme=o;break}}}catch(a){i.e(a)}finally{i.f()}if(-1===this._rfbAuthScheme)return this._fail("Unsupported security types (types: "+t+")");this._sock.sQpush8(this._rfbAuthScheme),this._sock.flush()}else{if(this._sock.rQwait("security scheme",4))return!1;if(this._rfbAuthScheme=this._sock.rQshift32(),0==this._rfbAuthScheme)return this._rfbInitState="SecurityReason",this._securityContext="authentication scheme",this._securityStatus=1,!0}return this._rfbInitState="Authentication",n.Debug("Authenticating using scheme: "+this._rfbAuthScheme),!0}},{key:"_handleSecurityReason",value:function(){if(this._sock.rQwait("reason length",4))return!1;var e=this._sock.rQshift32(),t="";if(e>0){if(this._sock.rQwait("reason",e,4))return!1;t=this._sock.rQshiftStr(e)}return""!==t?(this.dispatchEvent(new CustomEvent("securityfailure",{detail:{status:this._securityStatus,reason:t}})),this._fail("Security negotiation failed on "+this._securityContext+" (reason: "+t+")")):(this.dispatchEvent(new CustomEvent("securityfailure",{detail:{status:this._securityStatus}})),this._fail("Security negotiation failed on "+this._securityContext))}},{key:"_negotiateXvpAuth",value:function(){return void 0===this._rfbCredentials.username||void 0===this._rfbCredentials.password||void 0===this._rfbCredentials.target?(this.dispatchEvent(new CustomEvent("credentialsrequired",{detail:{types:["username","password","target"]}})),!1):(this._sock.sQpush8(this._rfbCredentials.username.length),this._sock.sQpush8(this._rfbCredentials.target.length),this._sock.sQpushString(this._rfbCredentials.username),this._sock.sQpushString(this._rfbCredentials.target),this._sock.flush(),this._rfbAuthScheme=2,this._negotiateAuthentication())}},{key:"_negotiateVeNCryptAuth",value:function(){if(0==this._rfbVeNCryptState){if(this._sock.rQwait("vencrypt version",2))return!1;var e=this._sock.rQshift8(),t=this._sock.rQshift8();if(0!=e||2!=t)return this._fail("Unsupported VeNCrypt version "+e+"."+t);this._sock.sQpush8(0),this._sock.sQpush8(2),this._sock.flush(),this._rfbVeNCryptState=1}if(1==this._rfbVeNCryptState){if(this._sock.rQwait("vencrypt ack",1))return!1;var r=this._sock.rQshift8();if(0!=r)return this._fail("VeNCrypt failure "+r);this._rfbVeNCryptState=2}if(2==this._rfbVeNCryptState){if(this._sock.rQwait("vencrypt subtypes length",1))return!1;var n=this._sock.rQshift8();if(n<1)return this._fail("VeNCrypt subtypes empty");this._rfbVeNCryptSubtypesLength=n,this._rfbVeNCryptState=3}if(3==this._rfbVeNCryptState){if(this._sock.rQwait("vencrypt subtypes",4*this._rfbVeNCryptSubtypesLength))return!1;for(var i=[],o=0;o<this._rfbVeNCryptSubtypesLength;o++)i.push(this._sock.rQshift32());this._rfbAuthScheme=-1;for(var a=0,s=i;a<s.length;a++){var u=s[a];if(19!==u&&this._isSupportedSecurityType(u)){this._rfbAuthScheme=u;break}}return-1===this._rfbAuthScheme?this._fail("Unsupported security types (types: "+i+")"):(this._sock.sQpush32(this._rfbAuthScheme),this._sock.flush(),this._rfbVeNCryptState=4,!0)}}},{key:"_negotiatePlainAuth",value:function(){if(void 0===this._rfbCredentials.username||void 0===this._rfbCredentials.password)return this.dispatchEvent(new CustomEvent("credentialsrequired",{detail:{types:["username","password"]}})),!1;var e=(0,i.encodeUTF8)(this._rfbCredentials.username),t=(0,i.encodeUTF8)(this._rfbCredentials.password);return this._sock.sQpush32(e.length),this._sock.sQpush32(t.length),this._sock.sQpushString(e),this._sock.sQpushString(t),this._sock.flush(),this._rfbInitState="SecurityResult",!0}},{key:"_negotiateStdVNCAuth",value:function(){if(this._sock.rQwait("auth challenge",16))return!1;if(void 0===this._rfbCredentials.password)return this.dispatchEvent(new CustomEvent("credentialsrequired",{detail:{types:["password"]}})),!1;var e=Array.prototype.slice.call(this._sock.rQshiftBytes(16)),r=t.genDES(this._rfbCredentials.password,e);return this._sock.sQpushBytes(r),this._sock.flush(),this._rfbInitState="SecurityResult",!0}},{key:"_negotiateARDAuth",value:function(){if(void 0===this._rfbCredentials.username||void 0===this._rfbCredentials.password)return this.dispatchEvent(new CustomEvent("credentialsrequired",{detail:{types:["username","password"]}})),!1;if(null!=this._rfbCredentials.ardPublicKey&&null!=this._rfbCredentials.ardCredentials)return this._sock.sQpushBytes(this._rfbCredentials.ardCredentials),this._sock.sQpushBytes(this._rfbCredentials.ardPublicKey),this._sock.flush(),this._rfbCredentials.ardCredentials=null,this._rfbCredentials.ardPublicKey=null,this._rfbInitState="SecurityResult",!0;if(this._sock.rQwait("read ard",4))return!1;var e=this._sock.rQshiftBytes(2),t=this._sock.rQshift16();if(this._sock.rQwait("read ard keylength",2*t,4))return!1;var r=this._sock.rQshiftBytes(t),n=this._sock.rQshiftBytes(t),i=C.default.generateKey({name:"DH",g:e,p:r},!1,["deriveBits"]);return this._negotiateARDAuthAsync(t,n,i),!1}},{key:"_negotiateARDAuthAsync",value:(u=function(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var o=e.apply(t,r);function a(e){I(o,n,i,a,s,"next",e)}function s(e){I(o,n,i,a,s,"throw",e)}a(void 0)}))}}(j().mark((function e(t,r,n){var o,a,s,u,c,l,h,f,d,_;return j().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(o=C.default.exportKey("raw",n.publicKey),a=C.default.deriveBits({name:"DH",public:r},n.privateKey,8*t),s=(0,i.encodeUTF8)(this._rfbCredentials.username).substring(0,63),u=(0,i.encodeUTF8)(this._rfbCredentials.password).substring(0,63),c=window.crypto.getRandomValues(new Uint8Array(128)),l=0;l<s.length;l++)c[l]=s.charCodeAt(l);for(c[s.length]=0,h=0;h<u.length;h++)c[64+h]=u.charCodeAt(h);return c[64+u.length]=0,e.next=11,C.default.digest("MD5",a);case 11:return f=e.sent,e.next=14,C.default.importKey("raw",f,{name:"AES-ECB"},!1,["encrypt"]);case 14:return d=e.sent,e.next=17,C.default.encrypt({name:"AES-ECB"},d,c);case 17:_=e.sent,this._rfbCredentials.ardCredentials=_,this._rfbCredentials.ardPublicKey=o,this._resumeAuthentication();case 21:case"end":return e.stop()}}),e,this)}))),function(e,t,r){return u.apply(this,arguments)})},{key:"_negotiateTightUnixAuth",value:function(){return void 0===this._rfbCredentials.username||void 0===this._rfbCredentials.password?(this.dispatchEvent(new CustomEvent("credentialsrequired",{detail:{types:["username","password"]}})),!1):(this._sock.sQpush32(this._rfbCredentials.username.length),this._sock.sQpush32(this._rfbCredentials.password.length),this._sock.sQpushString(this._rfbCredentials.username),this._sock.sQpushString(this._rfbCredentials.password),this._sock.flush(),this._rfbInitState="SecurityResult",!0)}},{key:"_negotiateTightTunnels",value:function(e){for(var t={0:{vendor:"TGHT",signature:"NOTUNNEL"}},r={},i=0;i<e;i++){var o=this._sock.rQshift32(),a=this._sock.rQshiftStr(4),s=this._sock.rQshiftStr(8);r[o]={vendor:a,signature:s}}return n.Debug("Server Tight tunnel types: "+r),r[1]&&"SICR"===r[1].vendor&&"SCHANNEL"===r[1].signature&&(n.Debug("Detected Siemens server. Assuming NOTUNNEL support."),r[0]={vendor:"TGHT",signature:"NOTUNNEL"}),r[0]?r[0].vendor!=t[0].vendor||r[0].signature!=t[0].signature?this._fail("Client's tunnel type had the incorrect vendor or signature"):(n.Debug("Selected tunnel type: "+t[0]),this._sock.sQpush32(0),this._sock.flush(),!1):this._fail("Server wanted tunnels, but doesn't support the notunnel type")}},{key:"_negotiateTightAuth",value:function(){if(!this._rfbTightVNC){if(this._sock.rQwait("num tunnels",4))return!1;var e=this._sock.rQshift32();if(e>0&&this._sock.rQwait("tunnel capabilities",16*e,4))return!1;if(this._rfbTightVNC=!0,e>0)return this._negotiateTightTunnels(e),!1}if(this._sock.rQwait("sub auth count",4))return!1;var t=this._sock.rQshift32();if(0===t)return this._rfbInitState="SecurityResult",!0;if(this._sock.rQwait("sub auth capabilities",16*t,4))return!1;for(var r={STDVNOAUTH__:1,STDVVNCAUTH_:2,TGHTULGNAUTH:129},i=[],o=0;o<t;o++){this._sock.rQshift32();var a=this._sock.rQshiftStr(12);i.push(a)}for(var s in n.Debug("Server Tight authentication types: "+i),r)if(-1!=i.indexOf(s))switch(this._sock.sQpush32(r[s]),this._sock.flush(),n.Debug("Selected authentication type: "+s),s){case"STDVNOAUTH__":return this._rfbInitState="SecurityResult",!0;case"STDVVNCAUTH_":return this._rfbAuthScheme=2,!0;case"TGHTULGNAUTH":return this._rfbAuthScheme=129,!0;default:return this._fail("Unsupported tiny auth scheme (scheme: "+s+")")}return this._fail("No supported sub-auth types!")}},{key:"_handleRSAAESCredentialsRequired",value:function(e){this.dispatchEvent(e)}},{key:"_handleRSAAESServerVerification",value:function(e){this.dispatchEvent(e)}},{key:"_negotiateRA2neAuth",value:function(){var e=this;return null===this._rfbRSAAESAuthenticationState&&(this._rfbRSAAESAuthenticationState=new x.default(this._sock,(function(){return e._rfbCredentials})),this._rfbRSAAESAuthenticationState.addEventListener("serververification",this._eventHandlers.handleRSAAESServerVerification),this._rfbRSAAESAuthenticationState.addEventListener("credentialsrequired",this._eventHandlers.handleRSAAESCredentialsRequired)),this._rfbRSAAESAuthenticationState.checkInternalEvents(),this._rfbRSAAESAuthenticationState.hasStarted||this._rfbRSAAESAuthenticationState.negotiateRA2neAuthAsync().catch((function(t){"disconnect normally"!==t.message&&e._fail(t.message)})).then((function(){return e._rfbInitState="SecurityResult",!0})).finally((function(){e._rfbRSAAESAuthenticationState.removeEventListener("serververification",e._eventHandlers.handleRSAAESServerVerification),e._rfbRSAAESAuthenticationState.removeEventListener("credentialsrequired",e._eventHandlers.handleRSAAESCredentialsRequired),e._rfbRSAAESAuthenticationState=null})),!1}},{key:"_negotiateMSLogonIIAuth",value:function(){if(this._sock.rQwait("mslogonii dh param",24))return!1;if(void 0===this._rfbCredentials.username||void 0===this._rfbCredentials.password)return this.dispatchEvent(new CustomEvent("credentialsrequired",{detail:{types:["username","password"]}})),!1;var e=this._sock.rQshiftBytes(8),t=this._sock.rQshiftBytes(8),r=this._sock.rQshiftBytes(8),n=C.default.generateKey({name:"DH",g:e,p:t},!0,["deriveBits"]),o=C.default.exportKey("raw",n.publicKey),a=C.default.deriveBits({name:"DH",public:r},n.privateKey,64),s=C.default.importKey("raw",a,{name:"DES-CBC"},!1,["encrypt"]),u=(0,i.encodeUTF8)(this._rfbCredentials.username).substring(0,255),c=(0,i.encodeUTF8)(this._rfbCredentials.password).substring(0,63),l=new Uint8Array(256),h=new Uint8Array(64);window.crypto.getRandomValues(l),window.crypto.getRandomValues(h);for(var f=0;f<u.length;f++)l[f]=u.charCodeAt(f);l[u.length]=0;for(var d=0;d<c.length;d++)h[d]=c.charCodeAt(d);return h[c.length]=0,l=C.default.encrypt({name:"DES-CBC",iv:a},s,l),h=C.default.encrypt({name:"DES-CBC",iv:a},s,h),this._sock.sQpushBytes(o),this._sock.sQpushBytes(l),this._sock.sQpushBytes(h),this._sock.flush(),this._rfbInitState="SecurityResult",!0}},{key:"_negotiateAuthentication",value:function(){switch(this._rfbAuthScheme){case 1:return this._rfbVersion>=3.8?this._rfbInitState="SecurityResult":this._rfbInitState="ClientInitialisation",!0;case 22:return this._negotiateXvpAuth();case 30:return this._negotiateARDAuth();case 2:return this._negotiateStdVNCAuth();case 16:return this._negotiateTightAuth();case 19:return this._negotiateVeNCryptAuth();case 256:return this._negotiatePlainAuth();case 129:return this._negotiateTightUnixAuth();case 6:return this._negotiateRA2neAuth();case 113:return this._negotiateMSLogonIIAuth();default:return this._fail("Unsupported auth scheme (scheme: "+this._rfbAuthScheme+")")}}},{key:"_handleSecurityResult",value:function(){if(this._sock.rQwait("VNC auth response ",4))return!1;var e=this._sock.rQshift32();return 0===e?(this._rfbInitState="ClientInitialisation",n.Debug("Authentication OK"),!0):this._rfbVersion>=3.8?(this._rfbInitState="SecurityReason",this._securityContext="security result",this._securityStatus=e,!0):(this.dispatchEvent(new CustomEvent("securityfailure",{detail:{status:e}})),this._fail("Security handshake failed"))}},{key:"_negotiateServerInit",value:function(){if(this._sock.rQwait("server initialization",24))return!1;var e=this._sock.rQshift16(),r=this._sock.rQshift16(),o=this._sock.rQshift8(),a=this._sock.rQshift8(),s=this._sock.rQshift8(),u=this._sock.rQshift8(),c=this._sock.rQshift16(),l=this._sock.rQshift16(),h=this._sock.rQshift16(),f=this._sock.rQshift8(),d=this._sock.rQshift8(),_=this._sock.rQshift8();this._sock.rQskipBytes(3);var p=this._sock.rQshift32();if(this._sock.rQwait("server init name",p,24))return!1;var y=this._sock.rQshiftStr(p);if(y=(0,i.decodeUTF8)(y,!0),this._rfbTightVNC){if(this._sock.rQwait("TightVNC extended server init header",8,24+p))return!1;var v=this._sock.rQshift16(),g=this._sock.rQshift16(),m=this._sock.rQshift16();this._sock.rQskipBytes(2);var b=16*(v+g+m);if(this._sock.rQwait("TightVNC extended server init header",b,32+p))return!1;this._sock.rQskipBytes(16*v),this._sock.rQskipBytes(16*g),this._sock.rQskipBytes(16*m)}return n.Info("Screen: "+e+"x"+r+", bpp: "+o+", depth: "+a+", bigEndian: "+s+", trueColor: "+u+", redMax: "+c+", greenMax: "+l+", blueMax: "+h+", redShift: "+f+", greenShift: "+d+", blueShift: "+_),this._setDesktopName(y),this._resize(e,r),this._viewOnly||this._keyboard.grab(),this._fbDepth=24,"Intel(r) AMT KVM"===this._fbName&&(n.Warn("Intel AMT KVM only supports 8/16 bit depths. Using low color mode."),this._fbDepth=8),t.messages.pixelFormat(this._sock,this._fbDepth,!0),this._sendEncodings(),t.messages.fbUpdateRequest(this._sock,!1,0,0,this._fbWidth,this._fbHeight),this._updateConnectionState("connected"),!0}},{key:"_sendEncodings",value:function(){var e=[];e.push(S.encodings.encodingCopyRect),24==this._fbDepth&&(e.push(S.encodings.encodingTight),e.push(S.encodings.encodingTightPNG),e.push(S.encodings.encodingZRLE),e.push(S.encodings.encodingJPEG),e.push(S.encodings.encodingHextile),e.push(S.encodings.encodingRRE)),e.push(S.encodings.encodingRaw),e.push(S.encodings.pseudoEncodingQualityLevel0+this._qualityLevel),e.push(S.encodings.pseudoEncodingCompressLevel0+this._compressionLevel),e.push(S.encodings.pseudoEncodingDesktopSize),e.push(S.encodings.pseudoEncodingLastRect),e.push(S.encodings.pseudoEncodingQEMUExtendedKeyEvent),e.push(S.encodings.pseudoEncodingQEMULedEvent),e.push(S.encodings.pseudoEncodingExtendedDesktopSize),e.push(S.encodings.pseudoEncodingXvp),e.push(S.encodings.pseudoEncodingFence),e.push(S.encodings.pseudoEncodingContinuousUpdates),e.push(S.encodings.pseudoEncodingDesktopName),e.push(S.encodings.pseudoEncodingExtendedClipboard),24==this._fbDepth&&(e.push(S.encodings.pseudoEncodingVMwareCursor),e.push(S.encodings.pseudoEncodingCursor)),t.messages.clientEncodings(this._sock,e)}},{key:"_initMsg",value:function(){switch(this._rfbInitState){case"ProtocolVersion":return this._negotiateProtocolVersion();case"Security":return this._negotiateSecurity();case"Authentication":return this._negotiateAuthentication();case"SecurityResult":return this._handleSecurityResult();case"SecurityReason":return this._handleSecurityReason();case"ClientInitialisation":return this._sock.sQpush8(this._shared?1:0),this._sock.flush(),this._rfbInitState="ServerInitialisation",!0;case"ServerInitialisation":return this._negotiateServerInit();default:return this._fail("Unknown init state (state: "+this._rfbInitState+")")}}},{key:"_resumeAuthentication",value:function(){setTimeout(this._initMsg.bind(this),0)}},{key:"_handleSetColourMapMsg",value:function(){return n.Debug("SetColorMapEntries"),this._fail("Unexpected SetColorMapEntries message")}},{key:"_handleServerCutText",value:function(){if(n.Debug("ServerCutText"),this._sock.rQwait("ServerCutText header",7,1))return!1;this._sock.rQskipBytes(3);var e=this._sock.rQshift32();if(e=(0,r.toSigned32bit)(e),this._sock.rQwait("ServerCutText content",Math.abs(e),8))return!1;if(e>=0){var o=this._sock.rQshiftStr(e);if(this._viewOnly)return!0;this.dispatchEvent(new CustomEvent("clipboard",{detail:{text:o}}))}else{e=Math.abs(e);var a=this._sock.rQshift32(),s=65535&a,u=4278190080&a;if(u&$){this._clipboardServerCapabilitiesFormats={},this._clipboardServerCapabilitiesActions={};for(var c=0;c<=15;c++){var l=1<<c;s&l&&(this._clipboardServerCapabilitiesFormats[l]=!0,this._sock.rQshift32())}for(var f=24;f<=31;f++){var d=1<<f;this._clipboardServerCapabilitiesActions[d]=!!(u&d)}var _=[$,te,re,ne,ie];t.messages.extendedClipboardCaps(this._sock,_,{extendedClipboardFormatText:0})}else if(u===te){if(this._viewOnly)return!0;null!=this._clipboardText&&this._clipboardServerCapabilitiesActions[ie]&&1&s&&t.messages.extendedClipboardProvide(this._sock,[1],[this._clipboardText])}else if(u===re){if(this._viewOnly)return!0;this._clipboardServerCapabilitiesActions[ne]&&(null!=this._clipboardText?t.messages.extendedClipboardNotify(this._sock,[1]):t.messages.extendedClipboardNotify(this._sock,[]))}else if(u===ne){if(this._viewOnly)return!0;this._clipboardServerCapabilitiesActions[te]&&1&s&&t.messages.extendedClipboardRequest(this._sock,[1])}else{if(u!==ie)return this._fail("Unexpected action in extended clipboard message: "+u);if(this._viewOnly||!(1&s))return!0;this._clipboardText=null;var p=this._sock.rQshiftBytes(e-4),y=new h.default,v=null;y.setInput(p);for(var g=0;g<=15;g++){var m=1<<g;if(s&m){var b=0,w=y.inflate(4);b|=w[0]<<24,b|=w[1]<<16,b|=w[2]<<8,b|=w[3];var k=y.inflate(b);1===m&&(v=k)}}if(y.setInput(null),null!==v){for(var X="",K=0;K<v.length;K++)X+=String.fromCharCode(v[K]);v=X,(v=(0,i.decodeUTF8)(v)).length>0&&"\0"===v.charAt(v.length-1)&&(v=v.slice(0,-1)),v=v.replaceAll("\r\n","\n"),this.dispatchEvent(new CustomEvent("clipboard",{detail:{text:v}}))}}}return!0}},{key:"_handleServerFenceMsg",value:function(){if(this._sock.rQwait("ServerFence header",8,1))return!1;this._sock.rQskipBytes(3);var e=this._sock.rQshift32(),r=this._sock.rQshift8();if(this._sock.rQwait("ServerFence payload",r,9))return!1;r>64&&(n.Warn("Bad payload length ("+r+") in fence response"),r=64);var i=this._sock.rQshiftStr(r);return this._supportsFence=!0,e&1<<31?(e&=3,t.messages.clientFence(this._sock,e,i),!0):this._fail("Unexpected fence response")}},{key:"_handleXvpMsg",value:function(){if(this._sock.rQwait("XVP version and message",3,1))return!1;this._sock.rQskipBytes(1);var e=this._sock.rQshift8(),t=this._sock.rQshift8();switch(t){case 0:n.Error("XVP Operation Failed");break;case 1:this._rfbXvpVer=e,n.Info("XVP extensions enabled (version "+this._rfbXvpVer+")"),this._setCapability("power",!0);break;default:this._fail("Illegal server XVP message (msg: "+t+")")}return!0}},{key:"_normalMsg",value:function(){var e,r,i;switch(e=this._FBU.rects>0?0:this._sock.rQshift8()){case 0:return(i=this._framebufferUpdate())&&!this._enabledContinuousUpdates&&t.messages.fbUpdateRequest(this._sock,!0,0,0,this._fbWidth,this._fbHeight),i;case 1:return this._handleSetColourMapMsg();case 2:return n.Debug("Bell"),this.dispatchEvent(new CustomEvent("bell",{detail:{}})),!0;case 3:return this._handleServerCutText();case 150:return r=!this._supportsContinuousUpdates,this._supportsContinuousUpdates=!0,this._enabledContinuousUpdates=!1,r&&(this._enabledContinuousUpdates=!0,this._updateContinuousUpdates(),n.Info("Enabling continuous updates.")),!0;case 248:return this._handleServerFenceMsg();case 250:return this._handleXvpMsg();default:return this._fail("Unexpected server message (type "+e+")"),n.Debug("sock.rQpeekBytes(30): "+this._sock.rQpeekBytes(30)),!0}}},{key:"_framebufferUpdate",value:function(){var e=this;if(0===this._FBU.rects){if(this._sock.rQwait("FBU header",3,1))return!1;if(this._sock.rQskipBytes(1),this._FBU.rects=this._sock.rQshift16(),this._display.pending())return this._flushing=!0,this._display.flush().then((function(){e._flushing=!1,e._sock.rQwait("message",1)||e._handleMessage()})),!1}for(;this._FBU.rects>0;){if(null===this._FBU.encoding){if(this._sock.rQwait("rect header",12))return!1;this._FBU.x=this._sock.rQshift16(),this._FBU.y=this._sock.rQshift16(),this._FBU.width=this._sock.rQshift16(),this._FBU.height=this._sock.rQshift16(),this._FBU.encoding=this._sock.rQshift32(),this._FBU.encoding>>=0}if(!this._handleRect())return!1;this._FBU.rects--,this._FBU.encoding=null}return this._display.flip(),!0}},{key:"_handleRect",value:function(){switch(this._FBU.encoding){case S.encodings.pseudoEncodingLastRect:return this._FBU.rects=1,!0;case S.encodings.pseudoEncodingVMwareCursor:return this._handleVMwareCursor();case S.encodings.pseudoEncodingCursor:return this._handleCursor();case S.encodings.pseudoEncodingQEMUExtendedKeyEvent:return this._qemuExtKeyEventSupported=!0,!0;case S.encodings.pseudoEncodingDesktopName:return this._handleDesktopName();case S.encodings.pseudoEncodingDesktopSize:return this._resize(this._FBU.width,this._FBU.height),!0;case S.encodings.pseudoEncodingExtendedDesktopSize:return this._handleExtendedDesktopSize();case S.encodings.pseudoEncodingQEMULedEvent:return this._handleLedEvent();default:return this._handleDataRect()}}},{key:"_handleVMwareCursor",value:function(){var e=this._FBU.x,t=this._FBU.y,r=this._FBU.width,i=this._FBU.height;if(this._sock.rQwait("VMware cursor encoding",1))return!1;var o,a=this._sock.rQshift8();if(this._sock.rQshift8(),0==a){var s=-256;if(o=new Array(r*i*4),this._sock.rQwait("VMware cursor classic encoding",r*i*4*2,2))return!1;for(var u=new Array(r*i),c=0;c<r*i;c++)u[c]=this._sock.rQshift32();for(var l=new Array(r*i),h=0;h<r*i;h++)l[h]=this._sock.rQshift32();for(var f=0;f<r*i;f++)if(0==u[f]){var d=l[f],_=d>>8&255,p=d>>16&255,y=d>>24&255;o[4*f]=_,o[4*f+1]=p,o[4*f+2]=y,o[4*f+3]=255}else(u[f]&s)==s?0==l[f]?(o[4*f]=0,o[4*f+1]=0,o[4*f+2]=0,o[4*f+3]=0):(l[f],o[4*f]=0,o[4*f+1]=0,o[4*f+2]=0,o[4*f+3]=255):(o[4*f]=0,o[4*f+1]=0,o[4*f+2]=0,o[4*f+3]=255)}else{if(1!=a)return n.Warn("The given cursor type is not supported: "+a+" given."),!1;if(this._sock.rQwait("VMware cursor alpha encoding",r*i*4,2))return!1;o=new Array(r*i*4);for(var v=0;v<r*i;v++){var g=this._sock.rQshift32();o[4*v]=g>>24&255,o[4*v+1]=g>>16&255,o[4*v+2]=g>>8&255,o[4*v+3]=255&g}}return this._updateCursor(o,e,t,r,i),!0}},{key:"_handleCursor",value:function(){var e=this._FBU.x,t=this._FBU.y,r=this._FBU.width,n=this._FBU.height,i=r*n*4,o=Math.ceil(r/8)*n,a=i+o;if(this._sock.rQwait("cursor encoding",a))return!1;for(var s=this._sock.rQshiftBytes(i),u=this._sock.rQshiftBytes(o),c=new Uint8Array(r*n*4),l=0,h=0;h<n;h++)for(var f=0;f<r;f++){var d=u[h*Math.ceil(r/8)+Math.floor(f/8)]<<f%8&128?255:0;c[l]=s[l+2],c[l+1]=s[l+1],c[l+2]=s[l],c[l+3]=d,l+=4}return this._updateCursor(c,e,t,r,n),!0}},{key:"_handleDesktopName",value:function(){if(this._sock.rQwait("DesktopName",4))return!1;var e=this._sock.rQshift32();if(this._sock.rQwait("DesktopName",e,4))return!1;var t=this._sock.rQshiftStr(e);return t=(0,i.decodeUTF8)(t,!0),this._setDesktopName(t),!0}},{key:"_handleLedEvent",value:function(){if(this._sock.rQwait("LED Status",1))return!1;var e=this._sock.rQshift8(),t=!!(2&e),r=!!(4&e);return this._remoteCapsLock=r,this._remoteNumLock=t,!0}},{key:"_handleExtendedDesktopSize",value:function(){if(this._sock.rQwait("ExtendedDesktopSize",4))return!1;var e=this._sock.rQpeek8(),t=4+16*e;if(this._sock.rQwait("ExtendedDesktopSize",t))return!1;var r=!this._supportsSetDesktopSize;this._supportsSetDesktopSize=!0,this._sock.rQskipBytes(1),this._sock.rQskipBytes(3);for(var i=0;i<e;i+=1)0===i?(this._screenID=this._sock.rQshift32(),this._sock.rQskipBytes(2),this._sock.rQskipBytes(2),this._sock.rQskipBytes(2),this._sock.rQskipBytes(2),this._screenFlags=this._sock.rQshift32()):this._sock.rQskipBytes(16);if(1===this._FBU.x&&0!==this._FBU.y){var o="";switch(this._FBU.y){case 1:o="Resize is administratively prohibited";break;case 2:o="Out of resources";break;case 3:o="Invalid screen layout";break;default:o="Unknown reason"}n.Warn("Server did not accept the resize request: "+o)}else this._resize(this._FBU.width,this._FBU.height);return r&&this._requestRemoteResize(),!0}},{key:"_handleDataRect",value:function(){var e=this._decoders[this._FBU.encoding];if(!e)return this._fail("Unsupported encoding (encoding: "+this._FBU.encoding+")"),!1;try{return e.decodeRect(this._FBU.x,this._FBU.y,this._FBU.width,this._FBU.height,this._sock,this._display,this._fbDepth)}catch(t){return this._fail("Error decoding rect: "+t),!1}}},{key:"_updateContinuousUpdates",value:function(){this._enabledContinuousUpdates&&t.messages.enableContinuousUpdates(this._sock,!0,0,0,this._fbWidth,this._fbHeight)}},{key:"_resize",value:function(e,t){this._fbWidth=e,this._fbHeight=t,this._display.resize(this._fbWidth,this._fbHeight),this._updateClip(),this._updateScale(),this._updateContinuousUpdates(),this._saveExpectedClientSize()}},{key:"_xvpOp",value:function(e,r){this._rfbXvpVer<e||(n.Info("Sending XVP operation "+r+" (version "+e+")"),t.messages.xvpOp(this._sock,e,r))}},{key:"_updateCursor",value:function(e,t,r,n,i){this._cursorImage={rgbaPixels:e,hotx:t,hoty:r,w:n,h:i},this._refreshCursor()}},{key:"_shouldShowDotCursor",value:function(){if(!this._showDotCursor)return!1;for(var e=3;e<this._cursorImage.rgbaPixels.length;e+=4)if(this._cursorImage.rgbaPixels[e])return!1;return!0}},{key:"_refreshCursor",value:function(){if("connecting"===this._rfbConnectionState||"connected"===this._rfbConnectionState){var e=this._shouldShowDotCursor()?t.cursors.dot:this._cursorImage;this._cursor.change(e.rgbaPixels,e.hotx,e.hoty,e.w,e.h)}}}],[{key:"genDES",value:function(e,t){var r=e.split("").map((function(e){return e.charCodeAt(0)})),n=C.default.importKey("raw",r,{name:"DES-ECB"},!1,["encrypt"]);return C.default.encrypt({name:"DES-ECB"},n,t)}}]);var u}(u.default);oe.messages={keyEvent:function(e,t,r){e.sQpush8(4),e.sQpush8(r),e.sQpush16(0),e.sQpush32(t),e.flush()},QEMUExtendedKeyEvent:function(e,t,r,n){e.sQpush8(255),e.sQpush8(0),e.sQpush16(r),e.sQpush32(t);var i,o,a=(i=n,o=255&n,224==n>>8&&o<127?128|o:i);e.sQpush32(a),e.flush()},pointerEvent:function(e,t,r,n){e.sQpush8(5),e.sQpush8(n),e.sQpush16(t),e.sQpush16(r),e.flush()},_buildExtendedClipboardFlags:function(e,t){for(var r=new Uint8Array(4),n=0,i=0,o=0;o<e.length;o++)i|=e[o];for(var a=0;a<t.length;a++)n|=t[a];return r[0]=i>>24,r[1]=0,r[2]=0,r[3]=n,r},extendedClipboardProvide:function(e,t,r){for(var n=new d.default,o=[],a=0;a<t.length;a++){if(1!=t[a])throw new Error("Unsupported extended clipboard format for Provide message.");r[a]=r[a].replace(/\r\n|\r|\n/gm,"\r\n");var s=(0,i.encodeUTF8)(r[a]+"\0");o.push(s.length>>24&255,s.length>>16&255,s.length>>8&255,255&s.length);for(var u=0;u<s.length;u++)o.push(s.charCodeAt(u))}var c=n.deflate(new Uint8Array(o)),l=new Uint8Array(4+c.length);l.set(oe.messages._buildExtendedClipboardFlags([ie],t)),l.set(c,4),oe.messages.clientCutText(e,l,!0)},extendedClipboardNotify:function(e,t){var r=oe.messages._buildExtendedClipboardFlags([ne],t);oe.messages.clientCutText(e,r,!0)},extendedClipboardRequest:function(e,t){var r=oe.messages._buildExtendedClipboardFlags([te],t);oe.messages.clientCutText(e,r,!0)},extendedClipboardCaps:function(e,t,r){var n=Object.keys(r),i=new Uint8Array(4+4*n.length);n.map((function(e){return parseInt(e)})),n.sort((function(e,t){return e-t})),i.set(oe.messages._buildExtendedClipboardFlags(t,[]));for(var o=4,a=0;a<n.length;a++)i[o]=r[n[a]]>>24,i[o+1]=r[n[a]]>>16,i[o+2]=r[n[a]]>>8,i[o+3]=0|r[n[a]],o+=4,i[3]|=1<<n[a];oe.messages.clientCutText(e,i,!0)},clientCutText:function(e,t){var n,i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];e.sQpush8(6),e.sQpush8(0),e.sQpush8(0),e.sQpush8(0),n=i?(0,r.toUnsigned32bit)(-t.length):t.length,e.sQpush32(n),e.sQpushBytes(t),e.flush()},setDesktopSize:function(e,t,r,n,i){e.sQpush8(251),e.sQpush8(0),e.sQpush16(t),e.sQpush16(r),e.sQpush8(1),e.sQpush8(0),e.sQpush32(n),e.sQpush16(0),e.sQpush16(0),e.sQpush16(t),e.sQpush16(r),e.sQpush32(i),e.flush()},clientFence:function(e,t,r){e.sQpush8(248),e.sQpush8(0),e.sQpush8(0),e.sQpush8(0),e.sQpush32(t),e.sQpush8(r.length),e.sQpushString(r),e.flush()},enableContinuousUpdates:function(e,t,r,n,i,o){e.sQpush8(150),e.sQpush8(t),e.sQpush16(r),e.sQpush16(n),e.sQpush16(i),e.sQpush16(o),e.flush()},pixelFormat:function(e,t,r){var n;n=t>16?32:t>8?16:8;var i=Math.floor(t/3);e.sQpush8(0),e.sQpush8(0),e.sQpush8(0),e.sQpush8(0),e.sQpush8(n),e.sQpush8(t),e.sQpush8(0),e.sQpush8(r?1:0),e.sQpush16((1<<i)-1),e.sQpush16((1<<i)-1),e.sQpush16((1<<i)-1),e.sQpush8(0*i),e.sQpush8(1*i),e.sQpush8(2*i),e.sQpush8(0),e.sQpush8(0),e.sQpush8(0),e.flush()},clientEncodings:function(e,t){e.sQpush8(2),e.sQpush8(0),e.sQpush16(t.length);for(var r=0;r<t.length;r++)e.sQpush32(t[r]);e.flush()},fbUpdateRequest:function(e,t,r,n,i,o){typeof r>"u"&&(r=0),typeof n>"u"&&(n=0),e.sQpush8(3),e.sQpush8(t?1:0),e.sQpush16(r),e.sQpush16(n),e.sQpush16(i),e.sQpush16(o),e.flush()},xvpOp:function(e,t,r){e.sQpush8(250),e.sQpush8(0),e.sQpush8(t),e.sQpush8(r),e.flush()}},oe.cursors={none:{rgbaPixels:new Uint8Array,w:0,h:0,hotx:0,hoty:0},dot:{rgbaPixels:new Uint8Array([255,255,255,255,0,0,0,255,255,255,255,255,0,0,0,255,0,0,0,0,0,0,0,255,255,255,255,255,0,0,0,255,255,255,255,255]),w:3,h:3,hotx:1,hoty:1}}}(s)),s);const Bt=o(Ot),Dt=(0,i.forwardRef)(((e,t)=>{const r=(0,i.useRef)(null),o=(0,i.useRef)(e.autoConnect??!0),a=(0,i.useRef)([]),s=(0,i.useRef)({}),u=(0,i.useRef)(null),[c,l]=(0,i.useState)(!0),{url:h,style:f,className:d,viewOnly:_,rfbOptions:p,focusOnClick:y,clipViewport:v,dragViewport:g,scaleViewport:m,resizeSession:b,showDotCursor:w,background:k,qualityLevel:X,compressionLevel:K,autoConnect:S=!0,retryDuration:E=3e3,debug:x=!1,onConnect:C,onDisconnect:A,onCredentialsRequired:F,onSecurityFailure:L,onClipboard:P,onBell:M,onDesktopName:T,onCapabilities:Q}=e,R=(...e)=>{x&&console.info(...e)},O=(...e)=>{x&&console.error(...e)},B=()=>r.current,D=e=>{r.current=e},j=e=>{o.current=e},I=e=>{if(C)return C(e),void l(!1);R("Connected to remote VNC."),l(!1)},U=e=>{if(A)return A(e),void l(!0);o.current?(R(`Unexpectedly disconnected from remote VNC, retrying in ${E/1e3} seconds.`),a.current.push(setTimeout(V,E))):R("Disconnected from remote VNC."),l(!0)},N=e=>{var t,r,n;const i=B();if(F)return void F(e);const o=(null==(t=null==p?void 0:p.credentials)?void 0:t.username)??"",a=(null==(r=null==p?void 0:p.credentials)?void 0:r.password)??"",s=(null==(n=null==p?void 0:p.credentials)?void 0:n.target)??"";null==i||i.sendCredentials({password:a,username:o,target:s})},z=e=>{T?T(e):R(`Desktop name is ${e.detail.name}`)},H=()=>{const e=B();try{if(!e)return;a.current.forEach(clearTimeout),Object.keys(s.current).forEach((t=>{s.current[t]&&(e.removeEventListener(t,s.current[t]),s.current[t]=void 0)})),e.disconnect(),D(null),j(!1),U(new CustomEvent("disconnect",{detail:{clean:!0}}))}catch(t){O(t),D(null),j(!1)}},V=()=>{try{if(o&&r&&H(),!u.current)return;u.current.innerHTML="";const e=new Bt(u.current,h,p);e.viewOnly=_??!1,e.focusOnClick=y??!1,e.clipViewport=v??!1,e.dragViewport=g??!1,e.resizeSession=b??!1,e.scaleViewport=m??!1,e.showDotCursor=w??!1,e.background=k??"",e.qualityLevel=X??6,e.compressionLevel=K??2,D(e),s.current.connect=I,s.current.disconnect=U,s.current.credentialsrequired=N,s.current.securityfailure=L,s.current.clipboard=P,s.current.bell=M,s.current.desktopname=z,s.current.capabilities=Q,Object.keys(s.current).forEach((t=>{s.current[t]&&e.addEventListener(t,s.current[t])})),j(!0)}catch(e){O(e)}},W=e=>{const t=B(),r={username:(null==e?void 0:e.username)??"",password:(null==e?void 0:e.password)??"",target:(null==e?void 0:e.target)??""};null==t||t.sendCredentials(r)},G=(e,t,r)=>{const n=B();null==n||n.sendKey(e,t,r)},Z=()=>{const e=B();null==e||e.sendCtrlAltDel()},Y=()=>{const e=B();null==e||e.focus()},q=()=>{const e=B();null==e||e.blur()},J=()=>{const e=B();null==e||e.machineShutdown()},$=()=>{const e=B();null==e||e.machineReboot()},ee=()=>{const e=B();null==e||e.machineReset()},te=e=>{const t=B();null==t||t.clipboardPasteFrom(e)};(0,i.useImperativeHandle)(t,(()=>({connect:V,disconnect:H,connected:o.current,sendCredentials:W,sendKey:G,sendCtrlAltDel:Z,focus:Y,blur:q,machineShutdown:J,machineReboot:$,machineReset:ee,clipboardPaste:te,rfb:r.current,loading:c,eventListeners:s.current}))),(0,i.useEffect)((()=>(S&&V(),H)),[]);return(0,n.jsx)("div",{style:f,className:d,ref:u,onMouseEnter:()=>{document.activeElement&&document.activeElement instanceof HTMLElement&&document.activeElement.blur(),(()=>{const e=B();e&&e.focus()})()},onMouseLeave:()=>{const e=B();e&&e.blur()}})}))}}]);
//# sourceMappingURL=586eae61-5904718bf1e32368cf41.js.map