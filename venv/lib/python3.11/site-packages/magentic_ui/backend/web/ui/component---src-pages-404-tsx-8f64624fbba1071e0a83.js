"use strict";(self.webpackChunkMagentic_UI=self.webpackChunkMagentic_UI||[]).push([[453],{70731:function(e,t,n){n.r(t),n.d(t,{Head:function(){return m}});var o=n(96540),r=n(64810);const a={display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"100vh",padding:"2rem",textAlign:"center",backgroundColor:"#f8f9fa",fontFamily:"-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif"},l={fontSize:"6rem",margin:0,color:"#343a40",fontWeight:700},i={fontSize:"2rem",margin:"1rem 0 2rem",color:"#495057",fontWeight:500},c={fontSize:"1.2rem",marginBottom:"2rem",color:"#6c757d",maxWidth:"600px"},s={display:"inline-block",padding:"0.75rem 1.5rem",backgroundColor:"#007bff",color:"white",textDecoration:"none",borderRadius:"4px",fontWeight:500,transition:"background-color 0.2s ease"};t.default=()=>o.createElement("main",{style:a},o.createElement("h1",{style:l},"404"),o.createElement("h2",{style:i},"Page Not Found"),o.createElement("p",{style:c},"Sorry, we couldn't find the page you're looking for. The page might have been moved, deleted, or never existed."),o.createElement(r.Link,{to:"/",style:s,onMouseOver:e=>{e.currentTarget.style.backgroundColor="#0069d9"},onMouseOut:e=>{e.currentTarget.style.backgroundColor="#007bff"}},"Return to Home"));const m=()=>o.createElement("title",null,"Page Not Found | Magentic-UI ")}}]);
//# sourceMappingURL=component---src-pages-404-tsx-8f64624fbba1071e0a83.js.map