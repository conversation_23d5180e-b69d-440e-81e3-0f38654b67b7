{"version": 3, "file": "component---src-pages-404-tsx-8f64624fbba1071e0a83.js", "mappings": "oLAGA,MAAMA,EAAsC,CAC1CC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,SAChBC,UAAW,QACXC,QAAS,OACTC,UAAW,SACXC,gBAAiB,UACjBC,WAAY,+HAGRC,EAAoC,CACxCC,SAAU,OACVC,OAAQ,EACRC,MAAO,UACPC,WAAY,KAGRC,EAAuC,CAC3CJ,SAAU,OACVC,OAAQ,cACRC,MAAO,UACPC,WAAY,KAGRE,EAAiC,CACrCL,SAAU,SACVM,aAAc,OACdJ,MAAO,UACPK,SAAU,SAGNC,EAAiC,CACrClB,QAAS,eACTK,QAAS,iBACTE,gBAAiB,UACjBK,MAAO,QACPO,eAAgB,OAChBC,aAAc,MACdP,WAAY,IACZQ,WAAY,8BAsBd,UAnB0CC,IAEtCC,EAAAA,cAAA,QAAMC,MAAOzB,GACXwB,EAAAA,cAAA,MAAIC,MAAOf,GAAc,OACzBc,EAAAA,cAAA,MAAIC,MAAOV,GAAiB,kBAC5BS,EAAAA,cAAA,KAAGC,MAAOT,GAAW,mHAGrBQ,EAAAA,cAACE,EAAAA,KAAI,CAACC,GAAG,IAAIF,MAAON,EAAWS,YAAcC,IAC3CA,EAAEC,cAAcL,MAAMjB,gBAAkB,SAAS,EAChDuB,WAAaF,IACdA,EAAEC,cAAcL,MAAMjB,gBAAkB,SAAS,GAChD,mBASF,MAAMwB,EAAeA,IAAMR,EAAAA,cAAA,aAAO,gC", "sources": ["webpack://Magentic-UI/./src/pages/404.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { <PERSON>, HeadFC, PageProps } from \"gatsby\"\n\nconst containerStyle: React.CSSProperties = {\n  display: \"flex\",\n  flexDirection: \"column\",\n  alignItems: \"center\",\n  justifyContent: \"center\",\n  minHeight: \"100vh\",\n  padding: \"2rem\",\n  textAlign: \"center\",\n  backgroundColor: \"#f8f9fa\",\n  fontFamily: \"-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif\"\n}\n\nconst headingStyle: React.CSSProperties = {\n  fontSize: \"6rem\",\n  margin: 0,\n  color: \"#343a40\",\n  fontWeight: 700\n}\n\nconst subheadingStyle: React.CSSProperties = {\n  fontSize: \"2rem\",\n  margin: \"1rem 0 2rem\",\n  color: \"#495057\",\n  fontWeight: 500\n}\n\nconst textStyle: React.CSSProperties = {\n  fontSize: \"1.2rem\",\n  marginBottom: \"2rem\",\n  color: \"#6c757d\",\n  maxWidth: \"600px\"\n}\n\nconst linkStyle: React.CSSProperties = {\n  display: \"inline-block\",\n  padding: \"0.75rem 1.5rem\",\n  backgroundColor: \"#007bff\",\n  color: \"white\",\n  textDecoration: \"none\",\n  borderRadius: \"4px\",\n  fontWeight: 500,\n  transition: \"background-color 0.2s ease\"\n}\n\nconst NotFoundPage: React.FC<PageProps> = () => {\n  return (\n    <main style={containerStyle}>\n      <h1 style={headingStyle}>404</h1>\n      <h2 style={subheadingStyle}>Page Not Found</h2>\n      <p style={textStyle}>\n        Sorry, we couldn't find the page you're looking for. The page might have been moved, deleted, or never existed.\n      </p>\n      <Link to=\"/\" style={linkStyle} onMouseOver={(e) => {\n        e.currentTarget.style.backgroundColor = \"#0069d9\";\n      }} onMouseOut={(e) => {\n        e.currentTarget.style.backgroundColor = \"#007bff\";\n      }}>\n        Return to Home\n      </Link>\n    </main>\n  )\n}\n\nexport default NotFoundPage\n\nexport const Head: HeadFC = () => <title>Page Not Found | Magentic-UI </title>\n"], "names": ["containerStyle", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "padding", "textAlign", "backgroundColor", "fontFamily", "headingStyle", "fontSize", "margin", "color", "fontWeight", "subheadingStyle", "textStyle", "marginBottom", "max<PERSON><PERSON><PERSON>", "linkStyle", "textDecoration", "borderRadius", "transition", "NotFoundPage", "React", "style", "Link", "to", "onMouseOver", "e", "currentTarget", "onMouseOut", "Head"], "sourceRoot": ""}