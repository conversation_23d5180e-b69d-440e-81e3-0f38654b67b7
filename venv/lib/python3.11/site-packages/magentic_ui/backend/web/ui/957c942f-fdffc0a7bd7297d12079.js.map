{"version": 3, "file": "957c942f-fdffc0a7bd7297d12079.js", "mappings": "2SAuBA,SAASA,EAAIC,EAAMC,GAQnB,CACgBF,EAAIG,KAAK,KAAM,QACjBH,EAAIG,KAAK,KAAM,SAE7B,SAASC,IAAU,CAQnB,SAASC,EAAWC,EAAIC,EAAUC,GAChC,MAAMC,EAAaF,EAASG,KAAIC,IAC9B,MAAMC,EARV,SAAoBC,EAAQC,GAC1B,MAAO,IACFD,KACAC,EAEP,CAGoBC,CAAWP,EAAeG,EAAQC,SAElD,OADAN,EAAGU,iBAAiBL,EAAQM,UAAWN,EAAQO,GAAIN,GAC5C,WACLN,EAAGa,oBAAoBR,EAAQM,UAAWN,EAAQO,GAAIN,EACxD,CAAC,IAEH,OAAO,WACLH,EAAWW,SAAQC,IACjBA,GAAQ,GAEZ,CACF,CAEA,MAAMC,GAAe,EACfC,EAAW,mBACjB,MAAMC,UAAqBC,OAI3B,SAASC,EAAUC,EAAWzB,GAC5B,MACQ,IAAIsB,EADRF,EACqBC,EAEA,GAAGA,MAAarB,GAAW,KAEtD,CATAsB,EAAaI,UAAUC,SAAW,WAChC,OAAOC,KAAK5B,OACd,EASA,MAAM6B,UAAsB,YAC1B,WAAAC,IAAeC,GACbC,SAASD,GACTH,KAAKK,UAAY,KACjBL,KAAKT,OAASjB,EACd0B,KAAKM,cAAgBC,IACnB,MAAMF,EAAYL,KAAKQ,eACnBH,EAAUI,cACZJ,EAAUK,WAMAH,EAAMI,iBACCjB,GACjBa,EAAMK,gBAIR,EAEFZ,KAAKQ,aAAe,KAClB,IAAKR,KAAKK,UACR,MAAM,IAAIV,MAAM,mDAElB,OAAOK,KAAKK,SAAS,EAEvBL,KAAKa,aAAeR,IAClBL,KAAKK,UAAYA,CAAS,CAE9B,CACA,iBAAAS,GACEd,KAAKT,OAAShB,EAAWwC,OAAQ,CAAC,CAChC5B,UAAW,QACXC,GAAIY,KAAKM,gBAEb,CACA,iBAAAU,CAAkBC,GAChB,KAAIA,aAAevB,GAOnB,MAAMuB,EAHJjB,KAAKkB,SAAS,CAAC,EAInB,CACA,oBAAAC,GACEnB,KAAKT,QACP,CACA,MAAA6B,GACE,OAAOpB,KAAKqB,MAAMC,SAAStB,KAAKa,aAClC,EAGF,MAKMU,EAAWC,GAASA,EAAQ,EAI5BC,EAAe,CAACC,EAAQC,KAC5B,MAAMC,EAAeF,EAAOG,cAAgBF,EAAYE,YAClDC,EAAgBP,EAASG,EAAOF,OAChCO,EAAcR,EAASI,EAAYH,OACzC,OAAII,EACK,iDACmCE,wBAC1BC,UAGX,+CACmCD,kBAC9BJ,EAAOG,4BACPF,EAAYE,gCACRE,OACf,EAEGC,EAAc,CAACC,EAAIP,EAAQQ,IACZR,EAAOG,cAAgBK,EAAQL,YAEzC,oBACMI,mCACcC,EAAQC,cAE9B,oBACQF,oBACDP,EAAOG,6CACQK,EAAQC,8BACvBD,EAAQL,oBAclBO,EAAkBV,GAAU,4DAE3BH,EAASG,EAAOF,WA4BjBa,EAAS,CACbC,4BAlFkC,8NAmFlCC,YA7EkBC,GAAS,2CACWjB,EAASiB,EAAMd,OAAOF,WA6E5DiB,aA5CmBC,IACnB,MAAMC,EAAWD,EAAOf,YACxB,GAAIgB,EACF,OAAOlB,EAAaiB,EAAOhB,OAAQiB,GAErC,MAAMT,EAAUQ,EAAOR,QACvB,OAAIA,EACKF,EAAYU,EAAOP,YAAaO,EAAOhB,OAAQQ,GAEjD,gDAAgD,EAoCvDU,UA9BgBC,IAChB,GAAsB,WAAlBA,EAAOC,OACT,MAAO,sCAEHV,EAAgBS,EAAOnB,gBAG7B,MAAMiB,EAAWE,EAAOlB,YAClBO,EAAUW,EAAOX,QACvB,OAAIS,EACK,6CAEHlB,EAAaoB,EAAOnB,OAAQiB,WAG9BT,EACK,6CAEHF,EAAYa,EAAOV,YAAaU,EAAOnB,OAAQQ,WAG9C,oEAEHE,EAAgBS,EAAOnB,aAC1B,GASGqB,EAAS,CACbC,EAAG,EACHC,EAAG,GAECC,EAAM,CAACC,EAAQC,KAAW,CAC9BJ,EAAGG,EAAOH,EAAII,EAAOJ,EACrBC,EAAGE,EAAOF,EAAIG,EAAOH,IAEjBI,EAAW,CAACF,EAAQC,KAAW,CACnCJ,EAAGG,EAAOH,EAAII,EAAOJ,EACrBC,EAAGE,EAAOF,EAAIG,EAAOH,IAEjBK,EAAY,CAACH,EAAQC,IAAWD,EAAOH,IAAMI,EAAOJ,GAAKG,EAAOF,IAAMG,EAAOH,EAC7EM,EAASC,IAAS,CACtBR,EAAe,IAAZQ,EAAMR,GAAWQ,EAAMR,EAAI,EAC9BC,EAAe,IAAZO,EAAMP,GAAWO,EAAMP,EAAI,IAE1BQ,EAAQ,CAACC,EAAMC,EAAOC,EAAa,IAC1B,MAATF,EACK,CACLV,EAAGW,EACHV,EAAGW,GAGA,CACLZ,EAAGY,EACHX,EAAGU,GAGDE,EAAW,CAACV,EAAQC,IAAWU,KAAKC,MAAMX,EAAOJ,EAAIG,EAAOH,IAAM,GAAKI,EAAOH,EAAIE,EAAOF,IAAM,GAC/Fe,EAAY,CAACC,EAAQC,IAAWJ,KAAKK,OAAOD,EAAOtF,KAAI4E,GAASK,EAASI,EAAQT,MACjFY,EAAQhF,GAAMoE,IAAS,CAC3BR,EAAG5D,EAAGoE,EAAMR,GACZC,EAAG7D,EAAGoE,EAAMP,KAgBd,MAAMoB,EAAmB,CAACC,EAASd,KAAU,CAC3Ce,IAAKD,EAAQC,IAAMf,EAAMP,EACzBuB,KAAMF,EAAQE,KAAOhB,EAAMR,EAC3ByB,OAAQH,EAAQG,OAASjB,EAAMP,EAC/ByB,MAAOJ,EAAQI,MAAQlB,EAAMR,IAEzB2B,EAAaL,GAAW,CAAC,CAC7BtB,EAAGsB,EAAQE,KACXvB,EAAGqB,EAAQC,KACV,CACDvB,EAAGsB,EAAQI,MACXzB,EAAGqB,EAAQC,KACV,CACDvB,EAAGsB,EAAQE,KACXvB,EAAGqB,EAAQG,QACV,CACDzB,EAAGsB,EAAQI,MACXzB,EAAGqB,EAAQG,SAwBPG,EAAO,CAACX,EAAQY,IAChBA,GAASA,EAAMC,kBAvDH,EAACD,EAAOE,KACxB,MAAMlC,GAAS,OAAQ,CACrB0B,IAAKT,KAAKkB,IAAID,EAAQR,IAAKM,EAAMN,KACjCG,MAAOZ,KAAKK,IAAIY,EAAQL,MAAOG,EAAMH,OACrCD,OAAQX,KAAKK,IAAIY,EAAQN,OAAQI,EAAMJ,QACvCD,KAAMV,KAAKkB,IAAID,EAAQP,KAAMK,EAAML,QAErC,OAAI3B,EAAOoC,OAAS,GAAKpC,EAAOqC,QAAU,EACjC,KAEFrC,CAAM,EA8CJsC,CAAYN,EAAMO,cAAenB,IAEnC,OAAQA,GAEjB,IAAIoB,EAAa,EACfC,OACAC,kBACAC,OACAX,YAEA,MAAMY,EA3BS,EAACxB,EAAQY,IACnBA,EAGER,EAAiBJ,EAAQY,EAAMa,OAAOC,KAAKC,cAFzC3B,EAyBQ4B,CAASP,EAAKQ,UAAWjB,GACpCkB,EAtBS,EAAC9B,EAAQuB,EAAMD,IAC1BA,GAAmBA,EAAgBS,YAC9B,IACF/B,EACH,CAACuB,EAAKS,KAAMhC,EAAOuB,EAAKS,KAAOV,EAAgBS,YAAYR,EAAK9B,OAG7DO,EAeWiC,CAAST,EAAUD,EAAMD,GAE3C,MAAO,CACLD,OACAC,kBACAY,OAJcvB,EAAKmB,EAAWlB,GAK/B,EAGCuB,EAAkB,CAACC,EAAWC,KAC/BD,EAAUxB,OAA8DjF,IACzE,MAAM2G,EAAaF,EAAUxB,MACvB2B,EAAanD,EAASiD,EAAWC,EAAWb,OAAOe,SACnDC,EAAqBnD,EAAOiD,GAC5B3B,EAAQ,IACT0B,EACHb,OAAQ,CACNe,QAASF,EAAWb,OAAOe,QAC3BE,QAASL,EACTX,KAAM,CACJhC,MAAO6C,EACPZ,aAAcc,GAEhB1B,IAAKuB,EAAWb,OAAOV,MAGrBD,EAAUM,EAAW,CACzBC,KAAMe,EAAUtB,QAAQO,KACxBC,gBAAiBc,EAAUtB,QAAQQ,gBACnCC,KAAMa,EAAUb,KAChBX,UAOF,MALe,IACVwB,EACHxB,QACAE,UAEW,EAGf,MAAM6B,GAAiB,QAAWC,GAAcA,EAAWC,QAAO,CAACC,EAAUJ,KAC3EI,EAASJ,EAAQK,WAAW/E,IAAM0E,EAC3BI,IACN,CAAC,KACEE,GAAiB,QAAWC,GAAcA,EAAWJ,QAAO,CAACC,EAAUJ,KAC3EI,EAASJ,EAAQK,WAAW/E,IAAM0E,EAC3BI,IACN,CAAC,KACEI,GAAkB,QAAWN,GAAcO,OAAOC,OAAOR,KACzDS,GAAkB,QAAWJ,GAAcE,OAAOC,OAAOH,KAE/D,IAAIK,GAA+B,QAAW,CAAC1F,EAAaqF,KAC1D,MAAMrE,EAASyE,EAAgBJ,GAAYM,QAAOC,GAAa5F,IAAgB4F,EAAUT,WAAWnF,cAAa6F,MAAK,CAACC,EAAGC,IAAMD,EAAEX,WAAWxF,MAAQoG,EAAEZ,WAAWxF,QAClK,OAAOqB,CAAM,IAGf,SAASgF,EAAkBC,GACzB,OAAIA,EAAOC,IAAyB,YAAnBD,EAAOC,GAAG5J,KAClB2J,EAAOC,GAAGpG,YAEZ,IACT,CACA,SAASqG,EAAcF,GACrB,OAAIA,EAAOC,IAAyB,YAAnBD,EAAOC,GAAG5J,KAClB2J,EAAOC,GAAG7F,QAEZ,IACT,CAEA,IAAI+F,GAA0B,QAAW,CAACC,EAAQC,IAASA,EAAKX,QAAOY,GAAQA,EAAKpB,WAAW/E,KAAOiG,EAAOlB,WAAW/E,OAoDpHoG,EAAW,CAACZ,EAAW9F,IAAgB8F,EAAUT,WAAWnF,cAAgBF,EAAYqF,WAAW/E,GAEvG,MAAMqG,EAAgB,CACpB9E,MAAOT,EACPY,MAAO,GAEH4E,EAAc,CAClBC,UAAW,CAAC,EACZC,QAAS,CAAC,EACVC,IAAK,IAEDC,EAAW,CACfC,UAAWL,EACXM,YAAaP,EACbP,GAAI,MAGN,IAAIe,EAAW,CAACC,EAAYC,IAAerF,GAASoF,GAAcpF,GAASA,GAASqF,EAEhFC,EAAiCpE,IACnC,MAAMqE,EAAmBJ,EAASjE,EAAMN,IAAKM,EAAMJ,QAC7C0E,EAAqBL,EAASjE,EAAML,KAAMK,EAAMH,OACtD,OAAOK,IAEL,GADoBmE,EAAiBnE,EAAQR,MAAQ2E,EAAiBnE,EAAQN,SAAW0E,EAAmBpE,EAAQP,OAAS2E,EAAmBpE,EAAQL,OAEtJ,OAAO,EAET,MAAM0E,EAA+BF,EAAiBnE,EAAQR,MAAQ2E,EAAiBnE,EAAQN,QACzF4E,EAAiCF,EAAmBpE,EAAQP,OAAS2E,EAAmBpE,EAAQL,OAEtG,GAD6B0E,GAAgCC,EAE3D,OAAO,EAET,MAAMC,EAAqBvE,EAAQR,IAAMM,EAAMN,KAAOQ,EAAQN,OAASI,EAAMJ,OACvE8E,EAAuBxE,EAAQP,KAAOK,EAAML,MAAQO,EAAQL,MAAQG,EAAMH,MAEhF,GADgC4E,GAAsBC,EAEpD,OAAO,EAGT,OADgCD,GAAsBD,GAAkCE,GAAwBH,CAClF,CAC/B,EAGCI,EAA+B3E,IACjC,MAAMqE,EAAmBJ,EAASjE,EAAMN,IAAKM,EAAMJ,QAC7C0E,EAAqBL,EAASjE,EAAML,KAAMK,EAAMH,OACtD,OAAOK,GACemE,EAAiBnE,EAAQR,MAAQ2E,EAAiBnE,EAAQN,SAAW0E,EAAmBpE,EAAQP,OAAS2E,EAAmBpE,EAAQL,MAEzJ,EAGH,MAAM+E,GAAW,CACfC,UAAW,WACXhG,KAAM,IACNiG,cAAe,IACfnH,MAAO,MACPyD,IAAK,SACL2D,KAAM,SACNC,eAAgB,OAChBC,aAAc,QACdC,cAAe,SAEXC,GAAa,CACjBN,UAAW,aACXhG,KAAM,IACNiG,cAAe,IACfnH,MAAO,OACPyD,IAAK,QACL2D,KAAM,QACNC,eAAgB,MAChBC,aAAc,SACdC,cAAe,UAcjB,MAWME,GAAc,EAClBhG,OAAQiG,EACRvI,cACAwI,WACAC,4BACAC,8BAEA,MAAMC,EAAkBF,EAlBI,EAACnG,EAAQtC,KACrC,MAAMiE,EAAejE,EAAYkD,MAAQlD,EAAYkD,MAAMa,OAAOC,KAAKC,aAAe7C,EACtF,OAAOsB,EAAiBJ,EAAQ2B,EAAa,EAgBO2E,CAAsBL,EAAevI,GAAeuI,EACxG,MAf2B,EAACjG,EAAQtC,EAAa0I,MAC5C1I,EAAYoD,QAAQoB,QAGlBkE,EAAwB1I,EAAYoD,QAAQoB,OAA5CkE,CAAoDpG,GAWpDuG,CAAqBF,EAAiB3I,EAAa0I,IAThC,EAACpG,EAAQkG,EAAUE,IAA4BA,EAAwBF,EAAxBE,CAAkCpG,GASrBwG,CAAoBH,EAAiBH,EAAUE,EAAwB,EAMzJK,GAAmBvK,GAAQ8J,GAAY,IACxC9J,EACHkK,wBAAyBb,IAkC3B,SAASmB,IAAsB,cAC7BC,EAAa,YACbjJ,EAAW,YACXkH,EAAW,SACXsB,EAAQ,mBACRU,EAAkB,KAClBC,IAEA,OAAOF,EAAc9D,QAAO,SAAiBiE,EAAQtD,GACnD,MAAMxD,EAnBV,SAAmBwD,EAAWoB,GAC5B,MAAM/C,EAAY2B,EAAUnC,KAAKQ,UAC3BkF,EAAW,CACfzG,IAAKsE,EAAYrF,MAAMP,EACvByB,MAAO,EACPD,OAAQ,EACRD,KAAMqE,EAAYrF,MAAMR,GAE1B,OAAO,QAAQ,QAAO8C,EAAWkF,GACnC,CAUmBC,CAAUxD,EAAWoB,GAC9B5G,EAAKwF,EAAUT,WAAW/E,GAChC8I,EAAOrC,IAAIwC,KAAKjJ,GAnDO9B,MA0DvB,KA1DuBA,EAoDc,CACnC8D,SACAtC,cACAwI,WACAC,2BAA2B,GAxDEH,GAAY,IAC1C9J,EACHkK,wBAAyBpB,KA0DrB,OADA8B,EAAOvC,UAAUf,EAAUT,WAAW/E,KAAM,EACrC8I,EAET,MAAMI,EAjDe,EAAClJ,EAAI6I,EAAMD,KAClC,GAAkC,kBAAvBA,EACT,OAAOA,EAET,IAAKC,EACH,OAAO,EAET,MAAM,UACJtC,EAAS,QACTC,GACEqC,EACJ,GAAItC,EAAUvG,GACZ,OAAO,EAET,MAAM8E,EAAW0B,EAAQxG,GACzB,OAAO8E,GAAWA,EAASoE,aAAoB,EAkCvBC,CAAiBnJ,EAAI6I,EAAMD,GAC3CjF,EAAe,CACnBzD,YAAaF,EACbkJ,iBAGF,OADAJ,EAAOtC,QAAQxG,GAAM2D,EACdmF,CACT,GAAG,CACDrC,IAAK,GACLD,QAAS,CAAC,EACVD,UAAW,CAAC,GAEhB,CASA,SAAS6C,IAAQ,kBACfC,EAAiB,WACjBC,EAAU,YACV1C,EAAW,YACXlH,IAEA,MAAM6J,EAbR,SAA4BtE,EAAYpI,GACtC,IAAKoI,EAAWuE,OACd,OAAO,EAET,MAAMC,EAAkBxE,EAAWA,EAAWuE,OAAS,GAAGzE,WAAWxF,MACrE,OAAO1C,EAAQyM,WAAaG,EAAkBA,EAAkB,CAClE,CAOmBC,CAAmBL,EAAmB,CACrDC,eAEF,MAAO,CACL3C,UAAWL,EACXM,cACAd,GAAI,CACF5J,KAAM,UACNwD,YAAa,CACXE,YAAaF,EAAYqF,WAAW/E,GACpCT,MAAOgK,IAIf,CACA,SAASI,IAAuB,UAC9BnE,EAAS,kBACT6D,EAAiB,YACjB3J,EAAW,SACXwI,EAAQ,YACRtB,EAAW,KACXiC,EAAI,MACJtJ,EAAK,mBACLqJ,IAEA,MAAMU,EAAalD,EAASZ,EAAW9F,GACvC,GAAa,MAATH,EACF,OAAO6J,GAAQ,CACbC,oBACAC,aACA1C,cACAlH,gBAGJ,MAAMkK,EAAQP,EAAkBQ,MAAK1D,GAAQA,EAAKpB,WAAWxF,QAAUA,IACvE,IAAKqK,EACH,OAAOR,GAAQ,CACbC,oBACAC,aACA1C,cACAlH,gBAGJ,MAAMoK,EAAkB9D,EAAwBR,EAAW6D,GACrDU,EAAYV,EAAkBW,QAAQJ,GAU5C,MAAO,CACLjD,UATgB+B,GAAsB,CACtCC,cAFemB,EAAgBG,MAAMF,GAGrCrK,cACAkH,cACAiC,OACAX,SAAUA,EAAStF,MACnBgG,uBAIAhC,cACAd,GAAI,CACF5J,KAAM,UACNwD,YAAa,CACXE,YAAaF,EAAYqF,WAAW/E,GACpCT,UAIR,CAEA,SAAS2K,GAAsBhK,EAAaiK,GAC1C,OAAOC,QAAQD,EAAcE,SAASnK,GACxC,CAEA,IAiDIoK,GAAkB,EACpBC,kBACA5K,eACA6F,YACAP,aACAvF,cACA2J,oBACAmB,iBACAtC,WACAiC,oBAEA,MAAMM,EAAQD,EAAe1E,GAE7B,GADC2E,GAAgI9M,IAC9G,YAAf8M,EAAMvO,KAAoB,CAC5B,MAAMqN,EArCQ,GAChBgB,kBACA5K,eACA0J,oBACA3I,eAEA,IAAK2I,EAAkBG,OACrB,OAAO,KAET,MAAMkB,EAAehK,EAASnB,MACxBoL,EAAgBJ,EAAkBG,EAAe,EAAIA,EAAe,EACpEE,EAAavB,EAAkB,GAAGtE,WAAWxF,MAC7CsL,EAAYxB,EAAkBA,EAAkBG,OAAS,GAAGzE,WAAWxF,MAE7E,OAAIoL,EAAgBC,GAGhBD,GAJehL,EAAekL,EAAYA,EAAY,GAEjD,KAKFF,CAAa,EAiBDG,CAAY,CAC3BP,kBACA5K,eACAe,SAAU+J,EAAM/K,YAChB2J,sBAEF,OAAgB,MAAZE,EACK,KAEFI,GAAuB,CAC5BnE,YACA6D,oBACA3J,cACAwI,WACAW,KAAM2B,EAAe7D,UACrBC,YAAa4D,EAAe5D,YAC5BrH,MAAOgK,GAEX,CACA,MAAMA,EAlFU,GAChBgB,kBACA7K,cACAuF,aACAhF,UACAkK,oBAEA,IAAKzK,EAAYqL,iBACf,OAAO,KAET,MAAMC,EAAY/K,EAAQC,YAEpB+K,EADchG,EAAW+F,GACMjG,WAAWxF,MAEhD,OADyC2K,GAAsBc,EAAWb,GAEpEI,EACKU,EAEFA,EAAmB,EAExBV,EACKU,EAAmB,EAErBA,CAAgB,EA2DNC,CAAY,CAC3BX,kBACA7K,cACAiH,UAAW6D,EAAe7D,UAC1B1B,aACAhF,QAASwK,EAAMxK,QACfkK,kBAEF,OAAgB,MAAZZ,EACK,KAEFI,GAAuB,CAC5BnE,YACA6D,oBACA3J,cACAwI,WACAW,KAAM2B,EAAe7D,UACrBC,YAAa4D,EAAe5D,YAC5BrH,MAAOgK,GACP,EAgBA4B,GAAgB,EAClBhB,gBACAtE,SACAZ,iBAEA,MAAMhF,EAAU8F,EAAcF,GAC7B5F,GAAgEtC,IACjE,MAAMyN,EAAcnL,EAAQC,YACtBmL,EAASpG,EAAWmG,GAAa/H,KAAKiI,UAAUD,OAChDE,EAtB0B,GAChC5E,YACAwD,gBACAiB,cACAxE,kBAEA,MAAM4E,EAAcpB,QAAQzD,EAAUH,QAAQ4E,IAAgBzE,EAAUJ,UAAU6E,IAClF,OAAIlB,GAAsBkB,EAAajB,GAC9BqB,EAAc1K,EAASQ,EAAOsF,EAAYrF,OAE5CiK,EAAc5E,EAAYrF,MAAQT,CAAM,EAY5B2K,CAA4B,CAC7C9E,UAAWd,EAAOc,UAClBwD,gBACAiB,cACAxE,YAAaf,EAAOe,cAEtB,OAAO3F,EAAIoK,EAAQE,EAAW,EAGhC,MAAMG,GAAqC,CAACnI,EAAMoI,IAAQA,EAAIC,OAAOrI,EAAKhD,OAASoL,EAAIL,UAAU/H,EAAKoE,MAAQ,EAExGkE,GAA8B,CAACtI,EAAMvB,EAAQ8J,IAAa9J,EAAOuB,EAAKqE,gBAAkBkE,EAASF,OAAOrI,EAAKqE,gBAAkBkE,EAASR,UAAU/H,EAAKuE,eAAiB,EACxKiE,GAAU,EACdxI,OACAyI,iBACAF,cACItK,EAAM+B,EAAK9B,KAAMuK,EAAenI,UAAUN,EAAKS,KAAO0H,GAAmCnI,EAAMuI,GAAWD,GAA4BtI,EAAMyI,EAAenI,UAAWiI,IACtKG,GAAW,EACf1I,OACAyI,iBACAF,cACItK,EAAM+B,EAAK9B,KAAMuK,EAAenI,UAAUN,EAAKhD,OAXZ,EAACgD,EAAMoI,IAAQA,EAAIC,OAAOrI,EAAKS,KAAO2H,EAAIL,UAAU/H,EAAKoE,MAAQ,EAW5CuE,CAAiC3I,EAAMuI,GAAWD,GAA4BtI,EAAMyI,EAAenI,UAAWiI,IAO5K,IAAIK,GAAiB,EACnBtG,SACAL,YACAP,aACAb,YACA+F,oBAEA,MAAMd,EAAoB/D,EAA6BlB,EAAUW,WAAW/E,GAAIiF,GAC1EmH,EAAgB5G,EAAUnC,KAC1BE,EAAOa,EAAUb,KACvB,IAAK8F,EAAkBG,OACrB,MAjBgB,GAClBjG,OACA8I,WACAP,cACItK,EAAM+B,EAAK9B,KAAM4K,EAASC,WAAW/I,EAAKhD,OAASmL,GAAmCnI,EAAMuI,GAAWD,GAA4BtI,EAAM8I,EAASC,WAAYR,IAazJS,CAAY,CACjBhJ,OACA8I,SAAUjI,EAAUf,KACpByI,SAAUM,IAGd,MAAM,UACJzF,EAAS,YACTC,GACEf,EACE2G,EAAe7F,EAAUF,IAAI,GACnC,GAAI+F,EAAc,CAChB,MAAMC,EAAUxH,EAAWuH,GAC3B,GAAItC,GAAsBsC,EAAcrC,GACtC,OAAO8B,GAAS,CACd1I,OACAyI,eAAgBS,EAAQpJ,KACxByI,SAAUM,IAGd,MAAMM,GAAmB,QAAOD,EAAQpJ,KAAMuD,EAAYrF,OAC1D,OAAO0K,GAAS,CACd1I,OACAyI,eAAgBU,EAChBZ,SAAUM,GAEd,CACA,MAAMvD,EAAOQ,EAAkBA,EAAkBG,OAAS,GAC1D,GAAIX,EAAK9D,WAAW/E,KAAOwF,EAAUT,WAAW/E,GAC9C,OAAOoM,EAAcd,UAAUD,OAEjC,GAAInB,GAAsBrB,EAAK9D,WAAW/E,GAAImK,GAAgB,CAC5D,MAAM9G,GAAO,QAAOwF,EAAKxF,KAAM/B,EAAO6I,EAAcvD,YAAYrF,QAChE,OAAOwK,GAAQ,CACbxI,OACAyI,eAAgB3I,EAChByI,SAAUM,GAEd,CACA,OAAOL,GAAQ,CACbxI,OACAyI,eAAgBnD,EAAKxF,KACrByI,SAAUM,GACV,EAGAjE,GAA4B,CAAC/D,EAAW7C,KAC1C,MAAMqB,EAAQwB,EAAUxB,MACxB,OAAKA,EAGE3B,EAAIM,EAAOqB,EAAMa,OAAOC,KAAKC,cAF3BpC,CAEwC,EAiCnD,IAAIoL,GAAmCzO,IACrC,MAAM0O,EA/BsC,GAC5C/G,SACAL,YACApB,YACAa,aACAkF,oBAEA,MAAM0C,EAAWrH,EAAUnC,KAAKiI,UAAUD,OACpCvF,EAAKD,EAAOC,GAClB,OAAK1B,GAGA0B,EAGW,YAAZA,EAAG5J,KACEiQ,GAAe,CACpBtG,SACAL,YACAP,aACAb,YACA+F,kBAGGgB,GAAc,CACnBtF,SACAZ,aACAkF,kBAjBO0C,CAkBP,EAG0BC,CAAsC5O,GAC5DkG,EAAYlG,EAAKkG,UAEvB,OADyBA,EAAY+D,GAA0B/D,EAAWwI,GAAuBA,CAC1E,EAGrBG,GAAiB,CAAC7E,EAAU7D,KAC9B,MAAMX,EAAOtC,EAASiD,EAAW6D,EAASzE,OAAOe,SAC3Cb,EAAerC,EAAOoC,GAmB5B,MAZgB,CACdd,OAPY,OAAQ,CACpBN,IAAK+B,EAAUrD,EACfwB,OAAQ6B,EAAUrD,EAAIkH,EAAStF,MAAMK,OACrCV,KAAM8B,EAAUtD,EAChB0B,MAAO4B,EAAUtD,EAAImH,EAAStF,MAAMI,QAIpCS,OAAQ,CACNe,QAAS0D,EAASzE,OAAOe,QACzBzB,IAAKmF,EAASzE,OAAOV,IACrB2B,QAASL,EACTX,KAAM,CACJhC,MAAOgC,EACPC,iBAIQ,EAGhB,SAASqJ,GAAgBC,EAAKhI,GAC5B,OAAOgI,EAAItQ,KAAIqD,GAAMiF,EAAWjF,IAClC,CAUA,IAkDIkN,GAAmC,EACrCC,sBACA3H,YACA0C,eAEA,MAAMkF,EAPuB,EAAClF,EAAU3G,IAAUN,EAAIiH,EAASzE,OAAOC,KAAKC,aAAcpC,GAOzD8L,CAAyBnF,EAAUiF,GAC7DG,EAASlM,EAASgM,EAAyB5H,EAAUnC,KAAKiI,UAAUD,QAC1E,OAAOpK,EAAIuE,EAAU+H,OAAOjC,UAAUD,OAAQiC,EAAO,EAGnDE,GAAgC,EAClChI,YACA9F,cACA+N,yBACAvF,WACAC,4BACAuF,kBAAiB,MAEjB,MAAMC,EAAevM,EAASqM,EAAwBjI,EAAUnC,KAAKiI,UAAUD,QAEzEnN,EAAO,CACX8D,OAFcI,EAAiBoD,EAAUnC,KAAKiI,UAAWqC,GAGzDjO,cACAyI,4BACAD,YAEF,OAAOwF,EAxgBsBxP,KAAQ8J,UAAY,IAC9C9J,EACHkK,yBA1CuC7E,EA0CqBrF,EAAKwB,YAAY6D,KA1C9BX,IAC/C,MAAMqE,EAAmBJ,EAASjE,EAAMN,IAAKM,EAAMJ,QAC7C0E,EAAqBL,EAASjE,EAAML,KAAMK,EAAMH,OACtD,OAAOK,GACDS,IAASiE,GACJP,EAAiBnE,EAAQR,MAAQ2E,EAAiBnE,EAAQN,QAE5D0E,EAAmBpE,EAAQP,OAAS2E,EAAmBpE,EAAQL,MACvE,KARsCc,KA2CvC,EAqgBwBqK,CAAuB1P,GAAQuK,GAAiBvK,EAAK,EAG3E2P,GAAkB,EACpBtD,kBACA/E,YACA9F,cACAuF,aACAuF,iBACAtC,WACA4F,8BACAC,0BACA5D,oBAEA,IAAKzK,EAAYsO,UACf,OAAO,KAET,MAAM3E,EAAoB/D,EAA6B5F,EAAYqF,WAAW/E,GAAIiF,GAC5EtF,EAAeyG,EAASZ,EAAW9F,GACnCmG,EAjsBgB,GACtB0E,kBACA/E,YACA9F,cACA2J,oBACAmB,qBAEA,IAAK9K,EAAYqL,iBACf,OAAO,KAGT,IADiBnF,EAAkB4E,GAEjC,OAAO,KAET,SAASyD,EAAUjM,GACjB,MAAM8D,EAAK,CACT5J,KAAM,UACN+D,QAAS,CACPC,YAAa8B,EACbpC,YAAaF,EAAYqF,WAAW/E,KAGxC,MAAO,IACFwK,EACH1E,KAEJ,CACA,MAAMW,EAAM+D,EAAe7D,UAAUF,IAC/ByH,EAAYzH,EAAI+C,OAAS/C,EAAI,GAAK,KACxC,GAAI8D,EACF,OAAO2D,EAAYD,EAAUC,GAAa,KAE5C,MAAMC,EAAmBnI,EAAwBR,EAAW6D,GAC5D,IAAK6E,EACH,OAAKC,EAAiB3E,OAIfyE,EADME,EAAiBA,EAAiB3E,OAAS,GAClCzE,WAAW/E,IAHxB,KAKX,MAAMoO,EAAiBD,EAAiBE,WAAUC,GAAKA,EAAEvJ,WAAW/E,KAAOkO,KACrD,IAApBE,GAA4HzQ,IAC9H,MAAMgN,EAAgByD,EAAiB,EACvC,OAAIzD,EAAgB,EACX,KAGFsD,EADQE,EAAiBxD,GACR5F,WAAW/E,GAAG,EAkpBvBuO,CAAkB,CAC/BhE,kBACA/E,YACA9F,cACA2J,oBACAmB,oBACIF,GAAgB,CACpBC,kBACA5K,eACA6F,YACAP,aACAvF,cACA2J,oBACAmB,iBACAtC,WACAiC,kBAEF,IAAKtE,EACH,OAAO,KAET,MAAMsH,EAAsBR,GAAiC,CAC3D9G,SACAL,YACApB,UAAW1E,EACXuF,aACAkF,kBAUF,GAR+BqD,GAA8B,CAC3DhI,YACA9F,cACA+N,uBAAwBN,EACxBjF,SAAUA,EAAStF,MACnBuF,2BAA2B,EAC3BuF,gBAAgB,IAEU,CAM1B,MAAO,CACLc,gBANsBtB,GAAiC,CACvDC,sBACA3H,YACA0C,aAIArC,SACA4I,kBAAmB,KAEvB,CACA,MAAM7M,EAAWR,EAAS+L,EAAqBW,GACzCY,EA/IoB,GAC1B7I,SACAqC,WACAxI,cACAuF,aACA0J,sBAEA,MAAMC,EAAmB7B,GAAe7E,EAAUjH,EAAIiH,EAASzE,OAAOiB,QAASiK,IACzEE,EAAoBnP,EAAYkD,MAAQuB,EAAgBzE,EAAauB,EAAIvB,EAAYkD,MAAMa,OAAOiB,QAASiK,IAAoBjP,EAC/HmJ,EAAOhD,EAAOc,UACdmI,EAAqBpG,GAAsB,CAC/CC,cAAeqE,GAAgBnE,EAAKpC,IAAKxB,GACzCvF,cACAkH,YAAaf,EAAOe,YACpBsB,SAAU0G,EAAiBhM,MAC3BiG,OACAD,oBAAoB,IAEhBmG,EAAsBrG,GAAsB,CAChDC,cAAeqE,GAAgBnE,EAAKpC,IAAKxB,GACzCvF,YAAamP,EACbjI,YAAaf,EAAOe,YACpBsB,SAAUA,EAAStF,MACnBiG,OACAD,oBAAoB,IAEhBrC,EAAY,CAAC,EACbC,EAAU,CAAC,EACXsC,EAAS,CAACD,EAAMiG,EAAoBC,GAiB1C,OAhBAlG,EAAKpC,IAAIpJ,SAAQ2C,IACf,MAAM2D,EAvCV,SAAuB3D,EAAI8I,GACzB,IAAK,IAAIkG,EAAI,EAAGA,EAAIlG,EAAOU,OAAQwF,IAAK,CACtC,MAAMrL,EAAemF,EAAOkG,GAAGxI,QAAQxG,GACvC,GAAI2D,EACF,OAAOA,CAEX,CACA,OAAO,IACT,CA+ByBsL,CAAcjP,EAAI8I,GACnCnF,EACF6C,EAAQxG,GAAM2D,EAGhB4C,EAAUvG,IAAM,CAAI,IAEJ,IACb6F,EACHc,UAAW,CACTF,IAAKoC,EAAKpC,IACVF,YACAC,WAGY,EAkGC0I,CAAsB,CACrCrJ,SACAqC,WACAxI,cACAuF,aACA0J,gBAAiB/M,IAEnB,MAAO,CACL4M,gBAAiBT,EACjBlI,OAAQ6I,EACRD,kBAAmB7M,EACpB,EAGH,MAAMuN,GAAiB/K,IACrB,MAAMgL,EAAOhL,EAAUtB,QAAQoB,OAE/B,OADCkL,GAA4GzR,IACtGyR,CAAI,EA2Db,MAAMC,GAAgC,CAAC7J,EAAW2E,KAChD,MAAM0C,EAAWrH,EAAUnC,KAAKiI,UAAUD,OAC1C,OAAOnB,GAAsB1E,EAAUT,WAAW/E,GAAImK,GAAiB/I,EAASyL,EAAU1C,EAAcvD,YAAYrF,OAASsL,CAAQ,EAEjIyC,GAA0B,CAAC9J,EAAW2E,KAC1C,MAAM0C,EAAWrH,EAAUnC,KAAKiI,UAChC,OAAOpB,GAAsB1E,EAAUT,WAAW/E,GAAImK,GAAiB/H,EAAiByK,EAAUvL,EAAO6I,EAAcvD,YAAYrF,QAAUsL,CAAQ,EAGvJ,IA0BI0C,IAAiB,QAAW,SAAwBhM,EAAMgI,GAC5D,MAAM5H,EAAe4H,EAAWhI,EAAK9B,MACrC,MAAO,CACLC,MAAOiC,EACPpC,MAAOC,EAAM+B,EAAK9B,KAAMkC,GAE5B,IAEA,MAeM6L,GAAgB,CAAC5M,EAAOG,KAAQ,IACjCH,EACHa,OAAQ,IACHb,EAAMa,OACTV,SAGE0M,GAAiB,CAACrL,EAAWoB,EAAWP,KAC5C,MAAMrC,EAAQwB,EAAUxB,MACtBwD,EAASZ,EAAWpB,IAAyHzG,IAC7IyG,EAAUtB,QAAQQ,iBAAiJ3F,IACrK,MAAM+R,EAAkBH,GAAenL,EAAUb,KAAMiC,EAAU+F,YAAYhK,MACvEoO,EA3BgC,EAACvL,EAAWsL,EAAiBzK,KACnE,MAAM1B,EAAOa,EAAUb,KACvB,GAAkC,YAA9Ba,EAAUW,WAAW6K,KACvB,OAAOpO,EAAM+B,EAAK9B,KAAMiO,EAAgBnM,EAAK9B,OAE/C,MAAMoO,EAAiBzL,EAAUtB,QAAQO,KAAKiJ,WAAW/I,EAAKoE,MAIxDmI,EAHkBxK,EAA6BlB,EAAUW,WAAW/E,GAAIiF,GAC5CJ,QAAO,CAACkL,EAAKC,IAAcD,EAAMC,EAAUzC,OAAO1J,UAAUN,EAAKoE,OAAO,GACxE+H,EAAgBnM,EAAK9B,MACjBoO,EACtC,OAAIC,GAAiB,EACZ,KAEFtO,EAAM+B,EAAK9B,KAAMqO,EAAc,EAcfG,CAAgC7L,EAAWsL,EAAiBzK,GAC7EiL,EAAQ,CACZR,kBACA3L,YAAa4L,EACbQ,kBAAmB/L,EAAUxB,MAAQwB,EAAUxB,MAAMa,OAAOV,IAAM,MAEpE,IAAKH,EAAO,CACV,MAAME,EAAUM,EAAW,CACzBC,KAAMe,EAAUtB,QAAQO,KACxBC,gBAAiB4M,EACjB3M,KAAMa,EAAUb,KAChBX,MAAOwB,EAAUxB,QAEnB,MAAO,IACFwB,EACHtB,UAEJ,CACA,MAAMsN,EAAYT,EAAiB1O,EAAI2B,EAAMa,OAAOV,IAAK4M,GAAkB/M,EAAMa,OAAOV,IAClFsN,EAAWb,GAAc5M,EAAOwN,GAChCtN,EAAUM,EAAW,CACzBC,KAAMe,EAAUtB,QAAQO,KACxBC,gBAAiB4M,EACjB3M,KAAMa,EAAUb,KAChBX,MAAOyN,IAET,MAAO,IACFjM,EACHtB,UACAF,MAAOyN,EACR,EAkCH,IAkEIC,GAAgB,EAClB/F,kBACAuD,8BACAtI,YACA+K,SACAtL,aACAL,aACAsD,WACAiC,oBAEA,MAAMzK,EA3QwB,GAC9B6K,kBACA4C,sBACA1N,SACAmF,aACAsD,eAEA,MAAMhE,EAASzE,EAAOqD,QAAQoB,OAC9B,IAAKA,EACH,OAAO,KAET,MAAMX,EAAO9D,EAAO8D,KACdiN,EAAyB3J,EAAS3C,EAAOX,EAAKhD,OAAQ2D,EAAOX,EAAKS,MAClEyM,EAAavL,EAAgBN,GAAYW,QAAOnB,GAAaA,IAAc3E,IAAQ8F,QAAOnB,GAAaA,EAAU4J,YAAWzI,QAAOnB,GAAagG,QAAQhG,EAAUtB,QAAQoB,UAASqB,QAAOnB,GAAa4C,EAA+BkB,EAAStF,MAAxCoE,CAA+CmI,GAAe/K,MAAamB,QAAOnB,IAC7R,MAAMsM,EAAiBvB,GAAe/K,GACtC,OAAImG,EACKrG,EAAOX,EAAKsE,cAAgB6I,EAAenN,EAAKsE,cAElD6I,EAAenN,EAAKqE,gBAAkB1D,EAAOX,EAAKqE,eAAe,IACvErC,QAAOnB,IACR,MAAMsM,EAAiBvB,GAAe/K,GAChCuM,EAA8B9J,EAAS6J,EAAenN,EAAKhD,OAAQmQ,EAAenN,EAAKS,MAC7F,OAAOwM,EAAuBE,EAAenN,EAAKhD,SAAWiQ,EAAuBE,EAAenN,EAAKS,OAAS2M,EAA4BzM,EAAOX,EAAKhD,SAAWoQ,EAA4BzM,EAAOX,EAAKS,KAAK,IAChNyB,MAAK,CAACC,EAAGC,KACV,MAAMiL,EAAQzB,GAAezJ,GAAGnC,EAAKqE,gBAC/BiJ,EAAS1B,GAAexJ,GAAGpC,EAAKqE,gBACtC,OAAI2C,EACKqG,EAAQC,EAEVA,EAASD,CAAK,IACpBrL,QAAO,CAACnB,EAAW7E,EAAOuR,IAAU3B,GAAe/K,GAAWb,EAAKqE,kBAAoBuH,GAAe2B,EAAM,IAAIvN,EAAKqE,kBACxH,IAAK6I,EAAWjH,OACd,OAAO,KAET,GAA0B,IAAtBiH,EAAWjH,OACb,OAAOiH,EAAW,GAEpB,MAAMM,EAAWN,EAAWlL,QAAOnB,GACPyC,EAASsI,GAAe/K,GAAWb,EAAKhD,OAAQ4O,GAAe/K,GAAWb,EAAKS,KAClGgN,CAAkB7D,EAAoB5J,EAAK9B,SAEpD,OAAwB,IAApBsP,EAASvH,OACJuH,EAAS,GAEdA,EAASvH,OAAS,EACbuH,EAAStL,MAAK,CAACC,EAAGC,IAAMwJ,GAAezJ,GAAGnC,EAAKhD,OAAS4O,GAAexJ,GAAGpC,EAAKhD,SAAQ,GAEzFkQ,EAAWhL,MAAK,CAACC,EAAGC,KACzB,MAAMiL,EAAQ7O,EAAUoL,EAAqBzK,EAAWyM,GAAezJ,KACjEmL,EAAS9O,EAAUoL,EAAqBzK,EAAWyM,GAAexJ,KACxE,OAAIiL,IAAUC,EACLD,EAAQC,EAEV1B,GAAezJ,GAAGnC,EAAKhD,OAAS4O,GAAexJ,GAAGpC,EAAKhD,MAAM,IACnE,EAAE,EAqNe0Q,CAA0B,CAC5C1G,kBACA4C,oBAAqBW,EACrBrO,OAAQ8Q,EACR3L,aACAsD,aAEF,IAAKxI,EACH,OAAO,KAET,MAAM2J,EAAoB/D,EAA6B5F,EAAYqF,WAAW/E,GAAIiF,GAC5E+G,EApNkB,GACxBmB,sBACAjF,WACAxI,cACA2J,oBACAc,oBAEA,MAAM+G,EAAS7H,EAAkB9D,QAAOC,GAAaiD,GAAiB,CACpEzG,OAAQsN,GAAwB9J,EAAW2E,GAC3CzK,cACAwI,SAAUA,EAAStF,MACnBuF,2BAA2B,MACzB1C,MAAK,CAACC,EAAGC,KACX,MAAMwL,EAAcvP,EAASuL,EAAqBhF,GAA0BzI,EAAa2P,GAA8B3J,EAAGyE,KACpHiH,EAAcxP,EAASuL,EAAqBhF,GAA0BzI,EAAa2P,GAA8B1J,EAAGwE,KAC1H,OAAIgH,EAAcC,GACR,EAENA,EAAcD,EACT,EAEFzL,EAAEX,WAAWxF,MAAQoG,EAAEZ,WAAWxF,KAAK,IAEhD,OAAO2R,EAAO,IAAM,IAAI,EA6LDG,CAAoB,CACzClE,oBAAqBW,EACrB5F,WACAxI,cACA2J,oBACAc,kBAEItE,EA9FiB,GACvBiI,8BACA9B,iBACA3C,oBACA7D,YACAP,aACAvF,cACAwI,WACAiC,oBAEA,IAAK6B,EAAgB,CACnB,GAAI3C,EAAkBG,OACpB,OAAO,KAET,MAAM8H,EAAW,CACf3K,UAAWL,EACXM,YAAaP,EACbP,GAAI,CACF5J,KAAM,UACNwD,YAAa,CACXE,YAAaF,EAAYqF,WAAW/E,GACpCT,MAAO,KAIPgS,EAA8B5E,GAAiC,CACnE9G,OAAQyL,EACR9L,YACApB,UAAW1E,EACXuF,aACAkF,kBAEI7G,EAAkB8C,EAASZ,EAAW9F,GAAeA,EAAc+P,GAAe/P,EAAa8F,EAAWP,GAShH,OAR+BuI,GAA8B,CAC3DhI,YACA9F,YAAa4D,EACbmK,uBAAwB8D,EACxBrJ,SAAUA,EAAStF,MACnBuF,2BAA2B,EAC3BuF,gBAAgB,IAEc4D,EAAW,IAC7C,CACA,MAAME,EAAsBpH,QAAQ0D,EAA4BpO,EAAY6D,KAAK9B,OAASuK,EAAe3I,KAAKiI,UAAUD,OAAO3L,EAAY6D,KAAK9B,OAC1IkJ,EAAgB,MACpB,MAAM8G,EAAazF,EAAejH,WAAWxF,MAC7C,OAAIyM,EAAejH,WAAW/E,KAAOwF,EAAUT,WAAW/E,IAGtDwR,EAFKC,EAKFA,EAAa,CACrB,EATqB,GAUhB7K,EAAc2I,GAAe7P,EAAY6D,KAAMiC,EAAU+F,YAC/D,OAAO5B,GAAuB,CAC5BnE,YACA6D,oBACA3J,cACAwI,WACAtB,cACAiC,KAAMvC,EACN/G,MAAOoL,GACP,EA+Ba+G,CAAmB,CAChC5D,8BACApO,cACA8F,YACAP,aACA+G,iBACA3C,oBACAnB,WACAiC,kBAEF,IAAKtE,EACH,OAAO,KAET,MAAMsH,EAAsBR,GAAiC,CAC3D9G,SACAL,YACApB,UAAW1E,EACXuF,aACAkF,kBAOF,MAAO,CACLqE,gBANsBtB,GAAiC,CACvDC,sBACA3H,YACA0C,aAIArC,SACA4I,kBAAmB,KACpB,EAGCkD,GAAoB9L,IACtB,MAAMC,EAAKD,EAAOC,GAClB,OAAKA,EAGW,YAAZA,EAAG5J,KACE4J,EAAGpG,YAAYE,YAEjBkG,EAAG7F,QAAQL,YALT,IAKoB,EAO/B,IAAIgS,GAAkB,EACpBC,QACA3V,WAEA,MAAM4V,EARmB,EAACjM,EAAQjB,KAClC,MAAM5E,EAAK2R,GAAkB9L,GAC7B,OAAO7F,EAAK4E,EAAW5E,GAAM,IAAI,EAMV+R,CAAmBF,EAAMhM,OAAQgM,EAAMG,WAAWpN,YACnEqN,EAA4B7H,QAAQ0H,GACpCI,EAAOL,EAAMG,WAAWpN,WAAWiN,EAAMM,SAAS/N,UAAUpE,IAC5DuQ,EAASuB,GAAkBI,EAC3BzK,EAAY8I,EAAOhN,KAAKkE,UACxB2K,EAAmC,aAAd3K,IAAsC,YAATvL,GAA+B,cAATA,IAAuC,eAAduL,IAAwC,cAATvL,GAAiC,eAATA,GAC9J,GAAIkW,IAAuBH,EACzB,OAAO,KAET,MAAM1H,EAA2B,cAATrO,GAAiC,eAATA,EAC1CsJ,EAAYqM,EAAMG,WAAW/M,WAAW4M,EAAMM,SAAS3M,UAAUxF,IACjE8N,EAA8B+D,EAAMnN,QAAQrB,KAAKgP,iBACjD,WACJpN,EAAU,WACVL,GACEiN,EAAMG,WACV,OAAOI,EAAqBvE,GAAgB,CAC1CtD,kBACAuD,8BACAtI,YACA9F,YAAa6Q,EACbtL,aACAiD,SAAU2J,EAAM3J,SAChB6F,wBAAyB8D,EAAMnN,QAAQ6I,OAAO+E,UAC9C9H,eAAgBqH,EAAMhM,OACtBsE,cAAe0H,EAAM1H,gBAClBmG,GAAc,CACjB/F,kBACAuD,8BACAtI,YACA+K,SACAtL,aACAL,aACAsD,SAAU2J,EAAM3J,SAChBiC,cAAe0H,EAAM1H,eACrB,EAGJ,SAASoI,GAAkBV,GACzB,MAAuB,aAAhBA,EAAMW,OAAwC,eAAhBX,EAAMW,KAC7C,CAEA,SAASC,GAAkB7P,GACzB,MAAMqE,EAAmBJ,EAASjE,EAAMN,IAAKM,EAAMJ,QAC7C0E,EAAqBL,EAASjE,EAAML,KAAMK,EAAMH,OACtD,OAAO,SAAalB,GAClB,OAAO0F,EAAiB1F,EAAMP,IAAMkG,EAAmB3F,EAAMR,EAC/D,CACF,CAqBA,SAAS2R,IAAiB,cACxBC,EAAa,UACbnN,EAAS,WACTZ,IAEA,MAAM6L,EAAavL,EAAgBN,GAAYW,QAAOY,IACpD,IAAKA,EAAK6H,UACR,OAAO,EAET,MAAM9J,EAASiC,EAAKrD,QAAQoB,OAC5B,IAAKA,EACH,OAAO,EAET,GAhC0B2M,EAgCQ3M,KAhCf0M,EAgCA+B,GA/BRpQ,KAAOsO,EAAOpO,OAASmO,EAAMnO,MAAQoO,EAAOtO,MAAQqO,EAAMtO,IAAMuO,EAAOrO,QAAUoO,EAAMpO,OAASqO,EAAOvO,KAgChH,OAAO,EAjCb,IAAuBsO,EAAOC,EAmC1B,GAAI4B,GAAkBvO,EAAlBuO,CAA0BE,EAActH,QAC1C,OAAO,EAET,MAAM9H,EAAO4C,EAAK5C,KACZqP,EAAc1O,EAAOmH,OAAO9H,EAAKmE,eACjCE,EAAiB+K,EAAcpP,EAAKqE,gBACpCC,EAAe8K,EAAcpP,EAAKsE,cAClCgL,EAAchM,EAAS3C,EAAOX,EAAKqE,gBAAiB1D,EAAOX,EAAKsE,eAChEiL,EAAmBD,EAAYjL,GAC/BmL,EAAiBF,EAAYhL,GACnC,OAAKiL,IAAqBC,IAGtBD,EACKlL,EAAiBgL,EAEnB/K,EAAe+K,EAAW,IAEnC,OAAKnC,EAAWjH,OAGU,IAAtBiH,EAAWjH,OACNiH,EAAW,GAAG1L,WAAW/E,GAtDpC,UAAyB,cACvB2S,EAAa,UACbnN,EAAS,WACTiL,IAEA,MAAMuC,EAAcxN,EAAUnC,KAAKiI,UAAUD,OACvC6F,EAAST,EAAW9T,KAAIsW,IAC5B,MAAM1P,EAAO0P,EAAU1P,KACjBvB,EAASR,EAAMyR,EAAU1P,KAAK9B,KAAMkR,EAActH,OAAO9H,EAAK9B,MAAOwR,EAAU5P,KAAKiI,UAAUD,OAAO9H,EAAKmE,gBAChH,MAAO,CACL1H,GAAIiT,EAAUlO,WAAW/E,GACzB4B,SAAUA,EAASoR,EAAahR,GACjC,IACAyD,MAAK,CAACC,EAAGC,IAAMA,EAAE/D,SAAW8D,EAAE9D,WACjC,OAAOsP,EAAO,GAAKA,EAAO,GAAGlR,GAAK,IACpC,CAyCSkT,CAAgB,CACrBP,gBACAnN,YACAiL,eARO,IAUX,CAEA,MAAM0C,GAAuB,CAAC/D,EAAM7N,KAAU,OAAQa,EAAiBgN,EAAM7N,IAU7E,SAAS6R,IAAe,UACtBzM,EAAS,GACT3G,IAEA,OAAOoK,QAAQzD,EAAUH,QAAQxG,IAAO2G,EAAUJ,UAAUvG,GAC9D,CAqEA,IAuDIqT,GAAgB,EAClBC,aACA9N,YACAP,aACAL,aACA4F,iBACAtC,WACAiC,oBAEA,MAAMwI,EAAgBQ,GAAqB3N,EAAUnC,KAAKiI,UAAWgI,GAC/DC,EAAgBb,GAAiB,CACrCC,gBACAnN,YACAZ,eAEF,IAAK2O,EACH,OAAO7M,EAET,MAAMhH,EAAckF,EAAW2O,GACzBlK,EAAoB/D,EAA6B5F,EAAYqF,WAAW/E,GAAIiF,GAC5EuO,EA7JkB,EAACpP,EAAWqP,KACpC,MAAM7Q,EAAQwB,EAAUxB,MACxB,OAAKA,EAGEuQ,GAAqBM,EAAM7Q,EAAMa,OAAOC,KAAKhC,OAF3C+R,CAEiD,EAwJjB1E,CAAoBrP,EAAaiT,GAC1E,MA5EqB,GACrBnN,YACAgO,iCAAkCE,EAClClJ,iBACA9K,cACA2J,oBACAc,oBAEA,IAAKzK,EAAYqL,iBACf,OAAO,KAET,MAAMxH,EAAO7D,EAAY6D,KACnBqD,EAAc2I,GAAe7P,EAAY6D,KAAMiC,EAAU+F,YACzD5H,EAAeiD,EAAYlF,MAC3BiS,EAAcD,EAAWnQ,EAAKhD,OAC9BqT,EAAYF,EAAWnQ,EAAKS,KAE5BoH,EADkBpF,EAAwBR,EAAW6D,GACvBQ,MAAKgK,IACvC,MAAM7T,EAAK6T,EAAM9O,WAAW/E,GACtB8T,EAAYD,EAAMxQ,KAAKiI,UAEvByI,EADYD,EAAUvQ,EAAKoE,MArBL,EAuBtBqM,EAA0B9J,GAAsBlK,EAAImK,GACpDqB,EAAc4H,GAAe,CACjCzM,UAAW6D,EAAe7D,UAC1B3G,OAEF,OAAIgU,EACExI,EACKoI,EAAYE,EAAUvQ,EAAKhD,OAASwT,GAAaH,EAAYE,EAAUvQ,EAAKS,KAAO+P,EAErFJ,EAAcG,EAAUvQ,EAAKhD,OAASoD,EAAeoQ,GAAaJ,EAAcG,EAAUvQ,EAAKS,KAAOL,EAAeoQ,EAE1HvI,EACKoI,EAAYE,EAAUvQ,EAAKhD,OAASoD,EAAeoQ,GAAaH,EAAYE,EAAUvQ,EAAKS,KAAOL,EAAeoQ,EAEnHJ,EAAcG,EAAUvQ,EAAKhD,OAASwT,GAAaJ,EAAcG,EAAUvQ,EAAKS,KAAO+P,CAAS,IAEzG,OAAK3I,EAGU,CACbxE,cACAD,UAAW6D,EAAe7D,UAC1Bb,GAAI,CACF5J,KAAM,UACN+D,QAAS,CACPC,YAAakL,EAAYrG,WAAW/E,GACpCJ,YAAaF,EAAYqF,WAAW/E,MATjC,IAaI,EAwBNiU,CAAiB,CACtBT,mCACAhO,YACAgF,iBACA9K,cACA2J,oBACAc,mBArImB,GACrBqJ,iCAAkCE,EAClClO,YACA9F,cACA2J,oBACAR,OACAX,WACAiC,oBAEA,MAAM5G,EAAO7D,EAAY6D,KACnBqD,EAAc2I,GAAe7P,EAAY6D,KAAMiC,EAAU+F,YACzD5H,EAAeiD,EAAYlF,MAC3BiS,EAAcD,EAAWnQ,EAAKhD,OAC9BqT,EAAYF,EAAWnQ,EAAKS,KAqB5BuF,EAlDR,UAAiB,UACf/D,EAAS,QACTiH,EAAO,WACPnD,IAEA,OAAKmD,EAGAnD,GAGDmD,EAAQ1H,WAAWxF,MAAQiG,EAAUT,WAAWxF,MAC3CkN,EAAQ1H,WAAWxF,MAAQ,EAH3BkN,EAAQ1H,WAAWxF,MAHnB,IASX,CAmCmB2U,CAAQ,CACvB1O,YACAiH,QAtBsBzG,EAAwBR,EAAW6D,GAC3BQ,MAAKgK,IACnC,MAAM7T,EAAK6T,EAAM9O,WAAW/E,GACtB4S,EAAciB,EAAMxQ,KAAKiI,UAAUD,OAAO9H,EAAK9B,MAC/CuS,EAA0B9J,GAAsBlK,EAAImK,GACpDqB,EAAc4H,GAAe,CACjCzM,UAAWkC,EACX7I,OAEF,OAAIgU,EACExI,EACKoI,GAAahB,EAEfe,EAAcf,EAAcjP,EAEjC6H,EACKoI,GAAahB,EAAcjP,EAE7BgQ,EAAcf,CAAW,KAC5B,KAIJtJ,WAAYlD,EAASZ,EAAW9F,KAElC,OAAOiK,GAAuB,CAC5BnE,YACA6D,oBACA3J,cACAwI,WACAW,OACAjC,cACArH,MAAOgK,GACP,EAuFI4K,CAAiB,CACrBX,mCACAhO,YACA9F,cACA2J,oBACAR,KAAM2B,EAAe7D,UACrBuB,WACAiC,iBACA,EAGAiK,GAAoB,CAACxP,EAAYyP,KAAY,IAC5CzP,EACH,CAACyP,EAAQtP,WAAW/E,IAAKqU,IAG3B,MAAMC,GAAyB,EAC7B9J,iBACA3E,SACAjB,iBAEA,MAAMiE,EAAO8I,GAAkBnH,GACzB+J,EAAM5C,GAAkB9L,GAC9B,IAAKgD,EACH,OAAOjE,EAET,GAAIiE,IAAS0L,EACX,OAAO3P,EAET,MAAM4P,EAAgB5P,EAAWiE,GACjC,IAAK2L,EAAc1R,QAAQQ,gBACzB,OAAOsB,EAET,MAAMyP,EA7ekBjQ,KACxB,MAAM8L,EAAQ9L,EAAUtB,QAAQQ,gBAC/B4M,GAAiIvS,IAClI,MAAMiF,EAAQwB,EAAUxB,MACxB,IAAKA,EAAO,CACV,MAAME,EAAUM,EAAW,CACzBC,KAAMe,EAAUtB,QAAQO,KACxBE,KAAMa,EAAUb,KAChBX,MAAO,KACPU,gBAAiB,OAEnB,MAAO,IACFc,EACHtB,UAEJ,CACA,MAAM2R,EAAevE,EAAMC,kBAC1BsE,GAAkK9W,IACnK,MAAM0S,EAAWb,GAAc5M,EAAO6R,GAChC3R,EAAUM,EAAW,CACzBC,KAAMe,EAAUtB,QAAQO,KACxBE,KAAMa,EAAUb,KAChBX,MAAOyN,EACP/M,gBAAiB,OAEnB,MAAO,IACFc,EACHtB,UACAF,MAAOyN,EACR,EAgdeqE,CAAkBF,GAClC,OAAOJ,GAAkBxP,EAAYyP,EAAQ,EAE/C,IA2BI5T,GAAS,EACXoR,QACArD,gBAAiBmG,EACjB3C,WAAY4C,EACZ1M,SAAU2M,EACVhP,OAAQiP,EACRrG,wBAEA,MAAMvG,EAAW2M,GAAkBhD,EAAM3J,SACnC8J,EAAa4C,GAAoB/C,EAAMG,WACvCxD,EAAkBmG,GAAyB9C,EAAMnN,QAAQ6I,OAAO+E,UAChEhF,EAASlM,EAASoN,EAAiBqD,EAAMrN,QAAQ+I,OAAO+E,WACxD/E,EAAS,CACbD,SACAgF,UAAW9D,EACX6D,gBAAiBpR,EAAI4Q,EAAMrN,QAAQ+I,OAAO8E,gBAAiB/E,IAEvDjK,EAAO,CACXiP,UAAWrR,EAAIsM,EAAO+E,UAAWpK,EAASzE,OAAOiB,SACjD2N,gBAAiBpR,EAAIsM,EAAO8E,gBAAiBnK,EAASzE,OAAOiB,SAC7D4I,OAAQrM,EAAIsM,EAAOD,OAAQpF,EAASzE,OAAOC,KAAKhC,QAE5CgD,EAAU,CACd6I,SACAlK,QAEF,GAAoB,eAAhBwO,EAAMW,MACR,MAAO,IACFX,EACHG,aACA9J,WACAxD,WAGJ,MAAMc,EAAYwM,EAAW/M,WAAW4M,EAAMM,SAAS3M,UAAUxF,IAC3D+U,EAAYD,GAAgBzB,GAAc,CAC9CC,WAAYjQ,EAAKiK,OACjB9H,YACAP,WAAY+M,EAAW/M,WACvBL,WAAYoN,EAAWpN,WACvB4F,eAAgBqH,EAAMhM,OACtBqC,WACAiC,cAAe0H,EAAM1H,gBAEjB6K,EAvEoB,GAC1BxP,YACAP,aACAL,aACA4F,iBACA3E,aAEA,MAAMoP,EAAUX,GAAuB,CACrC9J,iBACA3E,SACAjB,eAEI2L,EAASoB,GAAkB9L,GACjC,IAAK0K,EACH,OAAO0E,EAET,MAAM7Q,EAAYQ,EAAW2L,GAC7B,GAAInK,EAASZ,EAAWpB,GACtB,OAAO6Q,EAET,GAAI7Q,EAAUtB,QAAQQ,gBACpB,OAAO2R,EAET,MAAMC,EAAUzF,GAAerL,EAAWoB,EAAWP,GACrD,OAAOmP,GAAkBa,EAASC,EAAQ,EA+CVC,CAAsB,CACpD3P,YACAK,OAAQkP,EACRvK,eAAgBqH,EAAMhM,OACtBZ,WAAY+M,EAAW/M,WACvBL,WAAYoN,EAAWpN,aAczB,MAZe,IACViN,EACHnN,UACAsN,WAAY,CACV/M,WAAY+M,EAAW/M,WACvBL,WAAYoQ,GAEdnP,OAAQkP,EACR7M,WACAuG,kBAAmBA,GAAqB,KACxC7F,oBAAoB6F,GAA4B,KAErC,EAMf,IAAI2G,GAAY,EACdvP,SACAqC,WACAjD,aACAvF,cACAkJ,yBAEA,MAAMC,EAAOhD,EAAOc,UACdgC,EAXR,SAAuBsE,EAAKhI,GAC1B,OAAOgI,EAAItQ,KAAIqD,GAAMiF,EAAWjF,IAClC,CASwBqV,CAAcxM,EAAKpC,IAAKxB,GACxC0B,EAAY+B,GAAsB,CACtCC,gBACAjJ,cACAkH,YAAaf,EAAOe,YACpBsB,SAAUA,EAAStF,MACnBgG,qBACAC,SAEF,MAAO,IACFhD,EACHc,YACD,EAGC2O,GAA2B,EAC7BzP,SACAL,YACApB,YACAa,aACAiD,WACAiC,oBAEA,MAAMgD,EAAsBR,GAAiC,CAC3D9G,SACAL,YACAP,aACAb,YACA+F,kBAEF,OAAO+C,GAAiC,CACtCC,sBACA3H,YACA0C,YACA,EAGAqN,GAAc,EAChB1D,QACAG,WAAY4C,EACZ1M,SAAU2M,MAEe,SAAvBhD,EAAM2D,cAAiF7X,IACzF,MAAM8X,EAAuB5D,EAAMhM,OAC7BqC,EAAW2M,GAAkBhD,EAAM3J,SACnC8J,EAAa4C,GAAoB/C,EAAMG,YACvC,WACJ/M,EAAU,WACVL,GACEoN,EACExM,EAAYP,EAAW4M,EAAMM,SAAS3M,UAAUxF,IAChDuQ,EAASoB,GAAkB8D,GAChClF,GAAwH5S,IACzH,MAAM+B,EAAckF,EAAW2L,GACzB1K,EAASuP,GAAU,CACvBvP,OAAQ4P,EACRvN,WACAxI,cACAuF,eAEIuJ,EAAkB8G,GAAyB,CAC/CzP,SACAL,YACApB,UAAW1E,EACXuF,aACAiD,WACAiC,cAAe0H,EAAM1H,gBAEvB,OAAO1J,GAAO,CACZoF,SACA2I,kBACAqD,QACAG,aACA9J,YACA,EAQAwN,GAAgB,EAClBlQ,YACA0M,OACAjN,aACAiD,eAEA,MAAMtB,EAAc2I,GAAe2C,EAAK3O,KAAMiC,EAAU+F,YAClDoK,EAAarQ,EAA6B4M,EAAKnN,WAAW/E,GAAIiF,GAC9D2Q,EAAWD,EAAW3L,QAAQxE,IACpB,IAAdoQ,GAA2HjY,IAC7H,MAAMgL,EAAgBgN,EAAW1L,MAAM2L,EAAW,GAC5CvL,EAAW1B,EAAc9D,QAAO,CAACC,EAAUqB,KAC/CrB,EAASqB,EAAKpB,WAAW/E,KAAM,EACxB8E,IACN,CAAC,GACEqF,EAAgB,CACpB0L,cAAwC,YAAzB3D,EAAKnN,WAAW6K,KAC/BhJ,cACAyD,YAvBkBtF,MAyCpB,MAAO,CACLc,OATa,CACbc,UATgB+B,GAAsB,CACtCC,gBACAjJ,YAAawS,EACbtL,cACAiC,KAAM,KACNX,SAAUA,EAAStF,MACnBgG,oBAAoB,IAIpBhC,cACAd,GAAI,CACF5J,KAAM,UACNwD,aAtCgBqF,EAsCaS,EAAUT,WAtCT,CAClCxF,MAAOwF,EAAWxF,MAClBK,YAAamF,EAAWnF,gBAyCtBuK,gBACD,EAQH,MAAM5J,GAAQuV,IACR,CAAwC,EAMxCC,GAASD,IACT,CAAwC,EAO9C,IAyBIE,GAAkC,EACpCC,YACAC,oBACAhO,eAEA,MAAMiO,EAAqBjO,EAASzE,OAAOC,KAAKhC,MAChD,OAAOuU,EAAUtZ,KAAI6I,IACnB,MAAM5F,EAAc4F,EAAUT,WAAWnF,YAEnCgD,EAfKwB,KACb,MAAMxB,EAAQwB,EAAUxB,MAExB,OADCA,GAAyGjF,IACnGiF,CAAK,EAYIwT,CADGF,EAAkBtW,IAE7ByW,EAAwBzT,EAAMa,OAAOC,KAAKhC,MAE1C4U,EArCY,GACpB9Q,YACA8H,OAAQiJ,EACRC,0BAEA,MAAMjJ,GAAS,QAAO/H,EAAU+H,OAAQgJ,GAClClT,GAAO,QAAWkK,EAAQiJ,GAUhC,MATc,IACThR,EACHiR,YAAa,IACRjR,EAAUiR,YACblJ,UAEFA,SACAlK,OAEU,EAqBIqT,CAAgB,CAC5BlR,YACA8H,OAHkBrM,EAAIkV,EAAoBE,GAI1CG,oBAAqBtO,EAASzE,OAAOe,UAEvC,OAAO8R,CAAK,GACZ,EA+EJ,MAAMK,GAAa9E,GAAgC,SAAvBA,EAAM2D,aAC5BoB,GAAsB,CAAC/E,EAAOwC,EAASwC,KAC3C,MAAM7E,EAhJgB,EAACA,EAAYqC,KAAY,CAC/CpP,WAAY+M,EAAW/M,WACvBL,WAAYwP,GAAkBpC,EAAWpN,WAAYyP,KA8IlCyC,CAAkBjF,EAAMG,WAAYqC,GACvD,OAAKsC,GAAW9E,IAAUgF,EACjBpW,GAAO,CACZoR,QACAG,eAGGuD,GAAY,CACjB1D,QACAG,cACA,EAEJ,SAAS+E,GAAwBlF,GAC/B,OAAIA,EAAMrT,YAAqC,SAAvBqT,EAAM2D,aACrB,IACF3D,EACHpD,kBAAmB,MAGhBoD,CACT,CACA,MAAMmF,GAAS,CACbxE,MAAO,OACPyE,UAAW,KACXC,aAAa,GAEf,IAAIC,GAAU,CAACtF,EAAQmF,GAAQI,KAC7B,GAAoB,UAAhBA,EAAOlb,KACT,MAAO,IACF8a,GACHE,aAAa,GAGjB,GAAoB,oBAAhBE,EAAOlb,KAA4B,CACnB,SAAhB2V,EAAMW,OAA+H7U,IACvI,MAAM,SACJwU,EAAQ,gBACR3D,EAAe,SACftG,EAAQ,WACR8J,EAAU,aACVwD,GACE4B,EAAOC,QACL7R,EAAYwM,EAAW/M,WAAWkN,EAAS3M,UAAUxF,IACrDkS,EAAOF,EAAWpN,WAAWuN,EAAS/N,UAAUpE,IAChDuN,EAAS,CACb+E,UAAW9D,EACX6D,gBAAiB7M,EAAU+H,OAAOjC,UAAUD,OAC5CiC,OAAQxM,GAEJ0D,EAAU,CACd+I,SACAlK,KAAM,CACJiP,UAAWrR,EAAIsM,EAAO+E,UAAWpK,EAASzE,OAAOe,SACjD6N,gBAAiBpR,EAAIsM,EAAO+E,UAAWpK,EAASzE,OAAOe,SACvD8I,OAAQrM,EAAIsM,EAAO+E,UAAWpK,EAASzE,OAAOC,KAAKhC,SAGjD4V,EAAwBpS,EAAgB8M,EAAWpN,YAAY2S,OAAMpR,IAASA,EAAKqR,iBACnF,OACJ3R,EAAM,cACNsE,GACEuL,GAAc,CAChBlQ,YACA0M,OACAjN,WAAY+M,EAAW/M,WACvBiD,aAkBF,MAhBe,CACbsK,MAAO,WACPhU,YAAY,EACZ2T,WACAqD,eACAxD,aACAxN,UACAE,QAASF,EACT8S,wBACAzR,SACAsE,gBACAsN,aAAc5R,EACdqC,WACAuG,kBAAmB,KACnB7F,mBAAoB,KAGxB,CACA,GAAoB,wBAAhBwO,EAAOlb,KAAgC,CACzC,GAAoB,eAAhB2V,EAAMW,OAA0C,iBAAhBX,EAAMW,MACxC,OAAOX,EAES,aAAhBA,EAAMW,OAAwI7U,IAKhJ,MAJe,IACVkU,EACHW,MAAO,aAGX,CACA,GAAoB,2BAAhB4E,EAAOlb,KAET,MADkB,eAAhB2V,EAAMW,OAA0C,iBAAhBX,EAAMW,OAAqJ7U,IA9K7J,GAClCkU,QACA6F,gBAEAnX,KACA,MAAMoX,EAAmBD,EAAUE,SAASjb,KAAI8D,IAC9C,MAAMoX,EAAWhG,EAAMG,WAAWpN,WAAWnE,EAAOb,aAEpD,OADiBuE,EAAgB0T,EAAUpX,EAAOgD,OACnC,IAEXmB,EAAa,IACdiN,EAAMG,WAAWpN,cACjBD,EAAegT,IAEdG,EAAmB9S,EAAegR,GAAgC,CACtEC,UAAWyB,EAAUzB,UACrBC,kBAAmBtR,EACnBsD,SAAU2J,EAAM3J,YAEZjD,EAAa,IACd4M,EAAMG,WAAW/M,cACjB6S,GAELJ,EAAUK,SAAS1a,SAAQ2C,WAClBiF,EAAWjF,EAAG,IAEvB,MAAMgS,EAAa,CACjBpN,aACAK,cAEI+S,EAAYrG,GAAkBE,EAAMhM,QACpCoS,EAAUD,EAAYhG,EAAWpN,WAAWoT,GAAa,KACzDxS,EAAYwM,EAAW/M,WAAW4M,EAAMM,SAAS3M,UAAUxF,IAC3DkS,EAAOF,EAAWpN,WAAWiN,EAAMM,SAAS/N,UAAUpE,KAE1D6F,OAAQ4R,EAAY,cACpBtN,GACEuL,GAAc,CAChBlQ,YACA0M,OACAjN,aACAiD,SAAU2J,EAAM3J,WAEZsC,EAAiByN,GAAWA,EAAQlN,iBAAmB8G,EAAMhM,OAAS4R,EACtE5R,EAASwN,GAAc,CAC3BC,WAAYzB,EAAMnN,QAAQrB,KAAKiK,OAC/B9H,UAAWwM,EAAW/M,WAAW4M,EAAMM,SAAS3M,UAAUxF,IAC1DiF,WAAY+M,EAAW/M,WACvBL,WAAYoN,EAAWpN,WACvB4F,iBACAtC,SAAU2J,EAAM3J,SAChBiC,kBAEF4L,KACA,MAAMmC,EAAgB,IACjBrG,EACHW,MAAO,WACP3M,SACA4R,eACAzF,aACA7H,gBACAvB,oBAAoB,GAEtB,MAAoB,eAAhBiJ,EAAMW,MACD0F,EAEW,IACfA,EACH1F,MAAO,eACP3R,OAAQgR,EAAMhR,OACdsX,WAAW,EAEK,EAuGTC,CAA8B,CACnCvG,QACA6F,UAAWN,EAAOC,UAGtB,GAAoB,SAAhBD,EAAOlb,KAAiB,CAC1B,GAAoB,iBAAhB2V,EAAMW,MACR,OAAOX,EAERU,GAAkBV,IAA4HlU,IAC/I,MACE4P,OAAQiB,GACN4I,EAAOC,QACX,OAAIhW,EAAUmN,EAAiBqD,EAAMnN,QAAQ6I,OAAO+E,WAC3CT,EAEFpR,GAAO,CACZoR,QACArD,kBACA3I,OAAQ8Q,GAAW9E,GAASA,EAAMhM,OAAS,MAE/C,CACA,GAAoB,4BAAhBuR,EAAOlb,KAAoC,CAC7C,GAAoB,iBAAhB2V,EAAMW,MACR,OAAOuE,GAAwBlF,GAEjC,GAAoB,eAAhBA,EAAMW,MACR,OAAOuE,GAAwBlF,GAEhCU,GAAkBV,IAA4HlU,IAC/I,MAAM,GACJqC,EAAE,UACFqE,GACE+S,EAAOC,QACLrV,EAAS6P,EAAMG,WAAWpN,WAAW5E,GAC3C,IAAKgC,EACH,OAAO6P,EAET,MAAMrO,EAAWW,EAAgBnC,EAAQqC,GACzC,OAAOuS,GAAoB/E,EAAOrO,GAAU,EAC9C,CACA,GAAoB,gCAAhB4T,EAAOlb,KAAwC,CACjD,GAAoB,iBAAhB2V,EAAMW,MACR,OAAOX,EAERU,GAAkBV,IAAiIlU,IACpJ,MAAM,GACJqC,EAAE,UACFgO,GACEoJ,EAAOC,QACLrV,EAAS6P,EAAMG,WAAWpN,WAAW5E,GAC1CgC,GAAoIrE,IACnIqE,EAAOgM,YAAcA,GAC6BrQ,IACpD,MAAM0W,EAAU,IACXrS,EACHgM,aAEF,OAAO4I,GAAoB/E,EAAOwC,GAAS,EAC7C,CACA,GAAoB,wCAAhB+C,EAAOlb,KAAgD,CACzD,GAAoB,iBAAhB2V,EAAMW,MACR,OAAOX,EAERU,GAAkBV,IAAiIlU,IACpJ,MAAM,GACJqC,EAAE,iBACF+K,GACEqM,EAAOC,QACLrV,EAAS6P,EAAMG,WAAWpN,WAAW5E,GAC1CgC,GAA6IrE,IAC5IqE,EAAO+I,mBAAqBA,GAC6BpN,IAC3D,MAAM0W,EAAU,IACXrS,EACH+I,oBAEF,OAAO6L,GAAoB/E,EAAOwC,GAAS,EAC7C,CACA,GAAoB,0BAAhB+C,EAAOlb,KAAkC,CAC3C,GAAoB,iBAAhB2V,EAAMW,OAA4C,mBAAhBX,EAAMW,MAC1C,OAAOX,EAERU,GAAkBV,IAAqHlU,IACvIkU,EAAMyF,uBAAkJ3Z,IACzJ,MAAM0G,EAAY+S,EAAOC,QAAQhT,UACjC,GAAIhD,EAAUwQ,EAAM3J,SAASzE,OAAOiB,QAASL,GAC3C,OAAO0S,GAAwBlF,GAEjC,MAAM3J,EAAW6E,GAAe8E,EAAM3J,SAAU7D,GAChD,OAAIsS,GAAW9E,GACN0D,GAAY,CACjB1D,QACA3J,aAGGzH,GAAO,CACZoR,QACA3J,YAEJ,CACA,GAAoB,+BAAhBkP,EAAOlb,KAAuC,CAChD,IAAKqW,GAAkBV,GACrB,OAAOA,EAET,MAAMzB,EAAYgH,EAAOC,QAAQjH,UACjC,GAAI/O,EAAU+O,EAAWyB,EAAM3J,SAASzE,OAAOV,KAC7C,OAAO8O,EAET,MAAMrC,EAAgB,IACjBqC,EAAM3J,SACTzE,OAAQ,IACHoO,EAAM3J,SAASzE,OAClBV,IAAKqN,IAGT,MAAO,IACFyB,EACH3J,SAAUsH,EAEd,CACA,GAAoB,YAAhB4H,EAAOlb,MAAsC,cAAhBkb,EAAOlb,MAAwC,cAAhBkb,EAAOlb,MAAwC,eAAhBkb,EAAOlb,KAAuB,CAC3H,GAAoB,eAAhB2V,EAAMW,OAA0C,iBAAhBX,EAAMW,MACxC,OAAOX,EAES,aAAhBA,EAAMW,OAA0I7U,IAClJ,MAAMiD,EAASgR,GAAgB,CAC7BC,QACA3V,KAAMkb,EAAOlb,OAEf,OAAK0E,EAGEH,GAAO,CACZoR,QACAhM,OAAQjF,EAAOiF,OACf2I,gBAAiB5N,EAAO4N,gBACxBC,kBAAmB7N,EAAO6N,oBANnBoD,CAQX,CACA,GAAoB,iBAAhBuF,EAAOlb,KAAyB,CAClC,MAAM2E,EAASuW,EAAOC,QAAQxW,OACZ,eAAhBgR,EAAMW,OAA4J7U,IAOpK,MANiB,IACZkU,EACHW,MAAO,eACP2F,WAAW,EACXtX,SAGJ,CACA,GAAoB,iBAAhBuW,EAAOlb,KAAyB,CAClC,MAAM,UACJ+a,EAAS,aACToB,EAAY,oBACZC,GACElB,EAAOC,QACO,aAAhBxF,EAAMW,OAAwC,iBAAhBX,EAAMW,OAAwI7U,IAQ9K,MAPe,CACb6U,MAAO,iBACPyE,YACAoB,eACAC,sBACAtG,WAAYH,EAAMG,WAGtB,CACA,GAAoB,kBAAhBoF,EAAOlb,KAA0B,CACnC,MAAM,UACJ+a,GACEG,EAAOC,QACX,MAAO,CACL7E,MAAO,OACPyE,YACAC,aAAa,EAEjB,CACA,OAAOrF,CAAK,EAGd,SAAS0G,GAAMnB,EAAQoB,GACrB,OAAOpB,aAAkBjS,QAAU,SAAUiS,GAAUA,EAAOlb,OAASsc,CACzE,CACA,MAIMC,GAASva,IAAQ,CACrBhC,KAAM,OACNmb,QAASnZ,IAMLwa,GAAuBxa,IAAQ,CACnChC,KAAM,yBACNmb,QAASnZ,IAELya,GAAqB,KAAM,CAC/Bzc,KAAM,sBACNmb,QAAS,OAELuB,GAAwB1a,IAAQ,CACpChC,KAAM,0BACNmb,QAASnZ,IAEL2a,GAA2B3a,IAAQ,CACvChC,KAAM,8BACNmb,QAASnZ,IAEL4a,GAAkC5a,IAAQ,CAC9ChC,KAAM,sCACNmb,QAASnZ,IAEL6a,GAAO7a,IAAQ,CACnBhC,KAAM,OACNmb,QAASnZ,IAUL8a,GAAS,KAAM,CACnB9c,KAAM,UACNmb,QAAS,OAEL4B,GAAW,KAAM,CACrB/c,KAAM,YACNmb,QAAS,OAEL6B,GAAY,KAAM,CACtBhd,KAAM,aACNmb,QAAS,OAEL8B,GAAW,KAAM,CACrBjd,KAAM,YACNmb,QAAS,OAEL+B,GAAQ,KAAM,CAClBld,KAAM,QACNmb,QAAS,OAMLgC,GAAenb,IAAQ,CAC3BhC,KAAM,gBACNmb,QAASnZ,IAELob,GAAOpb,IAAQ,CACnBhC,KAAM,OACNmb,QAASnZ,IAMLqb,GAAwB,KAAM,CAClCrd,KAAM,0BACNmb,QAAS,OAkGX,MAAMmC,GAEE,0BAEFvZ,GACK,CACPqZ,KAAM,EACNG,UAAW,IAHTxZ,GAKG,CACLqZ,KAAM,KAGJI,GAAU,CACdC,YAAa,GACbC,YAAa,IACbC,YAAa,KAETC,GAAoB,GAAGJ,GAAQC,gBAjBtB,+BAkBTI,GAAc,CAClBC,MAAO,WAAWF,KAClBG,KAAM,aAAaH,eAA8BA,KACjDR,KAAMY,IACJ,MAAMC,EAAS,GAAGD,MAAaV,KAC/B,MAAO,aAAaW,cAAmBA,GAAQ,EAEjDR,YAAa,aAAaG,KAC1BrD,YAAa,UAAUqD,aAA4BA,cAA6BA,MAE5EM,GAAS9M,GAAUjM,EAAUiM,EAAQxM,QAAUuZ,EAAY,aAAa/M,EAAOvM,QAAQuM,EAAOtM,OAC9FsZ,GACJF,GADIE,GAEE,CAAChN,EAAQiN,KACb,MAAMC,EAAYJ,GAAO9M,GACzB,GAAKkN,EAGL,OAAKD,EAGE,GAAGC,WAAmBva,GAAcqZ,QAFlCkB,CAEyC,GAIhD,YACJZ,GAAW,YACXC,IACEH,GACEe,GAAgBZ,GAAcD,GAoFpC,MAAMc,GAAiB,EACrBC,WACAC,cACIC,GAAQzD,IACZ,IAAKmB,GAAMnB,EAAQ,QAEjB,YADAyD,EAAKzD,GAGP,MAAMvF,EAAQ8I,IACR9Z,EAASuW,EAAOC,QAAQxW,OAC9B,GAAoB,eAAhBgR,EAAMW,MAIR,YAHAoI,EAvPgB1c,KAAQ,CAC1BhC,KAAM,eACNmb,QAASnZ,IAqPE4c,CAAY,CACnBja,YAIJ,GAAoB,SAAhBgR,EAAMW,MACR,OAEuC,iBAAhBX,EAAMW,OAA4BX,EAAMsG,WAC8Exa,IAC7H,aAAhBkU,EAAMW,OAAwC,iBAAhBX,EAAMW,OAA+H7U,IACrK,MAAMwU,EAAWN,EAAMM,SACjBH,EAAaH,EAAMG,WACnBxM,EAAYwM,EAAW/M,WAAW4M,EAAMM,SAAS3M,UAAUxF,KAC3D,OACJ6F,EAAM,uBACNkV,GAhEgB,GAClB9V,aACApE,SACAma,aACA9I,OACAhK,WACAuP,mBAEA,IAAKuD,EAAWlV,IAAiB,SAAXjF,EAQpB,MAAO,CACLgF,OAR2BuP,GAAU,CACrCnQ,aACAY,OAAQ4R,EACR/X,YAAawS,EACbhK,WACAU,oBAAoB,IAIpBmS,wBAAwB,GAG5B,MAA2B,YAAvBC,EAAWlV,GAAG5J,KACT,CACL2J,OAAQmV,EACRD,wBAAwB,GAOrB,CACLlV,OALsB,IACnBmV,EACHrU,UAAWL,GAIXyU,wBAAwB,EACzB,EA+BGE,CAAc,CAChBpa,SACAma,WAAYnJ,EAAMhM,OAClBsE,cAAe0H,EAAM1H,cACrBsN,aAAc5F,EAAM4F,aACpBvF,KAAML,EAAMG,WAAWpN,WAAWiN,EAAMM,SAAS/N,UAAUpE,IAC3DkI,SAAU2J,EAAM3J,SAChBjD,WAAY4M,EAAMG,WAAW/M,aAEzBvF,EAAcqb,EAAyBnV,EAAkBC,GAAU,KACnE5F,EAAU8a,EAAyBhV,EAAcF,GAAU,KAC3DpG,EAAS,CACbF,MAAO4S,EAAS3M,UAAUjG,MAC1BK,YAAauS,EAAS/N,UAAUpE,IAE5BY,EAAS,CACbV,YAAasF,EAAUT,WAAW/E,GAClC9D,KAAMsJ,EAAUT,WAAW7I,KAC3BuD,SACAoB,SACA+O,KAAMiC,EAAM2D,aACZ9V,cACAO,WAEIqY,EAnHqB,GAC3BzS,SACAL,YACAwM,aACA9J,WACAiC,oBAEA,MAAM,WACJlF,EAAU,WACVL,GACEoN,EACEpS,EAAc+R,GAAkB9L,GAChCnG,EAAcE,EAAcgF,EAAWhF,GAAe,KACtDsS,EAAOtN,EAAWY,EAAUT,WAAWnF,aACvCsb,EAAkB5F,GAAyB,CAC/CzP,SACAL,YACAP,aACAkF,gBACA/F,UAAW1E,GAAewS,EAC1BhK,aAGF,OADe9G,EAAS8Z,EAAiB1V,EAAU+H,OAAOjC,UAAUD,OACvD,EA4Fe8P,CAAuB,CACjDtV,SACAL,YACAwM,aACA9J,SAAU2J,EAAM3J,SAChBiC,cAAe0H,EAAM1H,gBAEjB8M,EAAY,CAChB9E,SAAUN,EAAMM,SAChBhI,cAAe0H,EAAM1H,cACrBvJ,SACAiF,UAGF,MAD6BxE,EAAUwQ,EAAMnN,QAAQ6I,OAAOD,OAAQgL,IAAwBlO,QAAQxJ,EAAOX,UAKzG,YAHA2a,EAASvB,GAAa,CACpBpC,eAIJ,MAAMoB,EAzJc,GACpB3T,UACAhF,cACAmB,aAEA,MAAMua,EAAaxZ,EAAS8C,EAAShF,GACrC,GAAI0b,GAAc,EAChB,OAAOxB,GAET,GAAIwB,GAXwB,KAY1B,OAAOvB,GAET,MACMK,EAAWN,GAAca,IADZW,EAdS,MAiB5B,OAAOC,QADyB,WAAXxa,EAfI,GAekBqZ,EAAgCA,GAChDoB,QAAQ,GAAG,EA0IjBC,CAAgB,CACnC7W,QAASmN,EAAMnN,QAAQ6I,OAAOD,OAC9B5N,YAAa4Y,EACbzX,WAOF+Z,EA1UkB1c,KAAQ,CAC1BhC,KAAM,eACNmb,QAASnZ,IAwUAsd,CALI,CACXlD,sBACAD,eACApB,cAEyB,EAG7B,IAAIwE,GAAkB,KAAM,CAC1B1a,EAAGjC,OAAO4c,YACV1a,EAAGlC,OAAO6c,cAkBZ,SAASC,IAAkB,eACzBC,IAKA,MAAMC,GAAY,QAHlB,WACED,EAAeJ,KACjB,IAEM7e,EAtBR,SAAgC6D,GAC9B,MAAO,CACLvD,UAAW,SACXL,QAAS,CACPkf,SAAS,EACTC,SAAS,GAEX7e,GAAImB,IACEA,EAAM0D,SAAWlD,QAAUR,EAAM0D,SAAWlD,OAAOmd,UAGvDxb,GAAQ,EAGd,CAQkByb,CAAuBJ,GACvC,IAAIxe,EAASjB,EACb,SAAS8f,IACP,OAAO7e,IAAWjB,CACpB,CAWA,MAAO,CACLkE,MAXF,WACI4b,KAA4Hxe,IAC9HL,EAAShB,EAAWwC,OAAQ,CAAClC,GAC/B,EASEwf,KARF,WACGD,KAAuHxe,IACxHme,EAAUO,SACV/e,IACAA,EAASjB,CACX,EAIE8f,WAEJ,CAEA,MACMG,GAAiBC,IACrB,MAAMC,EAAWZ,GAAkB,CACjCC,eAAgBxX,IACdkY,EAAM3B,SA9ZuB,CACjC1e,KAAM,wBACNmb,QA4ZsC,CAChChT,cACC,IAGP,OAAOwW,GAAQzD,KACRoF,EAASL,YAAc5D,GAAMnB,EAAQ,oBACxCoF,EAASjc,QAEPic,EAASL,YAbI/E,IAAUmB,GAAMnB,EAAQ,kBAAoBmB,GAAMnB,EAAQ,iBAAmBmB,GAAMnB,EAAQ,SAajFqF,CAAarF,IACtCoF,EAASJ,OAEXvB,EAAKzD,EAAO,CACb,EAGH,IA0BIsF,GAAkB,KACpB,MAAMC,EAAU,GA0BhB,MAAO,CACL1b,IApBU9D,IACV,MAAMyf,EAAUC,YAAW,IAPbD,KACd,MAAMrd,EAAQod,EAAQtO,WAAUlI,GAAQA,EAAKyW,UAAYA,KAC5C,IAAXrd,GAAmG5B,IACrG,MAAOmf,GAASH,EAAQI,OAAOxd,EAAO,GACtCud,EAAME,UAAU,EAGiBC,CAAQL,KACnCE,EAAQ,CACZF,UACAI,SAAU7f,GAEZwf,EAAQ1T,KAAK6T,EAAM,EAenB1D,MAbY,KACZ,IAAKuD,EAAQnT,OACX,OAEF,MAAM0T,EAAU,IAAIP,GACpBA,EAAQnT,OAAS,EACjB0T,EAAQ7f,SAAQyf,IACdK,aAAaL,EAAMF,SACnBE,EAAME,UAAU,GAChB,EAKH,EAGH,MA2BMI,GAAc,CAACtH,EAAK3Y,KACxBoD,KACApD,IACA4Y,IAAQ,EAEJsH,GAAe,CAAClL,EAAUvC,KAAS,CACvC1P,YAAaiS,EAAS3M,UAAUxF,GAChC9D,KAAMiW,EAAS/N,UAAUlI,KACzBuD,OAAQ,CACNG,YAAauS,EAAS/N,UAAUpE,GAChCT,MAAO4S,EAAS3M,UAAUjG,OAE5BqQ,SAEF,SAASqN,GAAQK,EAAWC,EAAMC,EAAUC,GAC1C,IAAKH,EAEH,YADAE,EAASC,EAAkBF,IAG7B,MAAMG,EAzGkBF,KACxB,IAAIG,GAAY,EACZC,GAAY,EAChB,MAAMC,EAAYhB,YAAW,KAC3Be,GAAY,CAAI,IAEZhd,EAASzE,IACTwhB,GAIAC,IAOJD,GAAY,EACZH,EAASrhB,GACTghB,aAAaU,GAAU,EAGzB,OADAjd,EAAO+c,UAAY,IAAMA,EAClB/c,CAAM,EAkFMkd,CAAoBN,GAIvCF,EAAUC,EAHO,CACfC,SAAUE,IAGPA,EAAWC,aACdH,EAASC,EAAkBF,GAE/B,CACA,IAkGIQ,GAAa,CAACC,EAAeR,KAC/B,MAAMS,EAnGW,EAACD,EAAeR,KACjC,MAAMU,EAAexB,KACrB,IAAIyB,EAAW,KACf,MAmEM7E,EAAO1Y,IACVud,GAAqIxgB,IACtIwgB,EAAW,KACXf,GAAY,GAAa,IAAMH,GAAQe,IAAgBrd,UAAWC,EAAQ4c,EAAUpd,EAAOO,YAAW,EAcxG,MAAO,CACLyd,cArFoB,CAACle,EAAa0P,KAChCuO,GAAgJxgB,IAClJyf,GAAY,GAAmB,KAC7B,MAAMjgB,EAAK6gB,IAAgBK,gBACvBlhB,GAKFA,EAJe,CACb+C,cACA0P,QAGJ,GACA,EA2EF0O,YAzEkB,CAACnM,EAAUvC,KAC3BuO,GAAkJxgB,IACpJyf,GAAY,GAAqB,KAC/B,MAAMjgB,EAAK6gB,IAAgBO,kBACvBphB,GACFA,EAAGkgB,GAAalL,EAAUvC,GAC5B,GACA,EAmEFrP,MAjEY,CAAC4R,EAAUvC,KACrBuO,GAAkJxgB,IACpJ,MAAM4f,EAAOF,GAAalL,EAAUvC,GACpCuO,EAAW,CACTvO,OACA4O,aAAcrM,EACdsM,aAAclB,EAAK9d,OACnBif,YAAa,MAEfR,EAAajd,KAAI,KACfmc,GAAY,GAAe,IAAMH,GAAQe,IAAgB1d,YAAaid,EAAMC,EAAUpd,EAAOE,cAAa,GAC1G,EAuDFG,OArDa,CAAC0R,EAAUtM,KACxB,MAAMnF,EAAWkF,EAAkBC,GAC7B5F,EAAU8F,EAAcF,GAC7BsY,GAAqIxgB,IACtI,MAAMghB,GA/Ec,EAAC/N,EAAOC,KAC9B,GAAID,IAAUC,EACZ,OAAO,EAET,MAAM+N,EAAmBhO,EAAMpL,UAAUxF,KAAO6Q,EAAOrL,UAAUxF,IAAM4Q,EAAMpL,UAAU5F,cAAgBiR,EAAOrL,UAAU5F,aAAegR,EAAMpL,UAAUtJ,OAAS2U,EAAOrL,UAAUtJ,MAAQ0U,EAAMpL,UAAUjG,QAAUsR,EAAOrL,UAAUjG,MAC9Nsf,EAAmBjO,EAAMxM,UAAUpE,KAAO6Q,EAAOzM,UAAUpE,IAAM4Q,EAAMxM,UAAUlI,OAAS2U,EAAOzM,UAAUlI,KACjH,OAAO0iB,GAAoBC,CAAgB,EAyEbC,CAAgB3M,EAAUgM,EAASK,cAC3DG,IACFR,EAASK,aAAerM,GAE1B,MAAM4M,GArGwBlO,EAqGuCnQ,IApG1D,OADYkQ,EAqGuBuN,EAASM,eApG1B,MAAV5N,GAGR,MAATD,GAA2B,MAAVC,GAGdD,EAAMhR,cAAgBiR,EAAOjR,aAAegR,EAAMrR,QAAUsR,EAAOtR,QAPlD,IAACqR,EAAOC,EAsG1BkO,IACFZ,EAASM,aAAe/d,GAE1B,MAAMse,GAhGa,EAACpO,EAAOC,IAChB,MAATD,GAA2B,MAAVC,GAGR,MAATD,GAA2B,MAAVC,GAGdD,EAAM1Q,cAAgB2Q,EAAO3Q,aAAe0Q,EAAMhR,cAAgBiR,EAAOjR,YAyFlDqf,CAAed,EAASO,YAAaze,GAIjE,GAHI+e,IACFb,EAASO,YAAcze,IAEpB0e,IAAuBI,IAAuBC,EACjD,OAEF,MAAMzB,EAAO,IACRF,GAAalL,EAAUgM,EAASvO,MACnC3P,UACAP,YAAagB,GAEfwd,EAAajd,KAAI,KACfmc,GAAY,GAAgB,IAAMH,GAAQe,IAAgBxd,aAAc+c,EAAMC,EAAUpd,EAAOI,eAAc,GAC7G,EA4BF4Y,MA1BY,KACX+E,GAAkHxgB,IACnHugB,EAAa9E,OAAO,EAyBpBE,OACA4F,MAnBY,KACZ,IAAKf,EACH,OAEF,MAAMvd,EAAS,IACVyc,GAAac,EAASK,aAAcL,EAASvO,MAChD3P,QAAS,KACTP,YAAa,KACbmB,OAAQ,UAEVyY,EAAK1Y,EAAO,EAUb,EAIiBue,CAAanB,EAAeR,GAC9C,OAAOjB,GAAS1B,GAAQzD,IACtB,GAAImB,GAAMnB,EAAQ,0BAEhB,YADA6G,EAAUG,cAAchH,EAAOC,QAAQnX,YAAakX,EAAOC,QAAQ7B,cAGrE,GAAI+C,GAAMnB,EAAQ,mBAAoB,CACpC,MAAMjF,EAAWiF,EAAOC,QAAQlF,SAIhC,OAHA8L,EAAUK,YAAYnM,EAAUiF,EAAOC,QAAQ7B,cAC/CqF,EAAKzD,QACL6G,EAAU1d,MAAM4R,EAAUiF,EAAOC,QAAQ7B,aAE3C,CACA,GAAI+C,GAAMnB,EAAQ,iBAAkB,CAClC,MAAMxW,EAASwW,EAAOC,QAAQJ,UAAUrW,OAIxC,OAHAqd,EAAU7E,QACVyB,EAAKzD,QACL6G,EAAU3E,KAAK1Y,EAEjB,CAEA,GADAia,EAAKzD,GACDmB,GAAMnB,EAAQ,SAEhB,YADA6G,EAAUiB,QAGZ,MAAMrN,EAAQ0K,EAAM5B,WACA,aAAhB9I,EAAMW,OACRyL,EAAUxd,OAAOoR,EAAMM,SAAUN,EAAMhM,OACzC,CACD,EAGH,MAAMuZ,GAAgC7C,GAAS1B,GAAQzD,IACrD,IAAKmB,GAAMnB,EAAQ,2BAEjB,YADAyD,EAAKzD,GAGP,MAAMvF,EAAQ0K,EAAM5B,WACF,mBAAhB9I,EAAMW,OAAqJ7U,IAC7J4e,EAAM3B,SAASvB,GAAa,CAC1BpC,UAAWpF,EAAMoF,YAChB,EAGCoI,GAAuC9C,IAC3C,IAAIjf,EAAS,KACTgiB,EAAU,KAWd,OAAOzE,GAAQzD,IAKb,IAJImB,GAAMnB,EAAQ,UAAYmB,GAAMnB,EAAQ,kBAAoBmB,GAAMnB,EAAQ,8BAV1EkI,IACFC,qBAAqBD,GACrBA,EAAU,MAERhiB,IACFA,IACAA,EAAS,OAOXud,EAAKzD,IACAmB,GAAMnB,EAAQ,gBACjB,OAEF,MAAMxa,EAAU,CACdM,UAAW,SACXL,QAAS,CACPmf,SAAS,EACTD,SAAS,EACTyD,MAAM,GAERriB,GAAI,WAEkB,mBADNof,EAAM5B,WACVnI,OACR+J,EAAM3B,SAlqBoB,CAClC1e,KAAM,0BACNmb,QAAS,MAkqBL,GAEFiI,EAAUG,uBAAsB,KAC9BH,EAAU,KACVhiB,EAAShB,EAAWwC,OAAQ,CAAClC,GAAS,GACtC,CACH,EAyCH,IAAI8iB,GAAaC,GAAgBpD,GAAS1B,GAAQzD,IAChD,GAFiBA,IAAUmB,GAAMnB,EAAQ,kBAAoBmB,GAAMnB,EAAQ,iBAAmBmB,GAAMnB,EAAQ,SAExGwI,CAAWxI,GAGb,OAFAuI,EAAavD,YACbvB,EAAKzD,GAGP,GAAImB,GAAMnB,EAAQ,mBAAoB,CACpCyD,EAAKzD,GACL,MAAMvF,EAAQ0K,EAAM5B,WAGpB,MAFkB,aAAhB9I,EAAMW,OAA0I7U,SAClJgiB,EAAapf,MAAMsR,EAErB,CACAgJ,EAAKzD,GACLuI,EAAalc,OAAO8Y,EAAM5B,WAAW,EAGvC,MAAMkF,GAActD,GAAS1B,GAAQzD,IAEnC,GADAyD,EAAKzD,IACAmB,GAAMnB,EAAQ,0BACjB,OAEF,MAAM0I,EAAkBvD,EAAM5B,WACA,iBAA1BmF,EAAgBtN,QAGhBsN,EAAgB3H,WAGpBoE,EAAM3B,SAAStB,GAAK,CAClBzY,OAAQif,EAAgBjf,UACvB,EAGCkf,GAED,KACL,IAAIC,GAAc,EAChBC,mBACAC,eACAC,eACAnC,gBACAR,WACAmC,mBACI,eAAcxI,GAAS4I,IAAiB,SAxqBlCK,EAwqBwDD,EAxqB7C,IAAMtF,GAAQzD,IAC/BmB,GAAMnB,EAAQ,oBAChBgJ,EAAQjC,WAEN5F,GAAMnB,EAAQ,iBAChBgJ,EAAQC,SAASjJ,EAAOC,QAAQJ,UAAUrW,OAAOC,SAE/C0X,GAAMnB,EAAQ,UAAYmB,GAAMnB,EAAQ,mBAC1CgJ,EAAQE,UAEVzF,EAAKzD,EAAO,GA4kBgBgJ,IAAW,IAAMvF,GAAQzD,KACjDmB,GAAMnB,EAAQ,kBAAoBmB,GAAMnB,EAAQ,UAAYmB,GAAMnB,EAAQ,kBAC5EgJ,EAAQG,iBAEV1F,EAAKzD,EAAO,EA8EqEoJ,CAAwBP,GAvtBhGG,IAAW,EACpBzF,WACAC,cACIC,GAAQzD,IACZ,IAAKmB,GAAMnB,EAAQ,QAEjB,YADAyD,EAAKzD,GAGP,MAAM,GACJpX,EAAE,gBACFwO,EAAe,aACfgH,GACE4B,EAAOC,QACL7S,EAAUmW,IACM,mBAAlBnW,EAAQgO,OACVoI,EAASvB,GAAa,CACpBpC,UAAWzS,EAAQyS,aAGA,SAArB0D,IAAWnI,OAAmH7U,IAChIid,EAASxB,MACTwB,EA7ImC,CACnC1e,KAAM,yBACNmb,QA2I8B,CAC5BnX,YAAaF,EACbwV,kBAEF,MAGMiL,EAAU,CACdvgB,YAAaF,EACb0gB,cALoB,CACpBC,yBAA2C,SAAjBnL,KAMtB,SACJrD,EAAQ,WACRH,EAAU,SACV9J,GACEkY,EAAQQ,gBAAgBH,GAE5B7F,EAtJ6B,CAC7B1e,KAAM,kBACNmb,QAoJwB,CACtBlF,WACAH,aACAxD,kBACAgH,eACAtN,aACC,EA2qByH2Y,CAAKZ,GAAmBvF,GAAgB0E,GAA+BC,GAAsCQ,GAAaH,GAAWC,GAAerD,GA3EtQ8D,KACV,IAAIU,GAAa,EACjB,MAAO,IAAMjG,GAAQzD,IACnB,GAAImB,GAAMnB,EAAQ,mBAKhB,OAJA0J,GAAa,EACbV,EAAQW,eAAe3J,EAAOC,QAAQlF,SAAS3M,UAAUxF,IACzD6a,EAAKzD,QACLgJ,EAAQY,0BAIV,GADAnG,EAAKzD,GACA0J,EAAL,CAGA,GAAIvI,GAAMnB,EAAQ,SAGhB,OAFA0J,GAAa,OACbV,EAAQY,0BAGV,GAAIzI,GAAMnB,EAAQ,iBAAkB,CAClC0J,GAAa,EACb,MAAMlgB,EAASwW,EAAOC,QAAQJ,UAAUrW,OACpCA,EAAOX,SACTmgB,EAAQa,eAAergB,EAAOV,YAAaU,EAAOX,QAAQC,aAE5DkgB,EAAQY,yBACV,CAbA,CAaA,CACD,EAgD+RE,CAAMhB,GAAenC,GAAWC,EAAeR,MAxqBrU4C,KAwqBiV,EA8E7V,IAAIe,GAAe,EACjBC,eACAC,cACApe,SACAD,YAEA,MAAMoN,EAAYhP,EAAS,CACzBL,EAAGsgB,EACHrgB,EAAGogB,GACF,CACDrgB,EAAGiC,EACHhC,EAAGiC,IAML,MAJ0B,CACxBlC,EAAGc,KAAKkB,IAAI,EAAGqN,EAAUrP,GACzBC,EAAGa,KAAKkB,IAAI,EAAGqN,EAAUpP,GAEH,EAGtBsgB,GAAqB,KACvB,MAAMC,EAAMtF,SAASuF,gBAErB,OADCD,GAAyG5jB,IACnG4jB,CAAG,EAGRE,GAAqB,KACvB,MAAMF,EAAMD,KAOZ,OANkBH,GAAa,CAC7BC,aAAcG,EAAIH,aAClBC,YAAaE,EAAIF,YACjBre,MAAOue,EAAIG,YACXze,OAAQse,EAAII,cAEE,EAkCdC,GAAoB,EACtBzP,WACAuO,gBACAmB,eAEAthB,KACA,MAAM2H,EArCU,MAChB,MAAMzE,EAASgY,KACTrL,EAAYqR,KACZnf,EAAMmB,EAAOzC,EACbuB,EAAOkB,EAAO1C,EACdwgB,EAAMD,KAGN7e,EAAQF,EAFAgf,EAAIG,YAGZlf,EAASF,EAFAif,EAAII,aAqBnB,MAZiB,CACf/e,OAPY,OAAQ,CACpBN,MACAC,OACAE,QACAD,WAIAiB,OAAQ,CACNe,QAASf,EACTiB,QAASjB,EACTV,IAAKqN,EACL1M,KAAM,CACJhC,MAAOZ,EACP6C,aAAc7C,IAIL,EASEghB,GACXC,EAAe7Z,EAASzE,OAAOiB,QAC/BwN,EAAOC,EAAS/N,UAChBQ,EAAaid,EAASzd,UAAU4d,aAAa9P,EAAKhW,MAAMS,KAAImgB,GAASA,EAAM1e,UAAU6jB,2BAA2BF,EAAcrB,KAC9Hzb,EAAa4c,EAASrc,UAAUwc,aAAa7P,EAAS3M,UAAUtJ,MAAMS,KAAImgB,GAASA,EAAMoF,aAAaH,KACtG/P,EAAa,CACjB/M,WAAYD,EAAeC,GAC3BL,WAAYD,EAAeC,IAE7BmR,KAMA,MALe,CACb/D,aACAG,WACAjK,WAEW,EAGf,SAASia,GAAoBN,EAAU1D,EAAUrB,GAC/C,GAAIA,EAAM/X,WAAW/E,KAAOme,EAASne,GACnC,OAAO,EAET,GAAI8c,EAAM/X,WAAW7I,OAASiiB,EAASjiB,KACrC,OAAO,EAGT,MAA6B,YADhB2lB,EAASzd,UAAUge,QAAQtF,EAAM/X,WAAWnF,aAChDmF,WAAW6K,IAUtB,CACA,IAAIyS,GAAyB,CAACR,EAAUzjB,KACtC,IAAIkkB,EAAa,KACjB,MAAMrE,EAxLR,UAAyB,SACvB4D,EAAQ,UACRzjB,IAEA,IAAImkB,EATgB,CACpBtM,UAAW,CAAC,EACZ8B,SAAU,CAAC,EACXH,SAAU,CAAC,GAOP0H,EAAU,KACd,MAAMkD,EAAU,KACVlD,IAGJlhB,EAAUua,qBACV2G,EAAUG,uBAAsB,KAC9BH,EAAU,KACV/e,KACA,MAAM,UACJ0V,EAAS,SACT8B,EAAQ,SACRH,GACE2K,EACErS,EAAQ/K,OAAOsd,KAAKxM,GAAWtZ,KAAIqD,GAAM6hB,EAASrc,UAAU4c,QAAQpiB,GAAIkiB,aAAaphB,KAAS2E,MAAK,CAACC,EAAGC,IAAMD,EAAEX,WAAWxF,MAAQoG,EAAEZ,WAAWxF,QAC/I8U,EAAUlP,OAAOsd,KAAK7K,GAAUjb,KAAIqD,IAGjC,CACLJ,YAAaI,EACbyD,OAJYoe,EAASzd,UAAUge,QAAQpiB,GACpB5B,UAAUskB,6BAM3B9hB,EAAS,CACbqV,UAAW/F,EACX6H,SAAU5S,OAAOsd,KAAK1K,GACtBH,SAAUvD,GAEZkO,EAtCgB,CACpBtM,UAAW,CAAC,EACZ8B,SAAU,CAAC,EACXH,SAAU,CAAC,GAoCP7B,KACA3X,EAAUukB,QAAQ/hB,EAAO,IACzB,EA4BJ,MAAO,CACLK,IA3BU6b,IACV,MAAM9c,EAAK8c,EAAM/X,WAAW/E,GAC5BuiB,EAAQtM,UAAUjW,GAAM8c,EACxByF,EAAQ3K,SAASkF,EAAM/X,WAAWnF,cAAe,EAC7C2iB,EAAQxK,SAAS/X,WACZuiB,EAAQxK,SAAS/X,GAE1BwiB,GAAS,EAqBTvc,OAnBa6W,IACb,MAAM/X,EAAa+X,EAAM/X,WACzBwd,EAAQxK,SAAShT,EAAW/E,KAAM,EAClCuiB,EAAQ3K,SAAS7S,EAAWnF,cAAe,EACvC2iB,EAAQtM,UAAUlR,EAAW/E,YACxBuiB,EAAQtM,UAAUlR,EAAW/E,IAEtCwiB,GAAS,EAaTpG,KAXW,KACNkD,IAGLC,qBAAqBD,GACrBA,EAAU,KACViD,EAnEkB,CACpBtM,UAAW,CAAC,EACZ8B,SAAU,CAAC,EACXH,SAAU,CAAC,GAgEU,EAOvB,CAmHoBgL,CAAgB,CAChCxkB,UAAW,CACTukB,QAASvkB,EAAUsa,qBACnBC,mBAAoBva,EAAUua,oBAEhCkJ,aAgDIgB,EAAavkB,IAChBgkB,GAAoI3kB,IACrI,MAAMwgB,EAAWmE,EAAWnQ,SAAS3M,UAClB,aAAflH,EAAMpC,MACJimB,GAAoBN,EAAU1D,EAAU7f,EAAMoD,QAChDuc,EAAUhd,IAAI3C,EAAMoD,OAGL,YAAfpD,EAAMpC,MACJimB,GAAoBN,EAAU1D,EAAU7f,EAAMoD,QAChDuc,EAAUhY,OAAO3H,EAAMoD,MAE3B,EAqBI0e,EAAU,CACdvH,yBAhF+B,CAAC7Y,EAAIgO,KACnC6T,EAASzd,UAAU0e,OAAO9iB,IAA6IrC,IACnK2kB,GAGLlkB,EAAUya,yBAAyB,CACjC7Y,KACAgO,aACA,EAyEF8K,gCAvEsC,CAAC9Y,EAAI+K,KACtCuX,IAGJT,EAASzd,UAAU0e,OAAO9iB,IAAmJrC,IAC9KS,EAAU0a,gCAAgC,CACxC9Y,KACA+K,qBACA,EAgEF5G,gBApDsB,CAACnE,EAAI+iB,KACtBT,GAGLT,EAASzd,UAAUge,QAAQpiB,GAAI5B,UAAUqF,OAAOsf,EAAO,EAiDvDnK,sBA/D4B,CAAC5Y,EAAIqE,KAC5Bie,IAGJT,EAASzd,UAAU0e,OAAO9iB,IAAwIrC,IACnKS,EAAUwa,sBAAsB,CAC9B5Y,KACAqE,cACA,EAwDFuc,gBAxBsBH,IACpB6B,GAAuJ3kB,IACzJ,MAAMmf,EAAQ+E,EAASrc,UAAU4c,QAAQ3B,EAAQvgB,aAC3CgS,EAAO2P,EAASzd,UAAUge,QAAQtF,EAAM/X,WAAWnF,aACnDuS,EAAW,CACf3M,UAAWsX,EAAM/X,WACjBX,UAAW8N,EAAKnN,YAEZie,EAAcnB,EAASoB,UAAUJ,GAKvC,OAJAP,EAAa,CACXnQ,WACA6Q,eAEKpB,GAAkB,CACvBzP,WACA0P,WACAnB,cAAeD,EAAQC,eACvB,EAQFH,eAjDqB,KACrB,IAAK+B,EACH,OAEFrE,EAAU7B,OACV,MAAMlK,EAAOoQ,EAAWnQ,SAAS/N,UACjCyd,EAASzd,UAAU4d,aAAa9P,EAAKhW,MAAMmB,SAAQyf,GAASA,EAAM1e,UAAU8kB,gBAC5EZ,EAAWU,cACXV,EAAa,IAAI,GA2CnB,OAAOlC,CAAO,EAGZ+C,GAAe,CAACtR,EAAO7R,IACL,SAAhB6R,EAAMW,OAGU,mBAAhBX,EAAMW,QAGNX,EAAMoF,UAAUrW,OAAOV,cAAgBF,GAGF,SAAlC6R,EAAMoF,UAAUrW,OAAOC,QAG5BuiB,GAAeL,IACjBjkB,OAAOukB,SAASN,EAAOhiB,EAAGgiB,EAAO/hB,EAAE,EAGrC,MAAMsiB,IAA0B,QAAW1e,GAAcM,EAAgBN,GAAYW,QAAOnB,KACrFA,EAAU4J,aAGV5J,EAAUxB,UAYjB,IAAI2gB,GAA6B,EAC/BlY,SACA3L,cACAkF,iBAEA,GAAIlF,EAAa,CACf,MAAMsQ,EAAYpL,EAAWlF,GAC7B,OAAKsQ,EAAUpN,MAGRoN,EAFE,IAGX,CACA,MAAMA,EAnB2B,EAAChO,EAAQ4C,KAC1C,MAAM4e,EAAQF,GAAwB1e,GAAYiF,MAAKzF,IACpDA,EAAUxB,OAAqFjF,IACzF8U,GAAkBrO,EAAUxB,MAAMO,cAAlCsP,CAAiDzQ,OACpD,KACN,OAAOwhB,CAAK,EAcMC,CAA2BpY,EAAQzG,GACrD,OAAOoL,CAAS,EAGlB,MAAM0T,GAA6B,CACjCC,oBAAqB,IACrBC,sBAAuB,IACvBC,eAAgB,GAChBC,KAAMC,GAAcA,GAAc,EAClCC,kBAAmB,CACjBC,gBAAiB,KACjBC,aAAc,KAEhBC,UAAU,GAGZ,IAWIC,GAAgB,EAClBC,eACAC,aACA5f,cAEA,MAAM6f,EAAQD,EAAaD,EAC3B,GAAc,IAAVE,EAMF,OAAO,EAIT,OAFuB7f,EAAU2f,GACGE,CACnB,EAiDfC,GAAW,EACbC,iBACAC,aACAC,gBACAC,yBACAC,6BAEA,MAAMphB,EAnDmB,EAACghB,EAAgBC,EAAYG,EAAyB,IAAMnB,MACrF,MAAMoB,EAAsBD,IAC5B,GAAIJ,EAAiBC,EAAWK,mBAC9B,OAAO,EAET,GAAIN,GAAkBC,EAAWM,iBAC/B,OAAOF,EAAoBjB,eAE7B,GAAIY,IAAmBC,EAAWK,mBAChC,OAXY,EAad,MAKME,EAAmC,EALFb,GAAc,CACnDC,aAAcK,EAAWM,iBACzBV,WAAYI,EAAWK,mBACvBrgB,QAAS+f,IAGLhhB,EAASqhB,EAAoBjB,eAAiBiB,EAAoBhB,KAAKmB,GAC7E,OAAOpjB,KAAKqjB,KAAKzhB,EAAO,EAiCT0hB,CAAqBV,EAAgBC,EAAYG,GAChE,OAAe,IAAXphB,EACK,EAEJmhB,EAGE/iB,KAAKkB,IArCU,EAACqiB,EAAgBT,EAAeE,KACtD,MAAMC,EAAsBD,IACtBX,EAAeY,EAAoBd,kBAAkBE,aACrDmB,EAASP,EAAoBd,kBAAkBC,gBAC/CI,EAAeM,EACfL,EAAae,EAEbC,EADMC,KAAKhR,MACK8P,EACtB,GAAIiB,GAAWD,EACb,OAAOD,EAET,GAAIE,EAAUpB,EACZ,OAnCY,EAqCd,MAAMsB,EAAyCpB,GAAc,CAC3DC,aAAcH,EACdI,aACA5f,QAAS4gB,IAEL7hB,EAAS2hB,EAAiBN,EAAoBhB,KAAK0B,GACzD,OAAO3jB,KAAKqjB,KAAKzhB,EAAO,EAiBRgiB,CAAkBhiB,EAAQkhB,EAAeE,GA5D3C,GA0DLphB,CAEmF,EAG1FiiB,GAAkB,EACpBC,YACAC,kBACAjB,gBACAphB,OACAqhB,yBACAC,6BAEA,MAAMH,EArGoB,EAACiB,EAAWpiB,EAAMshB,EAAyB,IAAMnB,MAC3E,MAAMoB,EAAsBD,IAO5B,MAJmB,CACjBE,mBAHyBY,EAAUpiB,EAAKoE,MAAQmd,EAAoBnB,oBAIpEqB,iBAHuBW,EAAUpiB,EAAKoE,MAAQmd,EAAoBlB,sBAKnD,EA6FEiC,CAAsBF,EAAWpiB,EAAMshB,GAE1D,OADsBe,EAAgBriB,EAAKS,KAAO4hB,EAAgBriB,EAAKhD,OAE9DikB,GAAS,CACdC,eAAgBmB,EAAgBriB,EAAKS,KACrC0gB,aACAC,gBACAC,yBACAC,4BAGI,EAAIL,GAAS,CACnBC,eAAgBmB,EAAgBriB,EAAKhD,OACrCmkB,aACAC,gBACAC,yBACAC,0BACA,EAsBJ,MAAMiB,GAAQ3jB,GAAMT,GAAmB,IAAVA,EAAc,EAAIA,IAC/C,IAAIqkB,GAAc,EAChBpB,gBACAgB,YACA7iB,UACAuI,SACAuZ,yBACAC,6BAEA,MAAMe,EAAkB,CACtBtjB,IAAK+I,EAAOrK,EAAI2kB,EAAUrjB,IAC1BG,MAAOkjB,EAAUljB,MAAQ4I,EAAOtK,EAChCyB,OAAQmjB,EAAUnjB,OAAS6I,EAAOrK,EAClCuB,KAAM8I,EAAOtK,EAAI4kB,EAAUpjB,MAEvBvB,EAAI0kB,GAAgB,CACxBC,YACAC,kBACAjB,gBACAphB,KAAMiE,GACNod,yBACAC,2BAEI9jB,EAAI2kB,GAAgB,CACxBC,YACAC,kBACAjB,gBACAphB,KAAMwE,GACN6c,yBACAC,2BAEImB,EAAWF,GAAM,CACrB/kB,IACAC,MAEF,GAAIK,EAAU2kB,EAAUllB,GACtB,OAAO,KAET,MAAMmlB,EAzDkB,GACxBN,YACA7iB,UACAsiB,qBAEA,MAAMc,EAAqBpjB,EAAQG,OAAS0iB,EAAU1iB,OAChDkjB,EAAuBrjB,EAAQE,MAAQ2iB,EAAU3iB,MACvD,OAAKmjB,GAAyBD,EAG1BC,GAAwBD,EACnB,KAEF,CACLnlB,EAAGolB,EAAuB,EAAIf,EAAerkB,EAC7CC,EAAGklB,EAAqB,EAAId,EAAepkB,GAPpCokB,CAQR,EAyCegB,CAAoB,CAClCT,YACA7iB,UACAsiB,eAAgBY,IAElB,OAAKC,EAGE5kB,EAAU4kB,EAASnlB,GAAU,KAAOmlB,EAFlC,IAEyC,EAGpD,MAAMI,GAAiBlkB,GAAMT,GACb,IAAVA,EACK,EAEFA,EAAQ,EAAI,GAAK,IAEpB4kB,GAAa,MACjB,MAAMC,EAAe,CAACvkB,EAAQe,IACxBf,EAAS,EACJA,EAELA,EAASe,EACJf,EAASe,EAEX,EAET,MAAO,EACL2B,UACA3B,MACAggB,aAEA,MAAMyD,EAAevlB,EAAIyD,EAASqe,GAC5B0D,EAAU,CACd1lB,EAAGwlB,EAAaC,EAAazlB,EAAGgC,EAAIhC,GACpCC,EAAGulB,EAAaC,EAAaxlB,EAAG+B,EAAI/B,IAEtC,OAAIK,EAAUolB,EAAS3lB,GACd,KAEF2lB,CAAO,CAEjB,EAzBkB,GA0BbC,GAAqB,EACzB3jB,IAAK4jB,EACLjiB,UACAqe,aAEA,MAAMhgB,EAAM,CACVhC,EAAGc,KAAKkB,IAAI2B,EAAQ3D,EAAG4lB,EAAO5lB,GAC9BC,EAAGa,KAAKkB,IAAI2B,EAAQ1D,EAAG2lB,EAAO3lB,IAE1B4lB,EAAiBP,GAAetD,GAChC0D,EAAUH,GAAW,CACzBvjB,MACA2B,UACAqe,OAAQ6D,IAEV,OAAKH,IAGoB,IAArBG,EAAe7lB,GAAyB,IAAd0lB,EAAQ1lB,GAGb,IAArB6lB,EAAe5lB,GAAyB,IAAdylB,EAAQzlB,EAG1B,EAER6lB,GAAkB,CAAC3e,EAAU6a,IAAW2D,GAAmB,CAC/DhiB,QAASwD,EAASzE,OAAOiB,QACzB3B,IAAKmF,EAASzE,OAAOV,IACrBggB,WAcI+D,GAAqB,CAAC1iB,EAAW2e,KACrC,MAAMngB,EAAQwB,EAAUxB,MACxB,QAAKA,GAGE8jB,GAAmB,CACxBhiB,QAAS9B,EAAMa,OAAOiB,QACtB3B,IAAKH,EAAMa,OAAOV,IAClBggB,UACA,EAiBJ,IA0CItf,GAAS,EACXoO,QACA8S,gBACAC,yBACAxB,eACAjf,kBACA0gB,6BAEA,MAAMxZ,EAASwG,EAAMnN,QAAQrB,KAAKgP,gBAE5BvP,EADY+O,EAAMG,WAAW/M,WAAW4M,EAAMM,SAAS3M,UAAUxF,IAC7CqD,KAAKQ,UAC/B,GAAIgO,EAAMyF,sBAAuB,CAC/B,MACMyL,EAvDkB,GAC1B7a,WACApF,UACAuI,SACAsZ,gBACAC,yBACAC,6BAEA,MAAMphB,EAASsiB,GAAY,CACzBpB,gBACAgB,UAAWzd,EAAStF,MACpBE,UACAuI,SACAuZ,yBACAC,2BAEF,OAAOphB,GAAUojB,GAAgB3e,EAAUzE,GAAUA,EAAS,IAAI,EAuCjDsjB,CAAsB,CACnCpC,gBACAzc,SAHe2J,EAAM3J,SAIrBpF,UACAuI,SACAuZ,yBACAC,2BAEF,GAAI9B,EAEF,YADAK,EAAaL,EAGjB,CACA,MAAM3e,EAAYmf,GAA2B,CAC3ClY,SACA3L,YAAaiS,GAAkBE,EAAMhM,QACrCjB,WAAYiN,EAAMG,WAAWpN,aAE/B,IAAKR,EACH,OAEF,MAAM2e,EAzDuB,GAC7B3e,YACAtB,UACAuI,SACAsZ,gBACAC,yBACAC,6BAEA,MAAMjiB,EAAQwB,EAAUxB,MACxB,IAAKA,EACH,OAAO,KAET,MAAMa,EAASsiB,GAAY,CACzBpB,gBACAgB,UAAW/iB,EAAMO,cACjBL,UACAuI,SACAuZ,yBACAC,2BAEF,OAAOphB,GAAUqjB,GAAmB1iB,EAAWX,GAAUA,EAAS,IAAI,EAqCvDujB,CAAyB,CACtCrC,gBACAvgB,YACAtB,UACAuI,SACAuZ,yBACAC,2BAEE9B,GACF5e,EAAgBC,EAAUW,WAAW/E,GAAI+iB,EAC3C,EAGEkE,GAAsB,EACxB7D,eACAjf,kBACA0gB,yBAAyB,IAAMnB,OAE/B,MAAMwD,GAAuB,OAAQ9D,GAC/B+D,GAA0B,OAAQhjB,GACxC,IAAIga,EAAW,KACf,MAAMiJ,EAAYvV,IACfsM,GAA6GxgB,IAC9G,MAAM,uBACJinB,EAAsB,cACtBD,GACExG,EACJ1a,GAAO,CACLoO,QACAuR,aAAc8D,EACd/iB,gBAAiBgjB,EACjBxC,gBACAC,yBACAC,0BACA,EAmCJ,MAAO,CACLtkB,MAlCcsR,IACdtR,KACE4d,GAA0HxgB,IAC5H,MAAMgnB,EAAgBY,KAAKhR,MAC3B,IAAI8S,GAAkB,EACtB,MAAMC,EAAqB,KACzBD,GAAkB,CAAI,EAExB5jB,GAAO,CACLoO,QACA8S,cAAe,EACfC,wBAAwB,EACxBxB,aAAckE,EACdnjB,gBAAiBmjB,EACjBzC,2BAEF1G,EAAW,CACTwG,gBACAC,uBAAwByC,GAE1BtR,KACIsR,GACFD,EAAUvV,EACZ,EAYAuK,KAVW,KACN+B,IAGL+I,EAAqB7K,SACrB8K,EAAwB9K,SACxB8B,EAAW,KAAI,EAKf1a,OAAQ2jB,EACT,EAGCG,GAAqB,EACvBxO,OACA5U,kBACAif,mBAEA,MAMMoE,EAA+B,CAACpjB,EAAW2e,KAC/C,IAAK+D,GAAmB1iB,EAAW2e,GACjC,OAAOA,EAET,MAAM0D,EAtLkB,EAACriB,EAAW2e,KACtC,MAAMngB,EAAQwB,EAAUxB,MACxB,OAAKA,GAGAkkB,GAAmB1iB,EAAW2e,GAG5BuD,GAAW,CAChB5hB,QAAS9B,EAAMa,OAAOiB,QACtB3B,IAAKH,EAAMa,OAAOV,IAClBggB,WARO,IASP,EA0KgB0E,CAAoBrjB,EAAW2e,GAC/C,IAAK0D,EAEH,OADAtiB,EAAgBC,EAAUW,WAAW/E,GAAI+iB,GAClC,KAET,MAAM2E,EAA4BtmB,EAAS2hB,EAAQ0D,GACnDtiB,EAAgBC,EAAUW,WAAW/E,GAAI0nB,GAEzC,OADkBtmB,EAAS2hB,EAAQ2E,EACnB,EAEZC,EAA4B,CAACrQ,EAAuBpP,EAAU6a,KAClE,IAAKzL,EACH,OAAOyL,EAET,IAAK8D,GAAgB3e,EAAU6a,GAC7B,OAAOA,EAET,MAAM0D,EA9Ne,EAACve,EAAU6a,KAClC,IAAK8D,GAAgB3e,EAAU6a,GAC7B,OAAO,KAET,MAAMhgB,EAAMmF,EAASzE,OAAOV,IACtB2B,EAAUwD,EAASzE,OAAOiB,QAChC,OAAO4hB,GAAW,CAChB5hB,UACA3B,MACAggB,UACA,EAoNgB6E,CAAiB1f,EAAU6a,GAC3C,IAAK0D,EAEH,OADArD,EAAaL,GACN,KAET,MAAM8E,EAAyBzmB,EAAS2hB,EAAQ0D,GAChDrD,EAAayE,GAEb,OADkBzmB,EAAS2hB,EAAQ8E,EACnB,EAoBlB,OAlBqBhW,IACnB,MAAM4O,EAAU5O,EAAMpD,kBACtB,IAAKgS,EACH,OAEF,MAAM/gB,EAAciS,GAAkBE,EAAMhM,QAC3CnG,GAAsI/B,IACvI,MAAMmqB,EAAqBN,EAA6B3V,EAAMG,WAAWpN,WAAWlF,GAAc+gB,GAClG,IAAKqH,EACH,OAEF,MAAM5f,EAAW2J,EAAM3J,SACjB6f,EAAkBJ,EAA0B9V,EAAMyF,sBAAuBpP,EAAU4f,GACpFC,GAlDc,EAAClW,EAAOvE,KAC3B,MAAMC,EAAStM,EAAI4Q,EAAMnN,QAAQ6I,OAAO+E,UAAWhF,GACnDyL,EAAK,CACHxL,UACA,EAiDFya,CAAanW,EAAOkW,EAAgB,CAEnB,EAGjBE,GAAqB,EACvB9jB,kBACAif,eACArK,OACA8L,6BAEA,MAAMqD,EAAgBjB,GAAoB,CACxC7D,eACAjf,kBACA0gB,2BAEIsD,EAAaZ,GAAmB,CACpCxO,OACAqK,eACAjf,oBAqBF,MALiB,CACfV,OAfaoO,IACegT,IACJV,UAA4B,aAAhBtS,EAAMW,QAGf,UAAvBX,EAAM2D,aAIL3D,EAAMpD,mBAGX0Z,EAAWtW,GANTqW,EAAczkB,OAAOoO,GAMN,EAIjBtR,MAAO2nB,EAAc3nB,MACrB6b,KAAM8L,EAAc9L,KAEP,EAGjB,MAAMgM,GAAS,WACTC,GAAa,MACjB,MAAMC,EAAO,GAAGF,iBAChB,MAAO,CACLE,OACApoB,YAAa,GAAGooB,iBAChBC,UAAW,GAAGD,eAEjB,EAPkB,GAQb9iB,GAAY,MAChB,MAAM8iB,EAAO,GAAGF,eAChB,MAAO,CACLE,OACAC,UAAW,GAAGD,eACdtoB,GAAI,GAAGsoB,OAEV,EAPiB,GAQZlkB,GAAY,MAChB,MAAMkkB,EAAO,GAAGF,eAChB,MAAO,CACLE,OACAC,UAAW,GAAGD,eACdtoB,GAAI,GAAGsoB,OAEV,EAPiB,GAQZE,GAAkB,CACtBD,UAAW,GAAGH,kCAIVK,GAAY,CAACC,EAAOC,IAAaD,EAAM/rB,KAAIisB,IAC/C,MAAMlnB,EAAQknB,EAAKC,OAAOF,GAC1B,OAAKjnB,EAGE,GAAGknB,EAAKE,cAAcpnB,MAFpB,EAE6B,IACrCqnB,KAAK,KAER,IAAIC,GAAcT,IAChB,MAAMU,GAVgBC,EAUcX,EAVHY,GAAa,IAAIA,MAAcD,OAA1CA,MAWtB,MAAME,EAAe,MACnB,MAAMC,EAAa,2DAInB,MAAO,CACLP,SAAUG,EAAYZ,GAAWE,WACjCM,OAAQ,CACNS,OAAQ,mJAKRhJ,QAAS+I,EACTlL,SAjBgB,wBAkBhBoL,cAAeF,GAGpB,EAlBoB,GAoDfX,EAAQ,CAjCM,MAClB,MAAMc,EAAa,uBACHzP,GAAYJ,qBAE5B,MAAO,CACLmP,SAAUG,EAAYzjB,GAAU+iB,WAChCM,OAAQ,CACN1K,SAAUqL,EACVD,cAAeC,EACfC,WAAYD,GAGjB,EAZmB,GAiCQJ,EApBR,CAClBN,SAAUG,EAAY7kB,GAAUmkB,WAChCM,OAAQ,CACNS,OAAQ,2BAGC,CACXR,SAAU,OACVD,OAAQ,CACN1K,SAAU,6OAYd,MAAO,CACLmL,OAAQb,GAAUC,EAAO,UACzBpI,QAASmI,GAAUC,EAAO,WAC1BvK,SAAUsK,GAAUC,EAAO,YAC3Ba,cAAed,GAAUC,EAAO,iBAChCe,WAAYhB,GAAUC,EAAO,cAC9B,EAGH,MAAMgB,GAA8C,oBAAX5qB,aAAqD,IAApBA,OAAOmd,eAAqE,IAAlCnd,OAAOmd,SAAS0N,cAAgC,EAAAC,gBAAkB,EAAAC,UAEhLC,GAAU,KACd,MAAMC,EAAO9N,SAAS+N,cAAc,QAEpC,OADCD,GAA+GpsB,IACzGosB,CAAI,EAEPE,GAAgBC,IACpB,MAAM3tB,EAAK0f,SAAS0N,cAAc,SAKlC,OAJIO,GACF3tB,EAAG4tB,aAAa,QAASD,GAE3B3tB,EAAGL,KAAO,WACHK,CAAE,EA6DX,SAAS6tB,GAAiBC,EAAYvB,GACpC,OAAOwB,MAAMC,KAAKF,EAAWD,iBAAiBtB,GAChD,CAEA,IAAI0B,GAAkBjuB,GAChBA,GAAMA,EAAGkuB,eAAiBluB,EAAGkuB,cAAcC,YACtCnuB,EAAGkuB,cAAcC,YAEnB5rB,OAGT,SAAS6rB,GAAcpuB,GACrB,OAAOA,aAAciuB,GAAgBjuB,GAAIquB,WAC3C,CAEA,SAASC,GAAetC,EAAWroB,GACjC,MAAM4oB,EAAW,IAAIT,GAAWE,cAAcA,MACxCuC,EAAWV,GAAiBnO,SAAU6M,GAC5C,IAAKgC,EAASthB,OAEZ,OAAO,KAET,MAAMuhB,EAASD,EAASjhB,MAAKtN,GACpBA,EAAGyuB,aAAa3C,GAAWnoB,eAAiBA,IAErD,OAAK6qB,GAIAJ,GAAcI,GAIZA,EANE,IAOX,CA6EA,SAASE,KACP,MAAMtO,EAAU,CACd1X,WAAY,CAAC,EACbL,WAAY,CAAC,GAETsmB,EAAc,GAWpB,SAASC,EAAO7sB,GACV4sB,EAAY1hB,QACd0hB,EAAY7tB,SAAQ+tB,GAAMA,EAAG9sB,IAEjC,CACA,SAAS+sB,EAAkBrrB,GACzB,OAAO2c,EAAQ1X,WAAWjF,IAAO,IACnC,CA+CA,SAASsrB,EAAkBtrB,GACzB,OAAO2c,EAAQ/X,WAAW5E,IAAO,IACnC,CA8BA,MAAO,CACLwF,UA1EmB,CACnB+lB,SAAUzO,IACRH,EAAQ1X,WAAW6X,EAAM/X,WAAW/E,IAAM8c,EAC1CqO,EAAO,CACLjvB,KAAM,WACNwF,MAAOob,GACP,EAEJrc,OAAQ,CAACqc,EAAOjU,KACd,MAAMnE,EAAUiY,EAAQ1X,WAAW4D,EAAK9D,WAAW/E,IAC9C0E,GAGDA,EAAQ8mB,WAAa1O,EAAM0O,kBAGxB7O,EAAQ1X,WAAW4D,EAAK9D,WAAW/E,IAC1C2c,EAAQ1X,WAAW6X,EAAM/X,WAAW/E,IAAM8c,EAAK,EAEjD2O,WAAY3O,IACV,MAAM5c,EAAc4c,EAAM/X,WAAW/E,GAC/B0E,EAAU2mB,EAAkBnrB,GAC7BwE,GAGDoY,EAAM0O,WAAa9mB,EAAQ8mB,kBAGxB7O,EAAQ1X,WAAW/E,GACtByc,EAAQ/X,WAAWkY,EAAM/X,WAAWnF,cACtCurB,EAAO,CACLjvB,KAAM,UACNwF,MAAOob,IAEX,EAEFsF,QAzCF,SAA0BpiB,GACxB,MAAM8c,EAAQuO,EAAkBrrB,GAEhC,OADC8c,GAAkHnf,IAC5Gmf,CACT,EAsCE4O,SAAUL,EACVvI,OAAQ9iB,GAAMoK,QAAQihB,EAAkBrrB,IACxCgiB,aAAc9lB,GAAQiJ,OAAOC,OAAOuX,EAAQ1X,YAAYM,QAAOuX,GAASA,EAAM/X,WAAW7I,OAASA,KAoClGkI,UA1BmB,CACnBmnB,SAAUzO,IACRH,EAAQ/X,WAAWkY,EAAM/X,WAAW/E,IAAM8c,CAAK,EAEjD2O,WAAY3O,IACV,MAAMpY,EAAU4mB,EAAkBxO,EAAM/X,WAAW/E,IAC9C0E,GAGDoY,EAAM0O,WAAa9mB,EAAQ8mB,iBAGxB7O,EAAQ/X,WAAWkY,EAAM/X,WAAW/E,GAAG,EAEhDoiB,QAnBF,SAA0BpiB,GACxB,MAAM8c,EAAQwO,EAAkBtrB,GAEhC,OADC8c,GAAkHnf,IAC5Gmf,CACT,EAgBE4O,SAAUJ,EACVxI,OAAQ9iB,GAAMoK,QAAQkhB,EAAkBtrB,IACxCgiB,aAAc9lB,GAAQiJ,OAAOC,OAAOuX,EAAQ/X,YAAYW,QAAOuX,GAASA,EAAM/X,WAAW7I,OAASA,KAUlG+mB,UAnGF,SAAmBmI,GAEjB,OADAF,EAAYjiB,KAAKmiB,GACV,WACL,MAAM7rB,EAAQ2rB,EAAYlhB,QAAQohB,IACnB,IAAX7rB,GAGJ2rB,EAAYnO,OAAOxd,EAAO,EAC5B,CACF,EA2FEumB,MATF,WACEnJ,EAAQ1X,WAAa,CAAC,EACtB0X,EAAQ/X,WAAa,CAAC,EACtBsmB,EAAY1hB,OAAS,CACvB,EAOF,CAYA,IAAImiB,GAAe,gBAAoB,MAEnCC,GAAiB,KACnB,MAAMC,EAAO5P,SAAS4P,KAEtB,OADCA,GAA+FluB,IACzFkuB,CAAI,EAGb,MAAMC,GAAiB,CACrBxsB,SAAU,WACV0D,MAAO,MACPC,OAAQ,MACR2I,OAAQ,OACRmgB,OAAQ,IACRC,QAAS,IACTC,SAAU,SACVtpB,KAAM,gBACN,YAAa,eAGTupB,GAAQ3D,GAAa,oBAAoBA,IA4C/C,MAAM4D,GAAW,CACfC,UAAW,MAEb,SAASC,GAAYjE,EAAQvrB,EAAUsvB,IACrC,MAAMnsB,EAAK,UACX,OAAO,SAAQ,IAAM,GAAGooB,IAASvrB,EAAQuvB,YAAYpsB,KAAM,CAACnD,EAAQuvB,UAAWhE,EAAQpoB,GACzF,CAmCA,IAAIssB,GAAa,gBAAoB,MAmFrC,SAASC,GAAOC,GACV,CAGN,CAEA,SAASC,GAAmBtvB,EAAIuvB,GAC9BH,IAaF,CASA,SAASI,GAAYjoB,GACnB,MAAMkoB,GAAM,IAAAC,QAAOnoB,GAInB,OAHA,IAAAmlB,YAAU,KACR+C,EAAIloB,QAAUA,CAAO,IAEhBkoB,CACT,CAqCA,SAASpuB,GAAWqT,GAClB,MAAoB,SAAhBA,EAAMW,OAAoC,mBAAhBX,EAAMW,OAG7BX,EAAMrT,UACf,CAEA,MAAMsuB,GAAM,EACNC,GAAQ,GAGRC,GAAS,GACTC,GAAW,GACXjpB,GAAM,GACNkO,GAAO,GAMPgb,GAAgB,CACpB,CAACH,KAAQ,EACT,CAACD,KAAM,GAET,IAAIK,GAA2B7uB,IACzB4uB,GAAc5uB,EAAM8uB,UACtB9uB,EAAMK,gBACR,EAGF,MAAM0uB,GAAqB,MACzB,MAAM/E,EAAO,mBACb,GAAwB,oBAAbrM,SACT,OAAOqM,EAIT,MAFmB,CAACA,EAAM,KAAKA,IAAQ,SAASA,IAAQ,MAAMA,IAAQ,IAAIA,KAC7Cze,MAAK3M,GAAa,KAAKA,MAAe+e,YAC/CqM,CACrB,EAR0B,GAe3B,MAAMgF,GAAS,CACbpxB,KAAM,QAER,SAASqxB,IAAmB,OAC1BlR,EAAM,UACNpF,EAAS,SACTuW,EAAQ,SACRC,IAEA,MAAO,CAAC,CACNvwB,UAAW,YACXC,GAAImB,IACF,MAAM,OACJovB,EAAM,QACNC,EAAO,QACPC,GACEtvB,EACJ,GAtBgB,IAsBZovB,EACF,OAEF,MAAMnsB,EAAQ,CACZR,EAAG4sB,EACH3sB,EAAG4sB,GAECpb,EAAQgb,IACd,GAAmB,aAAfhb,EAAMtW,KAGR,OAFAoC,EAAMK,sBACN6T,EAAMqb,QAAQ9U,KAAKxX,GAGJ,YAAfiR,EAAMtW,MAAmGyB,IAC3G,MAAMmwB,EAAUtb,EAAMjR,MACtB,GAnCkCsL,EAmCEihB,EAnCQppB,EAmCCnD,IAlC1CM,KAAKksB,IAAIrpB,EAAQ3D,EAAI8L,EAAS9L,IAFV,GAEwCc,KAAKksB,IAAIrpB,EAAQ1D,EAAI6L,EAAS7L,IAFtE,GAqCrB,OApCR,IAAwC6L,EAAUnI,EAsC5CpG,EAAMK,iBACN,MAAMkvB,EAAUrb,EAAMqb,QAAQG,UAAUzsB,GACxCksB,EAAS,CACPvxB,KAAM,WACN2xB,WACA,GAEH,CACD3wB,UAAW,UACXC,GAAImB,IACF,MAAMkU,EAAQgb,IACK,aAAfhb,EAAMtW,MAIVoC,EAAMK,iBACN6T,EAAMqb,QAAQvU,KAAK,CACjB2U,sBAAsB,IAExBhX,KAPEoF,GAOS,GAEZ,CACDnf,UAAW,YACXC,GAAImB,IACsB,aAApBkvB,IAAWtxB,MACboC,EAAMK,iBAER0d,GAAQ,GAET,CACDnf,UAAW,UACXC,GAAImB,IAEF,GAAmB,YADLkvB,IACJtxB,KAIV,OA5GS,KA4GLoC,EAAM8uB,SACR9uB,EAAMK,sBACN0d,UAGF8Q,GAAyB7uB,GARvB+d,GAQ6B,GAEhC,CACDnf,UAAW,SACXC,GAAIkf,GACH,CACDnf,UAAW,SACXL,QAAS,CACPkf,SAAS,EACTC,SAAS,GAEX7e,GAAI,KACsB,YAApBqwB,IAAWtxB,MACbmgB,GACF,GAED,CACDnf,UAAW,uBACXC,GAAImB,IACF,MAAMkU,EAAQgb,IACG,SAAfhb,EAAMtW,MAAkGyB,IACtG6U,EAAMqb,QAAQK,0BAChB7R,IAGF/d,EAAMK,gBAAgB,GAEvB,CACDzB,UAAWmwB,GACXlwB,GAAIkf,GAER,CAsHA,SAAS8R,KAAU,CACnB,MAAMC,GAAiB,CACrB,CAACnB,KAAW,EACZ,CAACD,KAAS,EACV,CAAC9a,KAAO,EACR,CAAClO,KAAM,GAET,SAASqqB,GAAoBR,EAASzR,GACpC,SAASC,IACPD,IACAyR,EAAQxR,QACV,CAKA,MAAO,CAAC,CACNnf,UAAW,UACXC,GAAImB,GAxRO,KAyRLA,EAAM8uB,SACR9uB,EAAMK,sBACN0d,KA1RM,KA6RJ/d,EAAM8uB,SACR9uB,EAAMK,iBAZVyd,SACAyR,EAAQvU,QA3QM,KA0RRhb,EAAM8uB,SACR9uB,EAAMK,sBACNkvB,EAAQ5U,YA9RA,KAiSN3a,EAAM8uB,SACR9uB,EAAMK,sBACNkvB,EAAQ7U,UAlSG,KAqST1a,EAAM8uB,SACR9uB,EAAMK,sBACNkvB,EAAQ3U,aAzSE,KA4SR5a,EAAM8uB,SACR9uB,EAAMK,sBACNkvB,EAAQ1U,iBAGNiV,GAAe9vB,EAAM8uB,SACvB9uB,EAAMK,iBAGRwuB,GAAyB7uB,KAE1B,CACDpB,UAAW,YACXC,GAAIkf,GACH,CACDnf,UAAW,UACXC,GAAIkf,GACH,CACDnf,UAAW,QACXC,GAAIkf,GACH,CACDnf,UAAW,aACXC,GAAIkf,GACH,CACDnf,UAAW,SACXC,GAAIkf,GACH,CACDnf,UAAW,QACXC,GAAIkf,EACJxf,QAAS,CACPkf,SAAS,IAEV,CACD7e,UAAWmwB,GACXlwB,GAAIkf,GAER,CAqDA,MAAMiS,GAAO,CACXpyB,KAAM,QAuQR,MAAMqyB,GAAsB,CAAC,QAAS,SAAU,WAAY,SAAU,SAAU,WAAY,QAAS,SACrG,SAASC,GAAuBC,EAAQ/pB,GACtC,GAAe,MAAXA,EACF,OAAO,EAGT,GAD4B6pB,GAAoBG,SAAShqB,EAAQiqB,QAAQC,eAEvE,OAAO,EAET,MAAMzF,EAAYzkB,EAAQsmB,aAAa,mBACvC,MAAkB,SAAd7B,GAAsC,KAAdA,GAGxBzkB,IAAY+pB,GAGTD,GAAuBC,EAAQ/pB,EAAQmqB,cAChD,CACA,SAASC,GAA4BtpB,EAAWlH,GAC9C,MAAM0D,EAAS1D,EAAM0D,OACrB,QAAK2oB,GAAc3oB,IAGZwsB,GAAuBhpB,EAAWxD,EAC3C,CAEA,IAAI+sB,GAA6BxyB,IAAM,OAAQA,EAAGyyB,yBAAyB3jB,OAM3E,MAAM4jB,GAAuB,MAC3B,MAAM3G,EAAO,UACb,GAAwB,oBAAbrM,SACT,OAAOqM,EAIT,MAFmB,CAACA,EAAM,oBAAqB,yBACtBze,MAAKqlB,GAAQA,KAAQC,QAAQtxB,aACtCyqB,CACjB,EAR4B,GAS7B,SAAS8G,GAAgB7yB,EAAIusB,GAC3B,OAAU,MAANvsB,EACK,KAELA,EAAG0yB,IAAsBnG,GACpBvsB,EAEF6yB,GAAgB7yB,EAAGsyB,cAAe/F,EAC3C,CACA,SAASrc,GAAQlQ,EAAIusB,GACnB,OAAIvsB,EAAGkQ,QACElQ,EAAGkQ,QAAQqc,GAEbsG,GAAgB7yB,EAAIusB,EAC7B,CAKA,SAASuG,GAA+B9G,EAAWjqB,GACjD,MAAM0D,EAAS1D,EAAM0D,OACrB,MAlCiBzF,EAkCFyF,aAjCMwoB,GAAgBjuB,GAAI4yB,SAmCvC,OAAO,KApCX,IAAmB5yB,EAsCjB,MAAMusB,EATR,SAAqBP,GACnB,MAAO,IAAIF,GAAWE,cAAcA,KACtC,CAOmBU,CAAYV,GACvBwC,EAASte,GAAQzK,EAAQ8mB,GAC/B,OAAKiC,GAGAJ,GAAcI,GAIZA,EANE,IAOX,CAyBA,SAASpsB,GAAeL,GACtBA,EAAMK,gBACR,CACA,SAASwd,IAAS,SAChBmT,EAAQ,MACR9c,EAAK,aACL+c,EAAY,WACZC,IAEA,QAAKD,KAcDD,IAAa9c,CAiBnB,CACA,SAASid,IAAS,QAChBC,EAAO,MACPnT,EAAK,SACLsF,EAAQ,YACR3hB,IAEA,GAAIwvB,EAAQC,YACV,OAAO,EAET,MAAM7S,EAAQ+E,EAASrc,UAAUkmB,SAASxrB,GAC1C,QAAK4c,MAIAA,EAAMjgB,QAAQmR,aAGdmV,GAAa5G,EAAM5B,WAAYza,GAItC,CACA,SAAS0vB,IAAS,QAChBF,EAAO,UACPnH,EAAS,MACThM,EAAK,SACLsF,EAAQ,YACR3hB,EAAW,gBACX2vB,EAAe,YACfC,IAQA,IANoBL,GAAS,CAC3BC,UACAnT,QACAsF,WACA3hB,gBAGA,OAAO,KAET,MAAM4c,EAAQ+E,EAASrc,UAAU4c,QAAQliB,GACnC3D,EAlGR,SAAuBgsB,EAAWroB,GAChC,MAAM4oB,EAAW,IAAItjB,GAAU+iB,cAAcA,MAEvCwH,EADW3F,GAAiBnO,SAAU6M,GACfjf,MAAKtN,GACzBA,EAAGyuB,aAAaxlB,GAAUxF,MAAQE,IAE3C,OAAK6vB,GAGApF,GAAcoF,GAIZA,EANE,IAOX,CAoFaC,CAAczH,EAAWzL,EAAM/X,WAAW/E,IACrD,IAAKzD,EAEH,OAAO,KAET,GAAIuzB,IAAgBhT,EAAMjgB,QAAQozB,4BAA8BnB,GAA4BvyB,EAAIuzB,GAC9F,OAAO,KAET,MAAMI,EAAOR,EAAQS,MAAMN,GAAmBxzB,GAC9C,IAAImW,EAAQ,WACZ,SAAS4d,IACP,OAAOtT,EAAMjgB,QAAQqxB,uBACvB,CACA,SAASqB,IACP,OAAOG,EAAQvT,SAAS+T,EAC1B,CAWA,MAAMG,EAVN,SAAqBf,EAAUgB,GACzBnU,GAAS,CACXmT,WACA9c,QACA+c,eACAC,YAAY,KAEZjT,EAAM3B,SAAS0V,IAEnB,EAC4Cl0B,KAAK,KAAM,YACvD,SAASykB,EAAK3iB,GACZ,SAAS+Y,IACPyY,EAAQa,UACR/d,EAAQ,WACV,CAOA,SAASuD,EAAOlV,EAAQhE,EAAU,CAChCoxB,sBAAsB,IAGtB,GADA/vB,EAAKsyB,UACD3zB,EAAQoxB,qBAAsB,CAChC,MAAM3wB,EAAShB,EAAWwC,OAAQ,CAAC,CACjC5B,UAAW,QACXC,GAAIwB,GACJ9B,QAAS,CACP2iB,MAAM,EACNzD,SAAS,EACTC,SAAS,MAGba,WAAWvf,EACb,CACA2Z,IACAsF,EAAM3B,SAAStB,GAAK,CAClBzY,WAEJ,CACA,MA3Bc,aAAV2R,IACFyE,IAC4FtZ,KAE9F4e,EAAM3B,SAASnC,GAAOva,EAAKuyB,iBAC3Bje,EAAQ,WAsBD,CACL2J,SAAU,IAAMA,GAAS,CACvBmT,SAAU,WACV9c,QACA+c,eACAC,YAAY,IAEdtB,wBAAyBkC,EACzB9W,KAAMzc,GAAWkZ,EAAO,OAAQlZ,GAChCwf,OAAQxf,GAAWkZ,EAAO,SAAUlZ,MACjCqB,EAAK2vB,QAEZ,CA+DA,MAZgB,CACd1R,SAAU,IAAMA,GAAS,CACvBmT,SAAU,WACV9c,QACA+c,eACAC,YAAY,IAEdtB,wBAAyBkC,EACzBpC,UA1DF,SAAmBxf,GACjB,MAAMkiB,GAAS,QAAQnjB,IACrB8iB,GAAwB,IAAMtX,GAAK,CACjCxL,YACC,IAaL,MAAO,IAXKsT,EAAK,CACf4P,eAAgB,CACdzwB,GAAIE,EACJsO,kBACAgH,aAAc,SAEhBgb,QAAS,IAAME,EAAOrU,SACtBwR,QAAS,CACP9U,KAAM2X,KAKR3X,KAAM2X,EAEV,EAsCEC,SArCF,WACE,MAAM9C,EAAU,CACd7U,OAAQ,IAAMqX,EAAwBrX,IACtCE,UAAW,IAAMmX,EAAwBnX,IACzCD,SAAU,IAAMoX,EAAwBpX,IACxCE,SAAU,IAAMkX,EAAwBlX,KAE1C,OAAO0H,EAAK,CACV4P,eAAgB,CACdzwB,GAAIE,EACJsO,gBAAiBugB,GAA2BxyB,GAC5CiZ,aAAc,QAEhBgb,QAASn0B,EACTwxB,WAEJ,EAsBE3O,MArBF,WACwB/C,GAAS,CAC7BmT,SAAU,WACV9c,QACA+c,eACAC,YAAY,KAGZE,EAAQa,SAEZ,EAcF,CACA,MAAMK,GAAiB,CAj0BvB,SAAwBC,GACtB,MAAMC,GAAW,IAAAjE,QAAOS,IAClByD,GAAkB,IAAAlE,QAAOxwB,GACzB20B,GAAsB,SAAQ,KAAM,CACxC9zB,UAAW,YACXC,GAAI,SAAqBmB,GACvB,GAAIA,EAAM2yB,iBACR,OAEF,GA3HgB,IA2HZ3yB,EAAMovB,OACR,OAEF,GAAIpvB,EAAM4yB,SAAW5yB,EAAM6yB,SAAW7yB,EAAM8yB,UAAY9yB,EAAM+yB,OAC5D,OAEF,MAAMnxB,EAAc2wB,EAAIS,uBAAuBhzB,GAC/C,IAAK4B,EACH,OAEF,MAAM2tB,EAAUgD,EAAIU,WAAWrxB,EAAakc,EAAM,CAChD0T,YAAaxxB,IAEf,IAAKuvB,EACH,OAEFvvB,EAAMK,iBACN,MAAM4C,EAAQ,CACZR,EAAGzC,EAAMqvB,QACT3sB,EAAG1C,EAAMsvB,SAEXmD,EAAgBrsB,UAChB8sB,EAAiB3D,EAAStsB,EAC5B,KACE,CAACsvB,IACCY,GAA2B,SAAQ,KAAM,CAC7Cv0B,UAAW,4BACXC,GAAImB,IACF,GAAIA,EAAM2yB,iBACR,OAEF,MAAMjxB,EAAK6wB,EAAIS,uBAAuBhzB,GACtC,IAAK0B,EACH,OAEF,MAAMnD,EAAUg0B,EAAIa,wBAAwB1xB,GACvCnD,IAGDA,EAAQqxB,yBAGP2C,EAAIc,WAAW3xB,IAGpB1B,EAAMK,iBAAgB,KAEtB,CAACkyB,IACCe,GAAmB,SAAY,WAKnCb,EAAgBrsB,QAAUpI,EAAWwC,OAAQ,CAAC2yB,EAA0BT,GAJxD,CACdjV,SAAS,EACTC,SAAS,GAGb,GAAG,CAACyV,EAA0BT,IACxB5U,GAAO,SAAY,KAEF,SADL0U,EAASpsB,QACbxI,OAGZ40B,EAASpsB,QAAU4oB,GACnByD,EAAgBrsB,UAChBktB,IAAkB,GACjB,CAACA,IACEvV,GAAS,SAAY,KACzB,MAAM7J,EAAQse,EAASpsB,QACvB0X,IACmB,aAAf5J,EAAMtW,MACRsW,EAAMqb,QAAQxR,OAAO,CACnB4R,sBAAsB,IAGP,YAAfzb,EAAMtW,MACRsW,EAAMqb,QAAQ3O,OAChB,GACC,CAAC9C,IACEyV,GAAsB,SAAY,WACtC,MAIMr1B,EAAW+wB,GAAmB,CAClClR,SACApF,UAAWmF,EACXoR,SAAU,IAAMsD,EAASpsB,QACzB+oB,SAAUjb,IACRse,EAASpsB,QAAU8N,CAAK,IAG5Bue,EAAgBrsB,QAAUpI,EAAWwC,OAAQtC,EAZ7B,CACdwf,SAAS,EACTD,SAAS,GAWb,GAAG,CAACM,EAAQD,IACNoV,GAAmB,SAAY,SAA0B3D,EAAStsB,GAC1C,SAA1BuvB,EAASpsB,QAAQxI,MAA4HyB,IAC/ImzB,EAASpsB,QAAU,CACjBxI,KAAM,UACNqF,QACAssB,WAEFgE,GACF,GAAG,CAACA,IACJnI,IAA0B,WAExB,OADAkI,IACO,WACLb,EAAgBrsB,SAClB,CACF,GAAG,CAACktB,GACN,EAmFA,SAA2Bf,GACzB,MAAME,GAAkB,IAAAlE,QAAOsB,IACzB6C,GAAsB,SAAQ,KAAM,CACxC9zB,UAAW,UACXC,GAAI,SAAmBmB,GACrB,GAAIA,EAAM2yB,iBACR,OAEF,GA9VQ,KA8VJ3yB,EAAM8uB,QACR,OAEF,MAAMltB,EAAc2wB,EAAIS,uBAAuBhzB,GAC/C,IAAK4B,EACH,OAEF,MAAM4xB,EAAUjB,EAAIU,WAAWrxB,EAAakc,EAAM,CAChD0T,YAAaxxB,IAEf,IAAKwzB,EACH,OAEFxzB,EAAMK,iBACN,IAAIozB,GAAc,EAClB,MAAMlE,EAAUiE,EAAQnB,WAExB,SAASvU,IACN2V,GAAqIp0B,IACtIo0B,GAAc,EACdhB,EAAgBrsB,UAChBktB,GACF,CANAb,EAAgBrsB,UAOhBqsB,EAAgBrsB,QAAUpI,EAAWwC,OAAQuvB,GAAoBR,EAASzR,GAAO,CAC/EJ,SAAS,EACTD,SAAS,GAEb,KACE,CAAC8U,IACCe,GAAmB,SAAY,WAKnCb,EAAgBrsB,QAAUpI,EAAWwC,OAAQ,CAACkyB,GAJ9B,CACdjV,SAAS,EACTC,SAAS,GAGb,GAAG,CAACgV,IACJtH,IAA0B,WAExB,OADAkI,IACO,WACLb,EAAgBrsB,SAClB,CACF,GAAG,CAACktB,GACN,EA8HA,SAAwBf,GACtB,MAAMC,GAAW,IAAAjE,QAAOyB,IAClByC,GAAkB,IAAAlE,QAAOxwB,GACzBmxB,GAAW,SAAY,WAC3B,OAAOsD,EAASpsB,OAClB,GAAG,IACG+oB,GAAW,SAAY,SAAkBjb,GAC7Cse,EAASpsB,QAAU8N,CACrB,GAAG,IACGwe,GAAsB,SAAQ,KAAM,CACxC9zB,UAAW,aACXC,GAAI,SAAsBmB,GACxB,GAAIA,EAAM2yB,iBACR,OAEF,MAAM/wB,EAAc2wB,EAAIS,uBAAuBhzB,GAC/C,IAAK4B,EACH,OAEF,MAAM2tB,EAAUgD,EAAIU,WAAWrxB,EAAakc,EAAM,CAChD0T,YAAaxxB,IAEf,IAAKuvB,EACH,OAEF,MAAMmE,EAAQ1zB,EAAM2zB,QAAQ,IACtB,QACJtE,EAAO,QACPC,GACEoE,EACEzwB,EAAQ,CACZR,EAAG4sB,EACH3sB,EAAG4sB,GAELmD,EAAgBrsB,UAChB8sB,EAAiB3D,EAAStsB,EAC5B,KACE,CAACsvB,IACCe,GAAmB,SAAY,WAKnCb,EAAgBrsB,QAAUpI,EAAWwC,OAAQ,CAACkyB,GAJ9B,CACdhV,SAAS,EACTD,SAAS,GAGb,GAAG,CAACiV,IACE5U,GAAO,SAAY,KACvB,MAAM1X,EAAUosB,EAASpsB,QACJ,SAAjBA,EAAQxI,OAGS,YAAjBwI,EAAQxI,MACVihB,aAAazY,EAAQwtB,kBAEvBzE,EAASa,IACTyC,EAAgBrsB,UAChBktB,IAAkB,GACjB,CAACA,EAAkBnE,IAChBpR,GAAS,SAAY,KACzB,MAAM7J,EAAQse,EAASpsB,QACvB0X,IACmB,aAAf5J,EAAMtW,MACRsW,EAAMqb,QAAQxR,OAAO,CACnB4R,sBAAsB,IAGP,YAAfzb,EAAMtW,MACRsW,EAAMqb,QAAQ3O,OAChB,GACC,CAAC9C,IACEyV,GAAsB,SAAY,WACtC,MAAMh1B,EAAU,CACdmf,SAAS,EACTD,SAAS,GAEL7d,EAAO,CACXme,SACApF,UAAWmF,EACXoR,YAEI2E,EAAe71B,EAAWwC,OAtKpC,UAA2B,OACzBud,EAAM,UACNpF,EAAS,SACTuW,IAEA,MAAO,CAAC,CACNtwB,UAAW,YACXL,QAAS,CACPmf,SAAS,GAEX7e,GAAImB,IACF,MAAMkU,EAAQgb,IACd,GAAmB,aAAfhb,EAAMtW,KAER,YADAmgB,IAGF7J,EAAM4f,UAAW,EACjB,MAAM,QACJzE,EAAO,QACPC,GACEtvB,EAAM2zB,QAAQ,GACZ1wB,EAAQ,CACZR,EAAG4sB,EACH3sB,EAAG4sB,GAELtvB,EAAMK,iBACN6T,EAAMqb,QAAQ9U,KAAKxX,EAAM,GAE1B,CACDrE,UAAW,WACXC,GAAImB,IACF,MAAMkU,EAAQgb,IACK,aAAfhb,EAAMtW,MAIVoC,EAAMK,iBACN6T,EAAMqb,QAAQvU,KAAK,CACjB2U,sBAAsB,IAExBhX,KAPEoF,GAOS,GAEZ,CACDnf,UAAW,cACXC,GAAImB,IACsB,aAApBkvB,IAAWtxB,MAIfoC,EAAMK,iBACN0d,KAJEA,GAIM,GAET,CACDnf,UAAW,mBACXC,GAAImB,IACF,MAAMkU,EAAQgb,IACG,SAAfhb,EAAMtW,MAAyEyB,IACjF,MAAMq0B,EAAQ1zB,EAAM2zB,QAAQ,GAC5B,IAAKD,EACH,OAGF,KADqBA,EAAMK,OA9FL,KAgGpB,OAEF,MAAMC,EAAgB9f,EAAMqb,QAAQK,0BACpC,GAAmB,YAAf1b,EAAMtW,KAMV,OAAIo2B,EACE9f,EAAM4f,cACR9zB,EAAMK,sBAGR0d,SAGF/d,EAAMK,iBAbA2zB,GACFjW,GAYkB,GAEvB,CACDnf,UAAWmwB,GACXlwB,GAAIkf,GAER,CAgF4CkW,CAAkBr0B,GAAOrB,GAC3D21B,EAAel2B,EAAWwC,OAvMpC,UAA2B,OACzBud,EAAM,SACNmR,IAEA,MAAO,CAAC,CACNtwB,UAAW,oBACXC,GAAIkf,GACH,CACDnf,UAAW,SACXC,GAAIkf,GACH,CACDnf,UAAW,cACXC,GAAImB,IACFA,EAAMK,gBAAgB,GAEvB,CACDzB,UAAW,UACXC,GAAImB,IACsB,aAApBkvB,IAAWtxB,MAlaN,KAsaLoC,EAAM8uB,SACR9uB,EAAMK,iBAER0d,KANEA,GAMM,GAET,CACDnf,UAAWmwB,GACXlwB,GAAIkf,GAER,CAwK4CoW,CAAkBv0B,GAAOrB,GACjEk0B,EAAgBrsB,QAAU,WACxBytB,IACAK,GACF,CACF,GAAG,CAACnW,EAAQmR,EAAUpR,IAChBsW,GAAgB,SAAY,WAChC,MAAMlgB,EAAQgb,IACG,YAAfhb,EAAMtW,MAAmIyB,IAC3I,MAAMkwB,EAAUrb,EAAMqb,QAAQG,UAAUxb,EAAMjR,OAC9CksB,EAAS,CACPvxB,KAAM,WACN2xB,UACAuE,UAAU,GAEd,GAAG,CAAC5E,EAAUC,IACR+D,GAAmB,SAAY,SAA0B3D,EAAStsB,GAChD,SAApBisB,IAAWtxB,MAA4HyB,IACzI,MAAMu0B,EAAmBrV,WAAW6V,EA3Nf,KA4NrBjF,EAAS,CACPvxB,KAAM,UACNqF,QACAssB,UACAqE,qBAEFL,GACF,GAAG,CAACA,EAAqBrE,EAAUC,EAAUiF,IAC7ChJ,IAA0B,WAExB,OADAkI,IACO,WACLb,EAAgBrsB,UAChB,MAAM8N,EAAQgb,IACK,YAAfhb,EAAMtW,OACRihB,aAAa3K,EAAM0f,kBACnBzE,EAASa,IAEb,CACF,GAAG,CAACd,EAAUoE,EAAkBnE,IAChC/D,IAA0B,WASxB,OAReptB,EAAWwC,OAAQ,CAAC,CACjC5B,UAAW,YACXC,GAAI,OACJN,QAAS,CACPmf,SAAS,EACTD,SAAS,KAIf,GAAG,GACL,GA2UA,SAAS4W,IAAiB,UACxBpK,EAAS,MACThM,EAAK,SACLsF,EAAQ,cACR+Q,EAAa,qBACbC,IAEA,MAAMC,EAAa,IAAKD,EAAuBjC,GAAiB,MAASgC,GAAiB,IACpFlD,GAAU,IAAAqD,WAAS,IAvgC3B,WACE,IAAI7C,EAAO,KAeX,SAASK,IACNL,GAA+GvyB,IAChHuyB,EAAO,IACT,CAOA,MAAO,CACLP,UAzBF,WACE,OAAOvlB,QAAQ8lB,EACjB,EAwBE/T,SAvBF,SAAkBza,GAChB,OAAOA,IAAUwuB,CACnB,EAsBEC,MArBF,SAAe6C,GACX9C,GAAgHvyB,IAClH,MAAMs1B,EAAU,CACdD,WAGF,OADA9C,EAAO+C,EACAA,CACT,EAeE1C,UACA2C,WAXF,WACMhD,IACFA,EAAK8C,UACLzC,IAEJ,EAQF,CAs+BiC4C,KAAU,GACnCC,GAAiB,SAAY,SAAwBtuB,EAAUJ,GAC/DlG,GAAWsG,KAActG,GAAWkG,IACtCgrB,EAAQwD,YAEZ,GAAG,CAACxD,IACJhG,IAA0B,WACxB,IAAI5kB,EAAWyX,EAAM5B,WAMrB,OALoB4B,EAAM0G,WAAU,KAClC,MAAMve,EAAU6X,EAAM5B,WACtByY,EAAetuB,EAAUJ,GACzBI,EAAWJ,CAAO,GAGtB,GAAG,CAACgrB,EAASnT,EAAO6W,IACpB1J,IAA0B,IACjBgG,EAAQwD,YACd,CAACxD,EAAQwD,aACZ,MAAMvB,GAAa,SAAYzxB,GACtBuvB,GAAS,CACdC,UACA7N,WACAtF,QACArc,iBAED,CAACwvB,EAAS7N,EAAUtF,IACjBgV,GAAa,SAAY,CAACrxB,EAAamzB,EAAWx2B,IAAY+yB,GAAS,CAC3EF,UACA7N,WACA0G,YACAhM,QACArc,cACA2vB,gBAAiBwD,GAAa,KAC9BvD,YAAajzB,GAAWA,EAAQizB,YAAcjzB,EAAQizB,YAAc,QAClE,CAACvH,EAAWmH,EAAS7N,EAAUtF,IAC7B+U,GAAyB,SAAYhzB,GA9R7C,SAA2CiqB,EAAWjqB,GACpD,MAAMysB,EAASsE,GAA+B9G,EAAWjqB,GACzD,OAAKysB,EAGEA,EAAOC,aAAa3C,GAAWnoB,aAF7B,IAGX,CAwRsDozB,CAAkC/K,EAAWjqB,IAAQ,CAACiqB,IACpGmJ,GAA0B,SAAY1xB,IAC1C,MAAM8c,EAAQ+E,EAASrc,UAAUkmB,SAAS1rB,GAC1C,OAAO8c,EAAQA,EAAMjgB,QAAU,IAAI,GAClC,CAACglB,EAASrc,YACP+tB,GAAiB,SAAY,WAC5B7D,EAAQC,cAGbD,EAAQwD,aACuB,SAA3B3W,EAAM5B,WAAWnI,OACnB+J,EAAM3B,SAASxB,MAEnB,GAAG,CAACsW,EAASnT,IACPiX,GAAgB,SAAY,IAAM9D,EAAQC,aAAa,CAACD,IACxDmB,GAAM,SAAQ,KAAM,CACxBc,aACAJ,aACAD,yBACAI,0BACA6B,iBACAC,mBACE,CAAC7B,EAAYJ,EAAYD,EAAwBI,EAAyB6B,EAAgBC,IAzY9FjH,KA2YA,IAAK,IAAIvd,EAAI,EAAGA,EAAI8jB,EAAWtpB,OAAQwF,IACrC8jB,EAAW9jB,GAAG6hB,EAElB,CAEA,MAAM4C,GAAmBr0B,IAAS,CAChCif,gBAAiBqV,KAMf,IAAAC,YAL+B,KACzBv0B,EAAMif,iBACRjf,EAAMif,gBAAgBqV,EACxB,GAE+B,EAEnCnV,kBAAmBnf,EAAMmf,kBACzBje,YAAalB,EAAMkB,YACnBK,UAAWvB,EAAMuB,UACjBH,aAAcpB,EAAMoB,eAEhBozB,GAA4Bx0B,IAAS,IACtCskB,MACAtkB,EAAM0lB,oBACTd,kBAAmB,IACdN,GAA2BM,qBAC3B5kB,EAAM0lB,uBAGb,SAAS+O,GAASC,GAEhB,OADCA,EAAQpvB,SAA2G/G,IAC7Gm2B,EAAQpvB,OACjB,CACA,SAASqvB,GAAI30B,GACX,MAAM,UACJmpB,EAAS,aACT3pB,EAAY,QACZo1B,EAAO,MACP9J,EAAK,4BACL7pB,GACEjB,EACE60B,GAAe,IAAApH,QAAO,MAvnC5BJ,KAynCA,MAAMyH,EAAevH,GAAYvtB,GAC3B4e,GAAgB,SAAY,IACzByV,GAAiBS,EAAaxvB,UACpC,CAACwvB,IACErP,GAAyB,SAAY,IAClC+O,GAA0BM,EAAaxvB,UAC7C,CAACwvB,IACE1W,EA9zCR,SAAsB+K,GACpB,MAAMvoB,GAAK,SAAQ,IAAMksB,GAAM3D,IAAY,CAACA,IACtCqE,GAAM,IAAAC,QAAO,MAsCnB,OArCA,IAAAhD,YAAU,WACR,MAAMttB,EAAK0f,SAAS0N,cAAc,OAOlC,OANAiD,EAAIloB,QAAUnI,EACdA,EAAGyD,GAAKA,EACRzD,EAAG4tB,aAAa,YAAa,aAC7B5tB,EAAG4tB,aAAa,cAAe,SAC/B,OAAS5tB,EAAG43B,MAAOrI,IACnBF,KAAiBwI,YAAY73B,GACtB,WACLsgB,YAAW,WACT,MAAMgP,EAAOD,KACTC,EAAK9a,SAASxU,IAChBsvB,EAAKwI,YAAY93B,GAEfA,IAAOqwB,EAAIloB,UACbkoB,EAAIloB,QAAU,KAElB,GACF,CACF,GAAG,CAAC1E,KACa,SAAY7D,IAC3B,MAAMI,EAAKqwB,EAAIloB,QACXnI,IACFA,EAAG+3B,YAAcn4B,EAYR,GACV,GAEL,CAqxCmBo4B,CAAahM,GACxBiM,EAtwCR,UAA8B,UAC5BjM,EAAS,KACTkM,IAEA,MAAMjJ,EAAWa,GAAY,cAAe,CAC1CD,UAAW,MAEPpsB,GAAK,SAAQ,IAbrB,UAAsB,UACpBuoB,EAAS,SACTiD,IAEA,MAAO,mBAAmBjD,KAAaiD,GACzC,CAQ2BkJ,CAAa,CACpCnM,YACAiD,cACE,CAACA,EAAUjD,IAcf,OAbA,IAAAsB,YAAU,WACR,MAAMttB,EAAK0f,SAAS0N,cAAc,OAKlC,OAJAptB,EAAGyD,GAAKA,EACRzD,EAAG+3B,YAAcG,EACjBl4B,EAAG43B,MAAMQ,QAAU,OACnB/I,KAAiBwI,YAAY73B,GACtB,WACL,MAAMsvB,EAAOD,KACTC,EAAK9a,SAASxU,IAChBsvB,EAAKwI,YAAY93B,EAErB,CACF,GAAG,CAACyD,EAAIy0B,IACDz0B,CACT,CA6uCwC40B,CAAqB,CACzDrM,YACAkM,KAAMp0B,IAEF8f,EA1nDR,SAAyBoI,EAAW2B,GAClC,MAAMrB,GAAS,SAAQ,IAAMG,GAAYT,IAAY,CAACA,IAChDsM,GAAY,IAAAhI,QAAO,MACnBiI,GAAa,IAAAjI,QAAO,MACpBkI,GAAkB,SAAY,QAAWzjB,IAC7C,MAAM/U,EAAKu4B,EAAWpwB,QACrBnI,GAAqHoB,IACtHpB,EAAG+3B,YAAchjB,CAAQ,IACvB,IACE0jB,GAAiB,SAAY1jB,IACjC,MAAM/U,EAAKs4B,EAAUnwB,QACpBnI,GAAqHoB,IACtHpB,EAAG+3B,YAAchjB,CAAQ,GACxB,IACHoY,IAA0B,MACrBmL,EAAUnwB,SAAYowB,EAAWpwB,UAAwG/G,IAC5I,MAAM2rB,EAASW,GAAcC,GACvB+K,EAAUhL,GAAcC,GAS9B,OARA2K,EAAUnwB,QAAU4kB,EACpBwL,EAAWpwB,QAAUuwB,EACrB3L,EAAOa,aAAa,GAAG/B,YAAiBG,GACxC0M,EAAQ9K,aAAa,GAAG/B,aAAkBG,GAC1CuB,KAAUsK,YAAY9K,GACtBQ,KAAUsK,YAAYa,GACtBD,EAAenM,EAAOS,QACtByL,EAAgBlM,EAAOvI,SAChB,KACL,MAAMra,EAAS2mB,IACb,MAAMloB,EAAUkoB,EAAIloB,QACnBA,GAA4G/G,IAC7GmsB,KAAUuK,YAAY3vB,GACtBkoB,EAAIloB,QAAU,IAAI,EAEpBuB,EAAO4uB,GACP5uB,EAAO6uB,EAAW,CACnB,GACA,CAAC5K,EAAO8K,EAAgBD,EAAiBlM,EAAOS,OAAQT,EAAOvI,QAASiI,IAC3E,MAAMpK,GAAW,SAAY,IAAM4W,EAAgBlM,EAAO1K,WAAW,CAAC4W,EAAiBlM,EAAO1K,WACxFkC,GAAW,SAAYxf,IAK3Bk0B,EAJe,SAAXl0B,EAIYgoB,EAAOY,WAHLZ,EAAOU,cAGS,GACjC,CAACwL,EAAiBlM,EAAOU,cAAeV,EAAOY,aAC5CnJ,GAAU,SAAY,KACrBwU,EAAWpwB,SAGhBqwB,EAAgBlM,EAAOvI,QAAQ,GAC9B,CAACyU,EAAiBlM,EAAOvI,UAM5B,OALgB,SAAQ,KAAM,CAC5BnC,WACAkC,WACAC,aACE,CAACnC,EAAUkC,EAAUC,GAE3B,CAikDuB4U,CAAgB3M,EAAW2B,GAC1CiL,GAAe,SAAY/d,IAC/Byc,GAASI,GAAcrZ,SAASxD,EAAO,GACtC,IACGge,GAAmB,SAAQ,KAAM,QAAmB,CACxD1c,wBACAE,yBACAC,4BACAC,mCACAH,uBACCwc,IAAe,CAACA,IACbtT,EA72CR,WACE,MAAMA,GAAW,QAAQoJ,GAAgB,IAMzC,OALA,IAAApB,YAAU,IACD,WACLhI,EAASiE,OACX,GACC,CAACjE,IACGA,CACT,CAq2CmBwT,GACXpV,GAAmB,SAAQ,IACxBoC,GAAuBR,EAAUuT,IACvC,CAACvT,EAAUuT,IACRzV,GAAe,SAAQ,IAAMsI,GAAmB,CACpD7E,gBACAjf,gBAAiB8b,EAAiB9b,gBAClC0gB,6BACG,QAAmB,CACpB9L,SACCoc,MACD,CAAClV,EAAiB9b,gBAAiBgxB,EAActQ,IAC/C3E,EAljDR,SAAyBqI,GACvB,MAAM+M,GAAa,IAAAzI,QAAO,CAAC,GACrB0I,GAAY,IAAA1I,QAAO,MACnB2I,GAAuB,IAAA3I,QAAO,MAC9B4I,GAAe,IAAA5I,SAAO,GACtBtB,GAAW,SAAY,SAAkBvrB,EAAIkhB,GACjD,MAAMpE,EAAQ,CACZ9c,KACAkhB,SAGF,OADAoU,EAAW5wB,QAAQ1E,GAAM8c,EAClB,WACL,MAAMH,EAAU2Y,EAAW5wB,QACXiY,EAAQ3c,KACR8c,UACPH,EAAQ3c,EAEnB,CACF,GAAG,IACG01B,GAAe,SAAY,SAAsBC,GACrD,MAAM5K,EAASF,GAAetC,EAAWoN,GACrC5K,GAAUA,IAAW9O,SAAS2Z,eAChC7K,EAAO7J,OAEX,GAAG,CAACqH,IACEtH,GAAiB,SAAY,SAAwBnc,EAAU+wB,GAC/DN,EAAU7wB,UAAYI,IACxBywB,EAAU7wB,QAAUmxB,EAExB,GAAG,IACG7U,GAA0B,SAAY,WACtCwU,EAAqB9wB,SAGpB+wB,EAAa/wB,UAGlB8wB,EAAqB9wB,QAAU+a,uBAAsB,KACnD+V,EAAqB9wB,QAAU,KAC/B,MAAMoxB,EAASP,EAAU7wB,QACrBoxB,GACFJ,EAAaI,EACf,IAEJ,GAAG,CAACJ,IACE3U,GAAiB,SAAY,SAAwB/gB,GACzDu1B,EAAU7wB,QAAU,KACpB,MAAMqxB,EAAU9Z,SAAS2Z,cACpBG,GAGDA,EAAQ/K,aAAa3C,GAAWnoB,eAAiBF,IAGrDu1B,EAAU7wB,QAAU1E,EACtB,GAAG,IAiBH,OAhBA0pB,IAA0B,KACxB+L,EAAa/wB,SAAU,EAChB,WACL+wB,EAAa/wB,SAAU,EACvB,MAAM4a,EAAUkW,EAAqB9wB,QACjC4a,GACFC,qBAAqBD,EAEzB,IACC,KACa,SAAQ,KAAM,CAC5BiM,WACAxK,iBACAC,0BACAC,oBACE,CAACsK,EAAUxK,EAAgBC,EAAyBC,GAE1D,CAy+CuB+U,CAAgBzN,GAC/BhM,GAAQ,SAAQ,IAAMyD,GAAY,CACtCxC,WACAmC,eACAM,mBACAC,eACAlC,gBACAmC,kBACE,CAAC3C,EAAUmC,EAAcM,EAAkBC,EAAclC,EAAemC,IAM5E8T,EAAavvB,QAAU6X,EACvB,MAAM0Z,GAAgB,SAAY,KAChC,MAAMvxB,EAAUmvB,GAASI,GAEL,SADNvvB,EAAQiW,WACZnI,OACR9N,EAAQkW,SAASxB,KACnB,GACC,IACG5a,GAAa,SAAY,KAC7B,MAAMqT,EAAQgiB,GAASI,GAActZ,WACrC,MAAoB,mBAAhB9I,EAAMW,OAGU,SAAhBX,EAAMW,OAGHX,EAAMrT,UAAU,GACtB,IAKHI,GAJqB,SAAQ,KAAM,CACjCJ,aACAC,SAAUw3B,KACR,CAACz3B,EAAYy3B,KAEjB,MAAMC,GAAa,SAAYl2B,GAAMmjB,GAAa0Q,GAASI,GAActZ,WAAY3a,IAAK,IACpFm2B,GAAuB,SAAY,IAAM5jB,GAAkBshB,GAASI,GAActZ,aAAa,IAC/Fyb,GAAa,SAAQ,KAAM,CAC/BhW,QAASH,EACTiB,MAAOhB,EACPqI,YACA8N,QAASH,EACT3jB,kBAAmB4jB,EACnB3B,gCACA3S,cACE,CAAC0G,EAAWtI,EAAkBuU,EAA+BtU,EAAcgW,EAAYC,EAAsBtU,IAWjH,OAVA8Q,GAAiB,CACfpK,YACAhM,QACAsF,WACA+Q,cAAeoB,GAAW,KAC1BnB,sBAAqD,IAA/BzzB,EAAMyzB,wBAE9B,IAAAhJ,YAAU,IACDoM,GACN,CAACA,IACG,gBAAoB3J,GAAWgK,SAAU,CAC9C50B,MAAO00B,GACN,gBAAoB,KAAU,CAC/BlN,QAASyC,GACTpP,MAAOA,GACNnd,EAAMC,UACX,CAMA,SAASk3B,GAAgBn3B,GACvB,MAAMmpB,EAJC,UAKDloB,EAA8BjB,EAAMiB,6BAA+BD,EAAOC,4BAChF,OAAO,gBAAoBrC,EAAe,MAAMY,GAAgB,gBAAoBm1B,GAAK,CACvF7J,MAAO9qB,EAAM8qB,MACb3B,UAAWA,EACX3pB,aAAcA,EACdyB,4BAA6BA,EAC7BwyB,qBAAsBzzB,EAAMyzB,qBAC5BmB,QAAS50B,EAAM40B,QACf3V,gBAAiBjf,EAAMif,gBACvBE,kBAAmBnf,EAAMmf,kBACzBje,YAAalB,EAAMkB,YACnBE,aAAcpB,EAAMoB,aACpBG,UAAWvB,EAAMuB,UACjBmkB,oBAAqB1lB,EAAM0lB,qBAC1B1lB,EAAMC,WACX,CAEA,MAAMm3B,GACM,IADNA,GAEW,KAEXC,GAAwB,CAACC,EAA2BrW,IACpDA,EACKtG,GAAYT,KAAK+G,EAASnG,UAE/Bwc,EACK3c,GAAYE,KAEdF,GAAYC,MAEf2c,GAAqB,CAACpc,EAAaqc,KACvC,GAAKrc,EAGL,OAAOqc,EAAkB32B,GAAgBqZ,KAAOrZ,GAAgBwZ,SAAS,EAyC3E,SAASod,GAAWC,GAClB,MAAuB,aAAhBA,EAAO56B,KAlChB,SAA0BiiB,GACxB,MACMxS,EADYwS,EAASnO,UACLzC,QAChB,OACJD,EAAM,YACNlC,EAAW,SACXiV,GACElC,EACE5D,EAAcnQ,QAAQgB,GACtBlC,EAfyBiV,IACI,MAA/BA,EAASvV,mBACJuV,EAASvV,mBAEO,SAAlBuV,EAASvO,KAWMmnB,CAAyB5Y,GACzCyY,EAAkBxsB,QAAQiW,GAC1B2W,EAAYJ,EAAkBtc,GAAgBhN,EAAQiN,GAAeD,GAAkBhN,GAc7F,MAbc,CACZhO,SAAU,QACVgD,IAAKqJ,EAAI9H,UAAUvB,IACnBC,KAAMoJ,EAAI9H,UAAUtB,KACpB00B,UAAW,aACXj0B,MAAO2I,EAAIL,UAAUtI,MACrBC,OAAQ0I,EAAIL,UAAUrI,OACtBumB,WAAYiN,GAAsBvtB,EAAemX,GACjD2W,YACAE,QAASP,GAAmBpc,EAAaqc,GACzCO,OAAQP,EAAkBJ,GAA8BA,GACxDY,cAAe,OAGnB,CAQsCC,CAAiBP,GAN9C,CACLE,UAAW1c,IAFYgd,EAOwDR,GALxCxpB,QACvCkc,WAAY8N,EAAUC,+BAA4Bld,EAAY,QAHlE,IAA2Bid,CAQ3B,CA0BA,SAASE,GAAsBt5B,GAC7B,MAAMstB,EAAWa,GAAY,cACvB,WACJtnB,EAAU,SACV8c,EAAQ,gBACR4V,EAAe,2BACfxH,EAA0B,wBAC1B/B,EAAuB,UACvBlgB,GACE9P,EACErB,GAAU,SAAQ,KAAM,CAC5BozB,6BACA/B,0BACAlgB,eACE,CAACiiB,EAA4BjiB,EAAWkgB,IACtChM,GAAe,SAAYH,IAC/B,MAAMxlB,EAAKk7B,IAEX,OADCl7B,GAA2GoB,IAzChH,SAAwBoH,EAAYxI,EAAIwlB,EAAejhB,GACrD,MAAM42B,EAAiB54B,OAAO64B,iBAAiBp7B,GACzC+O,EAAY/O,EAAGyyB,wBACfzhB,GAAS,OAAajC,EAAWosB,GACjCr0B,GAAO,QAAWkK,EAAQwU,GAiBhC,MAPkB,CAChBhd,aACA0R,YAXkB,CAClBlJ,SACAohB,QAASpyB,EAAGoyB,QAAQC,cACpB+F,QAAS+C,EAAe/C,SASxBppB,WAPiB,CACjBxK,EAAGwM,EAAO1J,UAAUb,MACpBhC,EAAGuM,EAAO1J,UAAUZ,QAMpBsK,SACAlK,OAGJ,CAoBWu0B,CAAe7yB,EAAYxI,EAAIwlB,EAAa,GAClD,CAAChd,EAAY0yB,IACV3a,GAAQ,SAAQ,KAAM,CAC1B0O,WACAzmB,aACAlI,UACAqlB,kBACE,CAACnd,EAAYmd,EAAcrlB,EAAS2uB,IAClCqM,GAAe,IAAAhL,QAAO/P,GACtBgb,GAAoB,IAAAjL,SAAO,GACjCnD,IAA0B,KACxB7H,EAASrc,UAAU+lB,SAASsM,EAAanzB,SAClC,IAAMmd,EAASrc,UAAUimB,WAAWoM,EAAanzB,WACvD,CAACmd,EAASrc,YACbkkB,IAA0B,KACxB,GAAIoO,EAAkBpzB,QAEpB,YADAozB,EAAkBpzB,SAAU,GAG9B,MAAMmE,EAAOgvB,EAAanzB,QAC1BmzB,EAAanzB,QAAUoY,EACvB+E,EAASrc,UAAU/E,OAAOqc,EAAOjU,EAAK,GACrC,CAACiU,EAAO+E,EAASrc,WACtB,CAEA,IAAIuyB,GAAmB,gBAAoB,MAuC3C,SAASC,GAAmBC,GAC1B,MAAMr3B,GAAS,IAAAs3B,YAAWD,GAE1B,OADCr3B,GAAuGjD,IACjGiD,CACT,CAEA,SAASu3B,GAAgB75B,GACvBA,EAAMK,gBACR,CA4FA,IAAIy5B,GAAgB,CAAC1yB,EAAGC,IAAMD,IAAMC,EAEhC0yB,GAA8Bz3B,IAChC,MAAM,QACJX,EAAO,YACPP,GACEkB,EACJ,OAAIlB,EACKA,EAAYE,YAEjBK,EACKA,EAAQL,YAEV,IAAI,EAsFb,SAAS04B,GAAqBC,EAAmB,MAC/C,MAAO,CACL/5B,YAAY,EACZo4B,iBAAiB,EACjB4B,SAAS,EACTC,cAAe,KACf7oB,KAAM,KACN8oB,aAAc,KACdH,mBACAntB,YAAa,KAEjB,CACA,MAAMutB,GAAS,CACb7B,OAAQ,CACN56B,KAAM,YACNoR,OAAQxM,EACRy3B,iBAAkB,KAClBhB,2BAA2B,EAC3BqB,SAAUN,GAAqB,QA8DnC,MAMMO,GAAuB,CAC3Btf,sBAAuBA,IAEnBuf,IAAqB,SATG,KAC5B,MAAMC,EA9JR,WACE,MAAMC,GAAiB,QAAW,CAACj4B,EAAGC,KAAM,CAC1CD,IACAC,QAEIi4B,GAAsB,QAAW,CAACrpB,EAAM4oB,EAASE,EAAe,KAAMttB,EAAc,KAAMiV,EAAW,QAAS,CAClH7hB,YAAY,EACZg6B,UACA5B,gBAAiBxsB,QAAQiW,GACzBoY,cAAepY,EACfzQ,OACA8oB,eACAttB,cACAmtB,iBAAkB,SAEdW,GAAmB,QAAW,CAAC5rB,EAAQsC,EAAMI,EAAWwoB,EAASE,EAAe,KAAMttB,EAAc,KAAMxC,EAAqB,QAAS,CAC5IkuB,OAAQ,CACN56B,KAAM,WACNmkB,SAAU,KACVqY,eACAttB,cACAwE,OACAtC,SACA0C,YACApH,qBACAgwB,SAAUK,EAAoBrpB,EAAM4oB,EAASE,EAActtB,EAAa,WAkD5E,MA/CiB,CAACyG,EAAOsnB,KACvB,GAAI36B,GAAWqT,GAAQ,CACrB,GAAIA,EAAMM,SAAS3M,UAAUxF,KAAOm5B,EAASj5B,YAC3C,OAAO,KAET,MAAMoN,EAASuE,EAAMnN,QAAQ6I,OAAOD,OAC9B0C,EAAY6B,EAAMG,WAAW/M,WAAWk0B,EAASj5B,aACjDw4B,EAAe/mB,GAAkBE,EAAMhM,QACvCuF,GAvCqBvF,EAuCkBgM,EAAMhM,QAtCzCC,IAAyB,YAAnBD,EAAOC,GAAG5J,KAAqB2J,EAAOC,GAAG7F,QAAQC,YAAc,KAuCzE0I,EAAqBiJ,EAAMjJ,mBACjC,OAAOswB,EAAiBF,EAAe1rB,EAAOvM,EAAGuM,EAAOtM,GAAI6Q,EAAM2D,aAAcxF,EAAWmpB,EAASX,QAASE,EAActtB,EAAaxC,EAC1I,CA1C6B/C,MA2C7B,GAAoB,mBAAhBgM,EAAMW,MAA4B,CACpC,MAAMyE,EAAYpF,EAAMoF,UACxB,GAAIA,EAAUrW,OAAOV,cAAgBi5B,EAASj5B,YAC5C,OAAO,KAET,MAAMs4B,EAAUW,EAASX,QACnBxoB,EAAY6B,EAAMG,WAAW/M,WAAWk0B,EAASj5B,aACjDU,EAASqW,EAAUrW,OACnBgP,EAAOhP,EAAOgP,KACd8oB,EAAeL,GAA4Bz3B,GAC3CwK,EAxDqBxK,IACxBA,EAAOX,QAAUW,EAAOX,QAAQC,YAAc,KAuD7Bk5B,CAAyBx4B,GAEvCyf,EAAW,CACfnG,SAFerI,EAAMwG,aAGrBghB,MAAO7f,GACPY,OAAQvI,EAAMyG,oBACd4e,QAAS9rB,EAAcnL,GAAgBqZ,KAAO,KAC9CggB,MAAOluB,EAAcnL,GAAcqZ,KAAO,MAE5C,MAAO,CACLwd,OAAQ,CACN56B,KAAM,WACNoR,OAAQuE,EAAMyG,oBACdtI,YACAqQ,WACAqY,eACAttB,cACAwE,OACAhH,mBAAoB,KACpBgwB,SAAUK,EAAoBrpB,EAAM4oB,EAASE,EAActtB,EAAaiV,IAG9E,CACA,OAAO,IAAI,CAGf,CAkF2BkZ,GACnBC,EA7DR,WACE,MAAMR,GAAiB,QAAW,CAACj4B,EAAGC,KAAM,CAC1CD,IACAC,QAEIi4B,GAAsB,OAAWX,IACjCY,GAAmB,QAAW,CAAC5rB,EAAQirB,EAAmB,KAAMhB,KAA8B,CAClGT,OAAQ,CACN56B,KAAM,YACNoR,SACAirB,mBACAhB,4BACAqB,SAAUK,EAAoBV,QAG5BkB,EAAclB,GACXA,EAAmBW,EAAiBp4B,EAAQy3B,GAAkB,GAAQ,KAEzEmB,EAAW,CAACC,EAAOC,EAAY/zB,EAAQsE,KAC3C,MAAM0vB,EAAqBh0B,EAAOc,UAAUH,QAAQmzB,GAC9CG,EAA+B1vB,QAAQD,EAAc0L,eAAiB1L,EAAcE,SAASsvB,IAC7F15B,EAAU8F,EAAcF,GACxB0yB,EAAmBt4B,GAAWA,EAAQC,cAAgBy5B,EAAQC,EAAa,KACjF,IAAKC,EAAoB,CACvB,IAAKC,EACH,OAAOL,EAAYlB,GAErB,GAAI1yB,EAAOc,UAAUJ,UAAUozB,GAC7B,OAAO,KAET,MAAM5W,EAASzhB,EAAO6I,EAAcvD,YAAYrF,OAC1C+L,EAAS0rB,EAAejW,EAAOhiB,EAAGgiB,EAAO/hB,GAC/C,OAAOk4B,EAAiB5rB,EAAQirB,GAAkB,EACpD,CACA,GAAIuB,EACF,OAAOL,EAAYlB,GAErB,MAAMhtB,EAAa1F,EAAOe,YAAYrF,MAChC+L,EAAS0rB,EAAeztB,EAAWxK,EAAGwK,EAAWvK,GACvD,OAAOk4B,EAAiB5rB,EAAQirB,EAAkBsB,EAAmB3wB,cAAc,EAkBrF,MAhBiB,CAAC2I,EAAOsnB,KACvB,GAAI36B,GAAWqT,GACb,OAAIA,EAAMM,SAAS3M,UAAUxF,KAAOm5B,EAASj5B,YACpC,KAEFw5B,EAASP,EAASj5B,YAAa2R,EAAMM,SAAS3M,UAAUxF,GAAI6R,EAAMhM,OAAQgM,EAAM1H,eAEzF,GAAoB,mBAAhB0H,EAAMW,MAA4B,CACpC,MAAMyE,EAAYpF,EAAMoF,UACxB,OAAIA,EAAUrW,OAAOV,cAAgBi5B,EAASj5B,YACrC,KAEFw5B,EAASP,EAASj5B,YAAa+W,EAAUrW,OAAOV,YAAa+W,EAAUpR,OAAQoR,EAAU9M,cAClG,CACA,OAAO,IAAI,CAGf,CAG4B4vB,GAE1B,MADiB,CAACloB,EAAOsnB,IAAaJ,EAAiBlnB,EAAOsnB,IAAaK,EAAkB3nB,EAAOsnB,IAAaR,EAClG,GAKyCE,GAAsB,KAAM,CACpF3P,QAASyC,GACTqO,mBAAoB5B,IAFK,EAvRTh5B,IAChB,MAAMwtB,GAAM,IAAAC,QAAO,MACboN,GAAS,SAAY,CAAC19B,EAAK,QAC/BqwB,EAAIloB,QAAUnI,CAAE,GACf,IACG29B,GAAS,SAAY,IAAMtN,EAAIloB,SAAS,KACxC,UACJ6jB,EAAS,8BACTiM,EAA6B,SAC7B3S,GACEmW,GAAmB1L,KACjB,KACJpwB,EAAI,YACJ0D,GACEo4B,GAAmBD,IACjBhzB,GAAa,SAAQ,KAAM,CAC/B/E,GAAIZ,EAAMc,YACVX,MAAOH,EAAMG,MACbrD,OACA0D,iBACE,CAACR,EAAMc,YAAad,EAAMG,MAAOrD,EAAM0D,KACrC,SACJP,EAAQ,YACRa,EAAW,UACX8N,EAAS,wBACTkgB,EAAuB,2BACvB+B,EAA0B,QAC1BuI,EAAO,OACP1B,EACAvd,sBAAuB4gB,GACrB/6B,EAGJ,GArEAqtB,KAmBAF,MAkDKiM,EAAS,CASZhB,IARqB,SAAQ,KAAM,CACjCzyB,aACA8c,WACA4V,gBAAiByC,EACjBjK,6BACA/B,0BACAlgB,eACE,CAACjJ,EAAY8c,EAAUqY,EAAQjK,EAA4B/B,EAAyBlgB,IAE1F,CACA,MAAMosB,GAAkB,SAAQ,IAAMpsB,EAAY,CAChDqsB,SAAU,EACVC,KAAM,SACN,mBAAoB9F,EACpB,oCAAqCt0B,EACrC,kCAAmCqoB,EACnC/iB,WAAW,EACXlF,YAAa63B,IACX,MAAM,CAAC5P,EAAWiM,EAA+Bt0B,EAAa8N,IAC5DusB,GAAY,SAAYj8B,IACR,aAAhBw4B,EAAO56B,MAGN46B,EAAOzW,UAGe,cAAvB/hB,EAAMk8B,eAGV,IAAA7G,WAAUwG,EAA4B,GACrC,CAACA,EAA6BrD,IAC3B2D,GAAW,SAAQ,KACvB,MAAMtG,EAAQ0C,GAAWC,GACnB4D,EAAkC,aAAhB5D,EAAO56B,MAAuB46B,EAAOzW,SAAWka,OAAYlgB,EAWpF,MAVe,CACbsgB,SAAUV,EACVW,eAAgB,CACd,gCAAiCrS,EACjC,wBAAyBroB,EACzBi0B,QACAuG,mBAEFN,kBAEW,GACZ,CAAC7R,EAAW6R,EAAiBl6B,EAAa42B,EAAQyD,EAAWN,IAC1DY,GAAS,SAAQ,KAAM,CAC3B36B,YAAa6E,EAAW/E,GACxB9D,KAAM6I,EAAW7I,KACjBuD,OAAQ,CACNF,MAAOwF,EAAWxF,MAClBK,YAAamF,EAAWnF,gBAExB,CAACmF,EAAWnF,YAAamF,EAAW/E,GAAI+E,EAAWxF,MAAOwF,EAAW7I,OACzE,OAAO,gBAAoB,WAAgB,KAAMmD,EAASo7B,EAAU3D,EAAO8B,SAAUiC,GAAQ,IAoM/F,SAASC,GAAiB17B,GAGxB,OAFyB44B,GAAmBD,IACHgD,kBACjB37B,EAAMc,aAAgBd,EAAMo5B,QAG7C,gBAAoBM,GAAoB15B,GAFtC,IAGX,CACA,SAAS47B,GAAgB57B,GACvB,MAAM4O,EAA4C,kBAAzB5O,EAAM67B,iBAAgC77B,EAAM67B,eAC/DhL,EAA6B7lB,QAAQhL,EAAM87B,mCAC3ChN,EAA0B9jB,QAAQhL,EAAM8uB,yBAC9C,OAAO,gBAAoB4M,IAAkB,OAAS,CAAC,EAAG17B,EAAO,CAC/Do5B,SAAS,EACTxqB,UAAWA,EACXiiB,2BAA4BA,EAC5B/B,wBAAyBA,IAE7B,CAEA,MAAMiN,GAAU7S,GAAQ5mB,GAAS4mB,IAAS5mB,EACpC05B,GAAWD,GAAQ,UACnBE,GAASF,GAAQ,QAEjBG,IADYH,GAAQ,WACT,CAAClP,EAAU9uB,IAAOA,EAAG8uB,EAASsP,YAAcp+B,EAAG8uB,EAASuP,YAEnEC,GAAsBl/B,IAC1B,MAAM43B,EAAQr1B,OAAO64B,iBAAiBp7B,GAChC0vB,EAAW,CACfsP,UAAWpH,EAAMoH,UACjBC,UAAWrH,EAAMqH,WAEnB,OAAOF,GAASrP,EAAUmP,KAAaE,GAASrP,EAAUoP,GAAO,EAgC7DK,GAAuBn/B,GACjB,MAANA,GAGAA,IAAO0f,SAAS4P,MAGhBtvB,IAAO0f,SAASuF,gBALX,KAQJia,GAAoBl/B,GAGlBA,EAFEm/B,GAAqBn/B,EAAGsyB,eAKnC,IAiBI8M,GAAYp/B,IAAM,CACpBwE,EAAGxE,EAAGq/B,WACN56B,EAAGzE,EAAGs/B,YAGR,MAAMC,GAAav/B,IACjB,IAAKA,EACH,OAAO,EAGT,MAAuB,UADTuC,OAAO64B,iBAAiBp7B,GAC5B+C,UAGHw8B,GAAWv/B,EAAGsyB,cAAc,EAmGrC,IAAI3M,GAAe,EACjB0K,MACA7nB,aACAg3B,MACAha,eACAta,YACAu0B,iBACAjxB,mBACAlI,wBAEA,MAAMo5B,EAAoBF,EAAIE,kBACxB1uB,EAtCU,EAAC2uB,EAAWD,KAC5B,MAAM3T,GAAO,QAAO4T,GACpB,IAAKD,EACH,OAAO3T,EAET,GAAI4T,IAAcD,EAChB,OAAO3T,EAET,MAAMhmB,EAAMgmB,EAAK6T,WAAW75B,IAAM25B,EAAkBJ,UAC9Ct5B,EAAO+lB,EAAK6T,WAAW55B,KAAO05B,EAAkBL,WAChDp5B,EAASF,EAAM25B,EAAkB7a,aAEjC+a,EAAa,CACjB75B,MACAG,MAHYF,EAAO05B,EAAkB5a,YAIrC7e,SACAD,QAEI+I,GAAY,QAAO6wB,EAAY7T,EAAKyD,QAO1C,OANe,QAAU,CACvBzgB,YACAM,OAAQ0c,EAAK1c,OACbmgB,OAAQzD,EAAKyD,OACbC,QAAS1D,EAAK0D,SAEH,EAaEoQ,CAAUxP,EAAKqP,GACxB54B,GAAO,QAAWkK,EAAQwU,GAC1BtV,EAAU,MACd,IAAKwvB,EACH,OAAO,KAET,MAAMI,GAAc,QAAOJ,GACrBK,EAAa,CACjBlb,aAAc6a,EAAkB7a,aAChCC,YAAa4a,EAAkB5a,aAEjC,MAAO,CACL9T,OAAQ8uB,EACRh5B,MAAM,QAAWg5B,EAAata,GAC9Bte,OAAQk4B,GAAUM,GAClBK,aACAz5B,oBAEH,EAhBe,GAiBVmN,EAtHoB,GAC1BjL,aACAiJ,YACAjD,mBACAyM,gBACA/P,YACA8F,SACAlK,OACAoJ,cAEA,MAAM7J,EAAQ,MACZ,IAAK6J,EACH,OAAO,KAET,MAAM,WACJ6vB,EACA/uB,OAAQ8uB,GACN5vB,EACE2D,EAAY+Q,GAAa,CAC7BC,aAAckb,EAAWlb,aACzBC,YAAaib,EAAWjb,YACxBpe,OAAQo5B,EAAYF,WAAWl5B,OAC/BD,MAAOq5B,EAAYF,WAAWn5B,QAEhC,MAAO,CACLG,cAAesJ,EAAQpJ,KAAKQ,UAC5Bw4B,cACAC,aACAz5B,kBAAmB4J,EAAQ5J,kBAC3BY,OAAQ,CACNe,QAASiI,EAAQhJ,OACjBiB,QAAS+H,EAAQhJ,OACjBV,IAAKqN,EACL1M,KAAM,CACJhC,MAAOZ,EACP6C,aAAc7C,IAIrB,EA7Ba,GA8BRyC,EAAqB,aAAdkE,EAA2BD,GAAWO,GAkBnD,MAXkB,CAChBhD,aACAgG,mBACAyM,gBACAjU,OACAyK,YACAT,SACAlK,OACAT,QACAE,QAfcM,EAAW,CACzBC,OACAC,gBAAiB,KACjBC,OACAX,UAac,EA4DE25B,CAAsB,CACtCx3B,aACAiJ,WAAYguB,EACZjxB,mBACAyM,cAAeukB,EAAIvkB,cACnB/P,YACA8F,SACAlK,OACAoJ,YAEF,OAAOuD,CAAS,EAGlB,MAAMwsB,GAAY,CAChBzgB,SAAS,GAEL0gB,GAAU,CACd1gB,SAAS,GAEX,IAAI2gB,GAAqB7/B,GAAWA,EAAQ8jB,yBAA2B6b,GAAYC,GAEnF,MAAME,GAA+Bxe,GAAYA,GAAYA,EAAS4d,IAAIE,mBAAqB,KAC/F,SAASW,GAAsB1+B,GAC7B,MAAM2+B,GAAmB,IAAAhQ,QAAO,MAC1BuJ,EAAa4B,GAAmB1L,IAChCd,EAAWa,GAAY,cACvB,SACJxK,EAAQ,QACRzB,GACEgW,EACE0G,EAAcnQ,GAAYzuB,GAC1B6G,GAAa,SAAQ,KAAM,CAC/B/E,GAAI9B,EAAK0B,YACT1D,KAAMgC,EAAKhC,KACX0T,KAAM1R,EAAK0R,QACT,CAAC1R,EAAK0B,YAAa1B,EAAK0R,KAAM1R,EAAKhC,OACjC6gC,GAAyB,IAAAlQ,QAAO9nB,GAChCi4B,GAAuB,SAAQ,KAAM,QAAW,CAACj8B,EAAGC,KACvD67B,EAAiBn4B,SAA6G/G,IAC/H,MAAM8F,EAAS,CACb1C,IACAC,KAEFof,EAAQxH,sBAAsB7T,EAAW/E,GAAIyD,EAAO,KAClD,CAACsB,EAAW/E,GAAIogB,IACd6c,GAAmB,SAAY,KACnC,MAAM9e,EAAW0e,EAAiBn4B,QAClC,OAAKyZ,GAAaA,EAAS4d,IAAIE,kBAGxBN,GAAUxd,EAAS4d,IAAIE,mBAFrBn7B,CAEuC,GAC/C,IACGo8B,GAAe,SAAY,KAC/B,MAAMz5B,EAASw5B,IACfD,EAAqBv5B,EAAO1C,EAAG0C,EAAOzC,EAAE,GACvC,CAACi8B,EAAkBD,IAChBG,GAAuB,SAAQ,KAAM,OAAQD,IAAe,CAACA,IAC7DE,GAAkB,SAAY,KAClC,MAAMjf,EAAW0e,EAAiBn4B,QAC5B+H,EAAUkwB,GAA6Bxe,GAC3CA,GAAY1R,GAAuH9O,IACrHwgB,EAASuC,cACbC,yBACVuc,IAGFC,GAAsB,GACrB,CAACA,EAAsBD,IACpBjb,GAA6B,SAAY,CAACF,EAAcllB,KAC1DggC,EAAiBn4B,SAA6H/G,IAChJ,MAAMmH,EAAWg4B,EAAYp4B,QACvBkoB,EAAM9nB,EAASu4B,kBACpBzQ,GAA2GjvB,IAC5G,MAAMo+B,EAxMGx7B,KAGJ,CACL07B,kBAHwBP,GAAqBn7B,GAI7CiX,cAHoBskB,GAAWv7B,KAsMnB+8B,CAAO1Q,GACbzO,EAAW,CACfyO,MACA7nB,aACAg3B,MACArb,cAAe7jB,GAEjBggC,EAAiBn4B,QAAUyZ,EAC3B,MAAMnO,EAAYkS,GAAa,CAC7B0K,MACA7nB,aACAg3B,MACAha,eACAta,UAAW3C,EAAS2C,UACpBu0B,eAAgBl3B,EAASk3B,eACzBjxB,iBAAkBjG,EAASiG,iBAC3BlI,mBAAoBiC,EAASy4B,0BAEzBj5B,EAAay3B,EAAIE,kBAQvB,OAPI33B,IACFA,EAAW6lB,aAAa3B,GAAgBD,UAAW6N,EAAW7N,WAC9DjkB,EAAWrH,iBAAiB,SAAUmgC,EAAiBV,GAAmBve,EAASuC,iBAK9E1Q,CAAS,GACf,CAAComB,EAAW7N,UAAWxjB,EAAYq4B,EAAiBN,IACjDpa,GAAyB,SAAY,KACzC,MAAMvE,EAAW0e,EAAiBn4B,QAC5B+H,EAAUkwB,GAA6Bxe,GAE7C,OADEA,GAAY1R,GAAyJ9O,IAChKg+B,GAAUlvB,EAAQ,GACxB,IACGyW,GAAc,SAAY,KAC9B,MAAM/E,EAAW0e,EAAiBn4B,QACjCyZ,GAA8GxgB,IAC/G,MAAM8O,EAAUkwB,GAA6Bxe,GAC7C0e,EAAiBn4B,QAAU,KACtB+H,IAGL0wB,EAAqB9gB,SACrB5P,EAAQ+wB,gBAAgBhV,GAAgBD,WACxC9b,EAAQrP,oBAAoB,SAAUggC,EAAiBV,GAAmBve,EAASuC,gBAAe,GACjG,CAAC0c,EAAiBD,IACf15B,GAAS,SAAYsf,IACzB,MAAM5E,EAAW0e,EAAiBn4B,QACjCyZ,GAA6GxgB,IAC9G,MAAM8O,EAAUkwB,GAA6Bxe,GAC5C1R,GAA6H9O,IAC9H8O,EAAQovB,WAAa9Y,EAAO/hB,EAC5ByL,EAAQmvB,YAAc7Y,EAAOhiB,CAAC,GAC7B,IACG3C,GAAY,SAAQ,KACjB,CACL6jB,6BACAS,yBACAQ,cACAzf,YAED,CAACyf,EAAajB,EAA4BS,EAAwBjf,IAC/DqZ,GAAQ,SAAQ,KAAM,CAC1B0O,WACAzmB,aACA3G,eACE,CAACA,EAAW2G,EAAYymB,IAC5B9B,IAA0B,KACxBqT,EAAuBr4B,QAAUoY,EAAM/X,WACvC8c,EAASzd,UAAUmnB,SAASzO,GACrB,KACD+f,EAAiBn4B,SAEnBwe,IAEFrB,EAASzd,UAAUqnB,WAAW3O,EAAM,IAErC,CAAC1e,EAAW2G,EAAYme,EAAapG,EAAOsD,EAASyB,EAASzd,YACjEslB,IAA0B,KACnBmT,EAAiBn4B,SAGtB0b,EAAQvH,yBAAyBkkB,EAAuBr4B,QAAQ1E,IAAK9B,EAAK89B,eAAe,GACxF,CAAC99B,EAAK89B,eAAgB5b,IACzBsJ,IAA0B,KACnBmT,EAAiBn4B,SAGtB0b,EAAQtH,gCAAgCikB,EAAuBr4B,QAAQ1E,GAAI9B,EAAK6M,iBAAiB,GAChG,CAAC7M,EAAK6M,iBAAkBqV,GAC7B,CAEA,SAASqd,KAAQ,CACjB,MAAMC,GAAQ,CACZ16B,MAAO,EACPC,OAAQ,EACR2I,OAzhNgB,CAChBtJ,IAAK,EACLG,MAAO,EACPD,OAAQ,EACRD,KAAM,IAwiNFo7B,GAAW,EACfC,yBACAnnB,cACAonB,cAEA,MAAMl2B,EAtBQ,GACdi2B,yBACAnnB,cACAonB,aAEID,GAGY,UAAZC,EAFKH,GAKF,CACLz6B,OAAQwT,EAAYlJ,OAAOjC,UAAUrI,OACrCD,MAAOyT,EAAYlJ,OAAOjC,UAAUtI,MACpC4I,OAAQ6K,EAAYlJ,OAAO3B,QAQhBkyB,CAAQ,CACnBF,yBACAnnB,cACAonB,YAEF,MAAO,CACLlJ,QAASle,EAAYke,QACrBsC,UAAW,aACXj0B,MAAO2E,EAAK3E,MACZC,OAAQ0E,EAAK1E,OACb86B,UAAWp2B,EAAKiE,OAAOtJ,IACvB07B,YAAar2B,EAAKiE,OAAOnJ,MACzBw7B,aAAct2B,EAAKiE,OAAOpJ,OAC1B07B,WAAYv2B,EAAKiE,OAAOrJ,KACxB47B,WAAY,IACZC,SAAU,IACVhH,cAAe,OACf5N,WAAwB,SAAZqU,EAAqB9jB,GAAYtD,YAAc,KAC5D,EAyDH,IAAI4nB,GAAgB,QAvDAj/B,IAClB,MAAMk/B,GAAsB,IAAAzR,QAAO,MAC7B0R,GAA2B,SAAY,KACtCD,EAAoB55B,UAGzByY,aAAamhB,EAAoB55B,SACjC45B,EAAoB55B,QAAU,KAAI,GACjC,KACG,QACJm5B,EAAO,gBACPnD,EAAe,QACf8D,EAAO,UACPjW,GACEnpB,GACGw+B,EAAwBa,IAA6B,IAAA1L,UAA2B,SAAlB3zB,EAAMy+B,UAC3E,IAAAhU,YAAU,IACH+T,EAGW,SAAZC,GACFU,IACAE,GAA0B,GACnBhB,IAELa,EAAoB55B,QACf+4B,IAETa,EAAoB55B,QAAUmY,YAAW,KACvCyhB,EAAoB55B,QAAU,KAC9B+5B,GAA0B,EAAM,IAE3BF,GAdEd,IAeR,CAACI,EAASD,EAAwBW,IACrC,MAAMG,GAAkB,SAAYpgC,IACP,WAAvBA,EAAMk8B,eAGVE,IACgB,UAAZmD,GACFW,IACF,GACC,CAACX,EAASW,EAAS9D,IAChBvG,EAAQwJ,GAAS,CACrBC,yBACAC,QAASz+B,EAAMy+B,QACfpnB,YAAarX,EAAMqX,cAErB,OAAO,gBAAoBrX,EAAMqX,YAAYkY,QAAS,CACpDwF,QACA,kCAAmC5L,EACnCmS,gBAAiBgE,EACjB9R,IAAKxtB,EAAMu7B,UACX,IAkEJ,MAAMgE,WAAqB,gBACzB,WAAA1gC,IAAeC,GACbC,SAASD,GACTH,KAAK8T,MAAQ,CACX+sB,UAAWx0B,QAAQrM,KAAKqB,MAAMy/B,IAC9BthB,KAAMxf,KAAKqB,MAAMy/B,GACjBhB,QAAS9/B,KAAKqB,MAAM8J,eAAiBnL,KAAKqB,MAAMy/B,GAAK,OAAS,QAEhE9gC,KAAKygC,QAAU,KACc,UAAvBzgC,KAAK8T,MAAMgsB,SAGf9/B,KAAKkB,SAAS,CACZ2/B,WAAW,GACX,CAEN,CACA,+BAAOE,CAAyB1/B,EAAOyS,GACrC,OAAKzS,EAAM8J,cAOP9J,EAAMy/B,GACD,CACLD,WAAW,EACXrhB,KAAMne,EAAMy/B,GACZhB,QAAS,QAGThsB,EAAM+sB,UACD,CACLA,WAAW,EACXrhB,KAAM1L,EAAM0L,KACZsgB,QAAS,SAGN,CACLe,WAAW,EACXf,QAAS,QACTtgB,KAAM,MAvBC,CACLqhB,UAAWx0B,QAAQhL,EAAMy/B,IACzBthB,KAAMne,EAAMy/B,GACZhB,QAAS,OAsBf,CACA,MAAA1+B,GACE,IAAKpB,KAAK8T,MAAM+sB,UACd,OAAO,KAET,MAAMnE,EAAW,CACf+D,QAASzgC,KAAKygC,QACdjhB,KAAMxf,KAAK8T,MAAM0L,KACjBsgB,QAAS9/B,KAAK8T,MAAMgsB,SAEtB,OAAO9/B,KAAKqB,MAAMC,SAASo7B,EAC7B,EAgHF,MAAMsE,GAAe,CACnBnvB,KAAM,WACN1T,KAAM,UACNuL,UAAW,WACXu0B,gBAAgB,EAChBjxB,kBAAkB,EAClBwyB,yBAAyB,EACzByB,YAAa,KACbC,qBAZF,WAEE,OADChjB,SAAS4P,MAAgGluB,IACnGse,SAAS4P,IAClB,GAWMqT,GAA+B/F,IACnC,IAGIgG,EAHAC,EAAc,IACbjG,GAGL,IAAKgG,KAAkBJ,QACY1kB,IAA7B8e,EAASgG,KACXC,EAAc,IACTA,EACH,CAACD,GAAiBJ,GAAaI,KAIrC,OAAOC,CAAW,EAEdC,GAAiB,CAACnjC,EAAMiW,IAAajW,IAASiW,EAAS/N,UAAUlI,KACjEojC,GAAe,CAACntB,EAAUH,IAAeA,EAAW/M,WAAWkN,EAAS3M,UAAUxF,IA4GlFu/B,GAAqB,CACzBC,wBAxzJ8BthC,IAAQ,CACtChC,KAAM,6BACNmb,QAASnZ,KAwzJLuhC,IAAqB,SA9GC,KAC1B,MAAMC,EAAoB,CACxBjpB,YAAa,KACbkpB,0BAA0B,EAC1B/G,SAAU,CACRgH,gBAAgB,EAChBC,iBAAkB,KAClBC,qBAAsB,KACtBC,oBAAoB,GAEtBC,SAAU,MAENC,EAAuB,IACxBP,EACHC,0BAA0B,GAEtBO,GAAqB,QAAWn7B,IAAc,CAClD7E,YAAa6E,EAAW/E,GACxB9D,KAAM6I,EAAW7I,KACjBuD,OAAQ,CACNF,MAAOwF,EAAWxF,MAClBK,YAAamF,EAAWnF,iBAGtBugC,GAAc,QAAW,CAACngC,EAAIgO,EAAWoyB,EAA2BC,EAAyBliB,EAAU6gB,KAC3G,MAAM9+B,EAAcie,EAASpZ,WAAW/E,GAExC,GADeme,EAASpZ,WAAWnF,cAAgBI,EACvC,CACV,MAAMggC,EAAWhB,EAAc,CAC7B7/B,OAAQ6/B,EACR7gB,SAAU+hB,EAAmB/hB,EAASpZ,aACpC,KACE6zB,EAAW,CACfgH,eAAgBQ,EAChBP,iBAAkBO,EAA4BlgC,EAAc,KAC5D4/B,qBAAsB5/B,EACtB6/B,oBAAoB,GAEtB,MAAO,CACLtpB,YAAa0H,EAAS1H,YACtBkpB,0BAA0B,EAC1B/G,WACAoH,WAEJ,CACA,IAAKhyB,EACH,OAAOiyB,EAET,IAAKI,EACH,OAAOX,EAET,MAAM9G,EAAW,CACfgH,eAAgBQ,EAChBP,iBAAkB3/B,EAClB4/B,qBAAsB,KACtBC,oBAAoB,GAEtB,MAAO,CACLtpB,YAAa0H,EAAS1H,YACtBkpB,0BAA0B,EAC1B/G,WACAoH,SAAU,KACX,IA2CH,MAzCiB,CAACnuB,EAAOsnB,KACvB,MAAMmH,EAA2BpB,GAA6B/F,GACxDn5B,EAAKsgC,EAAyB1gC,YAC9B1D,EAAOokC,EAAyBpkC,KAChC8R,GAAasyB,EAAyBtE,eACtCgD,EAAcsB,EAAyBtB,YAC7C,GAAIxgC,GAAWqT,GAAQ,CACrB,MAAMM,EAAWN,EAAMM,SACvB,IAAKktB,GAAenjC,EAAMiW,GACxB,OAAO8tB,EAET,MAAM9hB,EAAWmhB,GAAantB,EAAUN,EAAMG,YACxC4tB,EAAiBjuB,GAAkBE,EAAMhM,UAAY7F,EAC3D,OAAOmgC,EAAYngC,EAAIgO,EAAW4xB,EAAgBA,EAAgBzhB,EAAU6gB,EAC9E,CACA,GAAoB,mBAAhBntB,EAAMW,MAA4B,CACpC,MAAMyE,EAAYpF,EAAMoF,UACxB,IAAKooB,GAAenjC,EAAM+a,EAAU9E,UAClC,OAAO8tB,EAET,MAAM9hB,EAAWmhB,GAAaroB,EAAU9E,SAAUN,EAAMG,YACxD,OAAOmuB,EAAYngC,EAAIgO,EAAWqqB,GAA4BphB,EAAUrW,UAAYZ,EAAI2R,GAAkBsF,EAAUpR,UAAY7F,EAAIme,EAAU6gB,EAChJ,CACA,GAAoB,SAAhBntB,EAAMW,OAAoBX,EAAMoF,YAAcpF,EAAMqF,YAAa,CACnE,MAAMD,EAAYpF,EAAMoF,UACxB,IAAKooB,GAAenjC,EAAM+a,EAAU9E,UAClC,OAAO8tB,EAET,MAAMhoB,EAAUtG,GAAkBsF,EAAUpR,UAAY7F,EAClDugC,EAAen2B,QAAQ6M,EAAUpR,OAAOC,IAAmC,YAA7BmR,EAAUpR,OAAOC,GAAG5J,MAClEskC,EAASvpB,EAAU9E,SAAS/N,UAAUpE,KAAOA,EACnD,OAAIiY,EACKsoB,EAAeb,EAAoBO,EAExCO,EACKd,EAEFO,CACT,CACA,OAAOA,CAAoB,CAEd,GAKuCV,IAAoB,CAACkB,EAAYC,EAAevH,KAC/F,IACF+F,GAA6B/F,MAC7BsH,KACAC,KAEJ,CACDxX,QAASyC,GACTqO,mBAAoB5B,IARK,EAtPTh5B,IAChB,MAAMg3B,GAAa,IAAA8B,YAAW5L,IAC7B8J,GAAsGz4B,IACvG,MAAM,UACJ4qB,EAAS,kBACThW,GACE6jB,EACEuK,GAAe,IAAA9T,QAAO,MACtB+T,GAAiB,IAAA/T,QAAO,OACxB,SACJxtB,EAAQ,YACRO,EAAW,KACX1D,EAAI,KACJ0T,EAAI,UACJnI,EAAS,wBACT81B,EAAuB,eACvBvB,EAAc,iBACdjxB,EAAgB,SAChB6tB,EAAQ,SACRoH,EAAQ,wBACRR,EAAuB,qBACvBP,GACE7/B,EACEi+B,GAAkB,SAAY,IAAMsD,EAAaj8B,SAAS,IAC1Dm8B,GAAkB,SAAY,CAACn/B,EAAQ,QAC3Ci/B,EAAaj8B,QAAUhD,CAAK,GAC3B,IAEGo/B,IADoB,SAAY,IAAMF,EAAel8B,SAAS,KAC1C,SAAY,CAAChD,EAAQ,QAC7Ck/B,EAAel8B,QAAUhD,CAAK,GAC7B,KAnGH+qB,KAyGA,MAAMsU,GAA6B,SAAY,KACzCxuB,KACFitB,EAAwB,CACtBpvB,UAAWqR,MAEf,GACC,CAAClP,EAAmBitB,IACvB5C,GAAsB,CACpBh9B,cACA1D,OACA0T,OACAnI,YACAu0B,iBACAjxB,mBACAwyB,0BACAF,oBAEF,MAAM5mB,GAAc,SAAQ,IAAM,gBAAoBkoB,GAAc,CAClEE,GAAIz/B,EAAMqX,YACVvN,cAAe9J,EAAMugC,2BACpB,EACDnB,UACAjhB,OACAsgB,aACI,gBAAoBQ,GAAe,CACvC5nB,YAAa8G,EACbihB,QAASA,EACT7D,SAAUmG,EACVjD,QAASA,EACTtV,UAAWA,EACXmS,gBAAiBqG,OACd,CAACxY,EAAWwY,EAA4B3hC,EAAMqX,YAAarX,EAAMugC,yBAA0BmB,IAC1FrG,GAAW,SAAQ,KAAM,CAC7BE,SAAUkG,EACVpqB,cACAuqB,eAAgB,CACd,wBAAyBphC,EACzB,gCAAiC2oB,MAEjC,CAACA,EAAW3oB,EAAa6W,EAAaoqB,IACpC9F,EAAkBiF,EAAWA,EAAS7hB,SAASje,YAAc,KAC7D+gC,GAAmB,SAAQ,KAAM,CACrCrhC,cACA1D,OACA6+B,qBACE,CAACn7B,EAAam7B,EAAiB7+B,IAmBnC,OAAO,gBAAoB67B,GAAiBzB,SAAU,CACpD50B,MAAOu/B,GACN5hC,EAASo7B,EAAU7B,GApBtB,WACE,IAAKoH,EACH,OAAO,KAET,MAAM,SACJ7hB,EAAQ,OACRhf,GACE6gC,EACEkB,EAAO,gBAAoBpG,GAAkB,CACjD56B,YAAaie,EAASje,YACtBX,MAAO4e,EAAS1e,OAAOF,MACvBi5B,SAAS,EACTxqB,WAAW,EACXkgB,yBAAyB,EACzB+B,4BAA4B,IAC3B,CAACkR,EAAmBC,IAAsBjiC,EAAOgiC,EAAmBC,EAAmBjjB,KAC1F,OAAO,eAAsB+iB,EAAMjC,IACrC,CAGiCoC,GAAW,IA0J9C,IAAIC,GAAuB7B,E", "sources": ["webpack://Magentic-UI/./node_modules/@hello-pangea/dnd/dist/dnd.esm.js"], "sourcesContent": ["import React, { useLayoutEffect, useEffect, useRef, useState, useContext } from 'react';\nimport ReactDOM, { flushSync } from 'react-dom';\nimport { createStore as createStore$1, applyMiddleware, compose, bindActionCreators } from 'redux';\nimport { Provider, connect } from 'react-redux';\nimport { useMemo, useCallback } from 'use-memo-one';\nimport { getRect, expand, offset, withScroll, calculateBox, getBox, createBox } from 'css-box-model';\nimport memoizeOne from 'memoize-one';\nimport rafSchd from 'raf-schd';\nimport _extends from '@babel/runtime/helpers/esm/extends';\n\nconst isProduction$1 = process.env.NODE_ENV === 'production';\nconst spacesAndTabs = /[ \\t]{2,}/g;\nconst lineStartWithSpaces = /^[ \\t]*/gm;\nconst clean$2 = value => value.replace(spacesAndTabs, ' ').replace(lineStartWithSpaces, '').trim();\nconst getDevMessage = message => clean$2(`\n  %c@hello-pangea/dnd\n\n  %c${clean$2(message)}\n\n  %c👷‍ This is a development only message. It will be removed in production builds.\n`);\nconst getFormattedMessage = message => [getDevMessage(message), 'color: #00C584; font-size: 1.2em; font-weight: bold;', 'line-height: 1.5', 'color: #723874;'];\nconst isDisabledFlag = '__@hello-pangea/dnd-disable-dev-warnings';\nfunction log(type, message) {\n  if (isProduction$1) {\n    return;\n  }\n  if (typeof window !== 'undefined' && window[isDisabledFlag]) {\n    return;\n  }\n  console[type](...getFormattedMessage(message));\n}\nconst warning = log.bind(null, 'warn');\nconst error = log.bind(null, 'error');\n\nfunction noop$2() {}\n\nfunction getOptions(shared, fromBinding) {\n  return {\n    ...shared,\n    ...fromBinding\n  };\n}\nfunction bindEvents(el, bindings, sharedOptions) {\n  const unbindings = bindings.map(binding => {\n    const options = getOptions(sharedOptions, binding.options);\n    el.addEventListener(binding.eventName, binding.fn, options);\n    return function unbind() {\n      el.removeEventListener(binding.eventName, binding.fn, options);\n    };\n  });\n  return function unbindAll() {\n    unbindings.forEach(unbind => {\n      unbind();\n    });\n  };\n}\n\nconst isProduction = process.env.NODE_ENV === 'production';\nconst prefix$1 = 'Invariant failed';\nclass RbdInvariant extends Error {}\nRbdInvariant.prototype.toString = function toString() {\n  return this.message;\n};\nfunction invariant(condition, message) {\n  if (isProduction) {\n    throw new RbdInvariant(prefix$1);\n  } else {\n    throw new RbdInvariant(`${prefix$1}: ${message || ''}`);\n  }\n}\n\nclass ErrorBoundary extends React.Component {\n  constructor(...args) {\n    super(...args);\n    this.callbacks = null;\n    this.unbind = noop$2;\n    this.onWindowError = event => {\n      const callbacks = this.getCallbacks();\n      if (callbacks.isDragging()) {\n        callbacks.tryAbort();\n        process.env.NODE_ENV !== \"production\" ? warning(`\n        An error was caught by our window 'error' event listener while a drag was occurring.\n        The active drag has been aborted.\n      `) : void 0;\n      }\n      const err = event.error;\n      if (err instanceof RbdInvariant) {\n        event.preventDefault();\n        if (process.env.NODE_ENV !== 'production') {\n          error(err.message);\n        }\n      }\n    };\n    this.getCallbacks = () => {\n      if (!this.callbacks) {\n        throw new Error('Unable to find AppCallbacks in <ErrorBoundary/>');\n      }\n      return this.callbacks;\n    };\n    this.setCallbacks = callbacks => {\n      this.callbacks = callbacks;\n    };\n  }\n  componentDidMount() {\n    this.unbind = bindEvents(window, [{\n      eventName: 'error',\n      fn: this.onWindowError\n    }]);\n  }\n  componentDidCatch(err) {\n    if (err instanceof RbdInvariant) {\n      if (process.env.NODE_ENV !== 'production') {\n        error(err.message);\n      }\n      this.setState({});\n      return;\n    }\n    throw err;\n  }\n  componentWillUnmount() {\n    this.unbind();\n  }\n  render() {\n    return this.props.children(this.setCallbacks);\n  }\n}\n\nconst dragHandleUsageInstructions = `\n  Press space bar to start a drag.\n  When dragging you can use the arrow keys to move the item around and escape to cancel.\n  Some screen readers may require you to be in focus mode or to use your pass through key\n`;\nconst position = index => index + 1;\nconst onDragStart = start => `\n  You have lifted an item in position ${position(start.source.index)}\n`;\nconst withLocation = (source, destination) => {\n  const isInHomeList = source.droppableId === destination.droppableId;\n  const startPosition = position(source.index);\n  const endPosition = position(destination.index);\n  if (isInHomeList) {\n    return `\n      You have moved the item from position ${startPosition}\n      to position ${endPosition}\n    `;\n  }\n  return `\n    You have moved the item from position ${startPosition}\n    in list ${source.droppableId}\n    to list ${destination.droppableId}\n    in position ${endPosition}\n  `;\n};\nconst withCombine = (id, source, combine) => {\n  const inHomeList = source.droppableId === combine.droppableId;\n  if (inHomeList) {\n    return `\n      The item ${id}\n      has been combined with ${combine.draggableId}`;\n  }\n  return `\n      The item ${id}\n      in list ${source.droppableId}\n      has been combined with ${combine.draggableId}\n      in list ${combine.droppableId}\n    `;\n};\nconst onDragUpdate = update => {\n  const location = update.destination;\n  if (location) {\n    return withLocation(update.source, location);\n  }\n  const combine = update.combine;\n  if (combine) {\n    return withCombine(update.draggableId, update.source, combine);\n  }\n  return 'You are over an area that cannot be dropped on';\n};\nconst returnedToStart = source => `\n  The item has returned to its starting position\n  of ${position(source.index)}\n`;\nconst onDragEnd = result => {\n  if (result.reason === 'CANCEL') {\n    return `\n      Movement cancelled.\n      ${returnedToStart(result.source)}\n    `;\n  }\n  const location = result.destination;\n  const combine = result.combine;\n  if (location) {\n    return `\n      You have dropped the item.\n      ${withLocation(result.source, location)}\n    `;\n  }\n  if (combine) {\n    return `\n      You have dropped the item.\n      ${withCombine(result.draggableId, result.source, combine)}\n    `;\n  }\n  return `\n    The item has been dropped while not over a drop area.\n    ${returnedToStart(result.source)}\n  `;\n};\nconst preset = {\n  dragHandleUsageInstructions,\n  onDragStart,\n  onDragUpdate,\n  onDragEnd\n};\n\nconst origin = {\n  x: 0,\n  y: 0\n};\nconst add = (point1, point2) => ({\n  x: point1.x + point2.x,\n  y: point1.y + point2.y\n});\nconst subtract = (point1, point2) => ({\n  x: point1.x - point2.x,\n  y: point1.y - point2.y\n});\nconst isEqual$1 = (point1, point2) => point1.x === point2.x && point1.y === point2.y;\nconst negate = point => ({\n  x: point.x !== 0 ? -point.x : 0,\n  y: point.y !== 0 ? -point.y : 0\n});\nconst patch = (line, value, otherValue = 0) => {\n  if (line === 'x') {\n    return {\n      x: value,\n      y: otherValue\n    };\n  }\n  return {\n    x: otherValue,\n    y: value\n  };\n};\nconst distance = (point1, point2) => Math.sqrt((point2.x - point1.x) ** 2 + (point2.y - point1.y) ** 2);\nconst closest$1 = (target, points) => Math.min(...points.map(point => distance(target, point)));\nconst apply = fn => point => ({\n  x: fn(point.x),\n  y: fn(point.y)\n});\n\nvar executeClip = (frame, subject) => {\n  const result = getRect({\n    top: Math.max(subject.top, frame.top),\n    right: Math.min(subject.right, frame.right),\n    bottom: Math.min(subject.bottom, frame.bottom),\n    left: Math.max(subject.left, frame.left)\n  });\n  if (result.width <= 0 || result.height <= 0) {\n    return null;\n  }\n  return result;\n};\n\nconst offsetByPosition = (spacing, point) => ({\n  top: spacing.top + point.y,\n  left: spacing.left + point.x,\n  bottom: spacing.bottom + point.y,\n  right: spacing.right + point.x\n});\nconst getCorners = spacing => [{\n  x: spacing.left,\n  y: spacing.top\n}, {\n  x: spacing.right,\n  y: spacing.top\n}, {\n  x: spacing.left,\n  y: spacing.bottom\n}, {\n  x: spacing.right,\n  y: spacing.bottom\n}];\nconst noSpacing = {\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0\n};\n\nconst scroll$1 = (target, frame) => {\n  if (!frame) {\n    return target;\n  }\n  return offsetByPosition(target, frame.scroll.diff.displacement);\n};\nconst increase = (target, axis, withPlaceholder) => {\n  if (withPlaceholder && withPlaceholder.increasedBy) {\n    return {\n      ...target,\n      [axis.end]: target[axis.end] + withPlaceholder.increasedBy[axis.line]\n    };\n  }\n  return target;\n};\nconst clip = (target, frame) => {\n  if (frame && frame.shouldClipSubject) {\n    return executeClip(frame.pageMarginBox, target);\n  }\n  return getRect(target);\n};\nvar getSubject = ({\n  page,\n  withPlaceholder,\n  axis,\n  frame\n}) => {\n  const scrolled = scroll$1(page.marginBox, frame);\n  const increased = increase(scrolled, axis, withPlaceholder);\n  const clipped = clip(increased, frame);\n  return {\n    page,\n    withPlaceholder,\n    active: clipped\n  };\n};\n\nvar scrollDroppable = (droppable, newScroll) => {\n  !droppable.frame ? process.env.NODE_ENV !== \"production\" ? invariant() : invariant() : void 0;\n  const scrollable = droppable.frame;\n  const scrollDiff = subtract(newScroll, scrollable.scroll.initial);\n  const scrollDisplacement = negate(scrollDiff);\n  const frame = {\n    ...scrollable,\n    scroll: {\n      initial: scrollable.scroll.initial,\n      current: newScroll,\n      diff: {\n        value: scrollDiff,\n        displacement: scrollDisplacement\n      },\n      max: scrollable.scroll.max\n    }\n  };\n  const subject = getSubject({\n    page: droppable.subject.page,\n    withPlaceholder: droppable.subject.withPlaceholder,\n    axis: droppable.axis,\n    frame\n  });\n  const result = {\n    ...droppable,\n    frame,\n    subject\n  };\n  return result;\n};\n\nconst toDroppableMap = memoizeOne(droppables => droppables.reduce((previous, current) => {\n  previous[current.descriptor.id] = current;\n  return previous;\n}, {}));\nconst toDraggableMap = memoizeOne(draggables => draggables.reduce((previous, current) => {\n  previous[current.descriptor.id] = current;\n  return previous;\n}, {}));\nconst toDroppableList = memoizeOne(droppables => Object.values(droppables));\nconst toDraggableList = memoizeOne(draggables => Object.values(draggables));\n\nvar getDraggablesInsideDroppable = memoizeOne((droppableId, draggables) => {\n  const result = toDraggableList(draggables).filter(draggable => droppableId === draggable.descriptor.droppableId).sort((a, b) => a.descriptor.index - b.descriptor.index);\n  return result;\n});\n\nfunction tryGetDestination(impact) {\n  if (impact.at && impact.at.type === 'REORDER') {\n    return impact.at.destination;\n  }\n  return null;\n}\nfunction tryGetCombine(impact) {\n  if (impact.at && impact.at.type === 'COMBINE') {\n    return impact.at.combine;\n  }\n  return null;\n}\n\nvar removeDraggableFromList = memoizeOne((remove, list) => list.filter(item => item.descriptor.id !== remove.descriptor.id));\n\nvar moveToNextCombine = ({\n  isMovingForward,\n  draggable,\n  destination,\n  insideDestination,\n  previousImpact\n}) => {\n  if (!destination.isCombineEnabled) {\n    return null;\n  }\n  const location = tryGetDestination(previousImpact);\n  if (!location) {\n    return null;\n  }\n  function getImpact(target) {\n    const at = {\n      type: 'COMBINE',\n      combine: {\n        draggableId: target,\n        droppableId: destination.descriptor.id\n      }\n    };\n    return {\n      ...previousImpact,\n      at\n    };\n  }\n  const all = previousImpact.displaced.all;\n  const closestId = all.length ? all[0] : null;\n  if (isMovingForward) {\n    return closestId ? getImpact(closestId) : null;\n  }\n  const withoutDraggable = removeDraggableFromList(draggable, insideDestination);\n  if (!closestId) {\n    if (!withoutDraggable.length) {\n      return null;\n    }\n    const last = withoutDraggable[withoutDraggable.length - 1];\n    return getImpact(last.descriptor.id);\n  }\n  const indexOfClosest = withoutDraggable.findIndex(d => d.descriptor.id === closestId);\n  !(indexOfClosest !== -1) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find displaced item in set') : invariant() : void 0;\n  const proposedIndex = indexOfClosest - 1;\n  if (proposedIndex < 0) {\n    return null;\n  }\n  const before = withoutDraggable[proposedIndex];\n  return getImpact(before.descriptor.id);\n};\n\nvar isHomeOf = (draggable, destination) => draggable.descriptor.droppableId === destination.descriptor.id;\n\nconst noDisplacedBy = {\n  point: origin,\n  value: 0\n};\nconst emptyGroups = {\n  invisible: {},\n  visible: {},\n  all: []\n};\nconst noImpact = {\n  displaced: emptyGroups,\n  displacedBy: noDisplacedBy,\n  at: null\n};\n\nvar isWithin = (lowerBound, upperBound) => value => lowerBound <= value && value <= upperBound;\n\nvar isPartiallyVisibleThroughFrame = frame => {\n  const isWithinVertical = isWithin(frame.top, frame.bottom);\n  const isWithinHorizontal = isWithin(frame.left, frame.right);\n  return subject => {\n    const isContained = isWithinVertical(subject.top) && isWithinVertical(subject.bottom) && isWithinHorizontal(subject.left) && isWithinHorizontal(subject.right);\n    if (isContained) {\n      return true;\n    }\n    const isPartiallyVisibleVertically = isWithinVertical(subject.top) || isWithinVertical(subject.bottom);\n    const isPartiallyVisibleHorizontally = isWithinHorizontal(subject.left) || isWithinHorizontal(subject.right);\n    const isPartiallyContained = isPartiallyVisibleVertically && isPartiallyVisibleHorizontally;\n    if (isPartiallyContained) {\n      return true;\n    }\n    const isBiggerVertically = subject.top < frame.top && subject.bottom > frame.bottom;\n    const isBiggerHorizontally = subject.left < frame.left && subject.right > frame.right;\n    const isTargetBiggerThanFrame = isBiggerVertically && isBiggerHorizontally;\n    if (isTargetBiggerThanFrame) {\n      return true;\n    }\n    const isTargetBiggerOnOneAxis = isBiggerVertically && isPartiallyVisibleHorizontally || isBiggerHorizontally && isPartiallyVisibleVertically;\n    return isTargetBiggerOnOneAxis;\n  };\n};\n\nvar isTotallyVisibleThroughFrame = frame => {\n  const isWithinVertical = isWithin(frame.top, frame.bottom);\n  const isWithinHorizontal = isWithin(frame.left, frame.right);\n  return subject => {\n    const isContained = isWithinVertical(subject.top) && isWithinVertical(subject.bottom) && isWithinHorizontal(subject.left) && isWithinHorizontal(subject.right);\n    return isContained;\n  };\n};\n\nconst vertical = {\n  direction: 'vertical',\n  line: 'y',\n  crossAxisLine: 'x',\n  start: 'top',\n  end: 'bottom',\n  size: 'height',\n  crossAxisStart: 'left',\n  crossAxisEnd: 'right',\n  crossAxisSize: 'width'\n};\nconst horizontal = {\n  direction: 'horizontal',\n  line: 'x',\n  crossAxisLine: 'y',\n  start: 'left',\n  end: 'right',\n  size: 'width',\n  crossAxisStart: 'top',\n  crossAxisEnd: 'bottom',\n  crossAxisSize: 'height'\n};\n\nvar isTotallyVisibleThroughFrameOnAxis = axis => frame => {\n  const isWithinVertical = isWithin(frame.top, frame.bottom);\n  const isWithinHorizontal = isWithin(frame.left, frame.right);\n  return subject => {\n    if (axis === vertical) {\n      return isWithinVertical(subject.top) && isWithinVertical(subject.bottom);\n    }\n    return isWithinHorizontal(subject.left) && isWithinHorizontal(subject.right);\n  };\n};\n\nconst getDroppableDisplaced = (target, destination) => {\n  const displacement = destination.frame ? destination.frame.scroll.diff.displacement : origin;\n  return offsetByPosition(target, displacement);\n};\nconst isVisibleInDroppable = (target, destination, isVisibleThroughFrameFn) => {\n  if (!destination.subject.active) {\n    return false;\n  }\n  return isVisibleThroughFrameFn(destination.subject.active)(target);\n};\nconst isVisibleInViewport = (target, viewport, isVisibleThroughFrameFn) => isVisibleThroughFrameFn(viewport)(target);\nconst isVisible$1 = ({\n  target: toBeDisplaced,\n  destination,\n  viewport,\n  withDroppableDisplacement,\n  isVisibleThroughFrameFn\n}) => {\n  const displacedTarget = withDroppableDisplacement ? getDroppableDisplaced(toBeDisplaced, destination) : toBeDisplaced;\n  return isVisibleInDroppable(displacedTarget, destination, isVisibleThroughFrameFn) && isVisibleInViewport(displacedTarget, viewport, isVisibleThroughFrameFn);\n};\nconst isPartiallyVisible = args => isVisible$1({\n  ...args,\n  isVisibleThroughFrameFn: isPartiallyVisibleThroughFrame\n});\nconst isTotallyVisible = args => isVisible$1({\n  ...args,\n  isVisibleThroughFrameFn: isTotallyVisibleThroughFrame\n});\nconst isTotallyVisibleOnAxis = args => isVisible$1({\n  ...args,\n  isVisibleThroughFrameFn: isTotallyVisibleThroughFrameOnAxis(args.destination.axis)\n});\n\nconst getShouldAnimate = (id, last, forceShouldAnimate) => {\n  if (typeof forceShouldAnimate === 'boolean') {\n    return forceShouldAnimate;\n  }\n  if (!last) {\n    return true;\n  }\n  const {\n    invisible,\n    visible\n  } = last;\n  if (invisible[id]) {\n    return false;\n  }\n  const previous = visible[id];\n  return previous ? previous.shouldAnimate : true;\n};\nfunction getTarget(draggable, displacedBy) {\n  const marginBox = draggable.page.marginBox;\n  const expandBy = {\n    top: displacedBy.point.y,\n    right: 0,\n    bottom: 0,\n    left: displacedBy.point.x\n  };\n  return getRect(expand(marginBox, expandBy));\n}\nfunction getDisplacementGroups({\n  afterDragging,\n  destination,\n  displacedBy,\n  viewport,\n  forceShouldAnimate,\n  last\n}) {\n  return afterDragging.reduce(function process(groups, draggable) {\n    const target = getTarget(draggable, displacedBy);\n    const id = draggable.descriptor.id;\n    groups.all.push(id);\n    const isVisible = isPartiallyVisible({\n      target,\n      destination,\n      viewport,\n      withDroppableDisplacement: true\n    });\n    if (!isVisible) {\n      groups.invisible[draggable.descriptor.id] = true;\n      return groups;\n    }\n    const shouldAnimate = getShouldAnimate(id, last, forceShouldAnimate);\n    const displacement = {\n      draggableId: id,\n      shouldAnimate\n    };\n    groups.visible[id] = displacement;\n    return groups;\n  }, {\n    all: [],\n    visible: {},\n    invisible: {}\n  });\n}\n\nfunction getIndexOfLastItem(draggables, options) {\n  if (!draggables.length) {\n    return 0;\n  }\n  const indexOfLastItem = draggables[draggables.length - 1].descriptor.index;\n  return options.inHomeList ? indexOfLastItem : indexOfLastItem + 1;\n}\nfunction goAtEnd({\n  insideDestination,\n  inHomeList,\n  displacedBy,\n  destination\n}) {\n  const newIndex = getIndexOfLastItem(insideDestination, {\n    inHomeList\n  });\n  return {\n    displaced: emptyGroups,\n    displacedBy,\n    at: {\n      type: 'REORDER',\n      destination: {\n        droppableId: destination.descriptor.id,\n        index: newIndex\n      }\n    }\n  };\n}\nfunction calculateReorderImpact({\n  draggable,\n  insideDestination,\n  destination,\n  viewport,\n  displacedBy,\n  last,\n  index,\n  forceShouldAnimate\n}) {\n  const inHomeList = isHomeOf(draggable, destination);\n  if (index == null) {\n    return goAtEnd({\n      insideDestination,\n      inHomeList,\n      displacedBy,\n      destination\n    });\n  }\n  const match = insideDestination.find(item => item.descriptor.index === index);\n  if (!match) {\n    return goAtEnd({\n      insideDestination,\n      inHomeList,\n      displacedBy,\n      destination\n    });\n  }\n  const withoutDragging = removeDraggableFromList(draggable, insideDestination);\n  const sliceFrom = insideDestination.indexOf(match);\n  const impacted = withoutDragging.slice(sliceFrom);\n  const displaced = getDisplacementGroups({\n    afterDragging: impacted,\n    destination,\n    displacedBy,\n    last,\n    viewport: viewport.frame,\n    forceShouldAnimate\n  });\n  return {\n    displaced,\n    displacedBy,\n    at: {\n      type: 'REORDER',\n      destination: {\n        droppableId: destination.descriptor.id,\n        index\n      }\n    }\n  };\n}\n\nfunction didStartAfterCritical(draggableId, afterCritical) {\n  return Boolean(afterCritical.effected[draggableId]);\n}\n\nvar fromCombine = ({\n  isMovingForward,\n  destination,\n  draggables,\n  combine,\n  afterCritical\n}) => {\n  if (!destination.isCombineEnabled) {\n    return null;\n  }\n  const combineId = combine.draggableId;\n  const combineWith = draggables[combineId];\n  const combineWithIndex = combineWith.descriptor.index;\n  const didCombineWithStartAfterCritical = didStartAfterCritical(combineId, afterCritical);\n  if (didCombineWithStartAfterCritical) {\n    if (isMovingForward) {\n      return combineWithIndex;\n    }\n    return combineWithIndex - 1;\n  }\n  if (isMovingForward) {\n    return combineWithIndex + 1;\n  }\n  return combineWithIndex;\n};\n\nvar fromReorder = ({\n  isMovingForward,\n  isInHomeList,\n  insideDestination,\n  location\n}) => {\n  if (!insideDestination.length) {\n    return null;\n  }\n  const currentIndex = location.index;\n  const proposedIndex = isMovingForward ? currentIndex + 1 : currentIndex - 1;\n  const firstIndex = insideDestination[0].descriptor.index;\n  const lastIndex = insideDestination[insideDestination.length - 1].descriptor.index;\n  const upperBound = isInHomeList ? lastIndex : lastIndex + 1;\n  if (proposedIndex < firstIndex) {\n    return null;\n  }\n  if (proposedIndex > upperBound) {\n    return null;\n  }\n  return proposedIndex;\n};\n\nvar moveToNextIndex = ({\n  isMovingForward,\n  isInHomeList,\n  draggable,\n  draggables,\n  destination,\n  insideDestination,\n  previousImpact,\n  viewport,\n  afterCritical\n}) => {\n  const wasAt = previousImpact.at;\n  !wasAt ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot move in direction without previous impact location') : invariant() : void 0;\n  if (wasAt.type === 'REORDER') {\n    const newIndex = fromReorder({\n      isMovingForward,\n      isInHomeList,\n      location: wasAt.destination,\n      insideDestination\n    });\n    if (newIndex == null) {\n      return null;\n    }\n    return calculateReorderImpact({\n      draggable,\n      insideDestination,\n      destination,\n      viewport,\n      last: previousImpact.displaced,\n      displacedBy: previousImpact.displacedBy,\n      index: newIndex\n    });\n  }\n  const newIndex = fromCombine({\n    isMovingForward,\n    destination,\n    displaced: previousImpact.displaced,\n    draggables,\n    combine: wasAt.combine,\n    afterCritical\n  });\n  if (newIndex == null) {\n    return null;\n  }\n  return calculateReorderImpact({\n    draggable,\n    insideDestination,\n    destination,\n    viewport,\n    last: previousImpact.displaced,\n    displacedBy: previousImpact.displacedBy,\n    index: newIndex\n  });\n};\n\nvar getCombinedItemDisplacement = ({\n  displaced,\n  afterCritical,\n  combineWith,\n  displacedBy\n}) => {\n  const isDisplaced = Boolean(displaced.visible[combineWith] || displaced.invisible[combineWith]);\n  if (didStartAfterCritical(combineWith, afterCritical)) {\n    return isDisplaced ? origin : negate(displacedBy.point);\n  }\n  return isDisplaced ? displacedBy.point : origin;\n};\n\nvar whenCombining = ({\n  afterCritical,\n  impact,\n  draggables\n}) => {\n  const combine = tryGetCombine(impact);\n  !combine ? process.env.NODE_ENV !== \"production\" ? invariant() : invariant() : void 0;\n  const combineWith = combine.draggableId;\n  const center = draggables[combineWith].page.borderBox.center;\n  const displaceBy = getCombinedItemDisplacement({\n    displaced: impact.displaced,\n    afterCritical,\n    combineWith,\n    displacedBy: impact.displacedBy\n  });\n  return add(center, displaceBy);\n};\n\nconst distanceFromStartToBorderBoxCenter = (axis, box) => box.margin[axis.start] + box.borderBox[axis.size] / 2;\nconst distanceFromEndToBorderBoxCenter = (axis, box) => box.margin[axis.end] + box.borderBox[axis.size] / 2;\nconst getCrossAxisBorderBoxCenter = (axis, target, isMoving) => target[axis.crossAxisStart] + isMoving.margin[axis.crossAxisStart] + isMoving.borderBox[axis.crossAxisSize] / 2;\nconst goAfter = ({\n  axis,\n  moveRelativeTo,\n  isMoving\n}) => patch(axis.line, moveRelativeTo.marginBox[axis.end] + distanceFromStartToBorderBoxCenter(axis, isMoving), getCrossAxisBorderBoxCenter(axis, moveRelativeTo.marginBox, isMoving));\nconst goBefore = ({\n  axis,\n  moveRelativeTo,\n  isMoving\n}) => patch(axis.line, moveRelativeTo.marginBox[axis.start] - distanceFromEndToBorderBoxCenter(axis, isMoving), getCrossAxisBorderBoxCenter(axis, moveRelativeTo.marginBox, isMoving));\nconst goIntoStart = ({\n  axis,\n  moveInto,\n  isMoving\n}) => patch(axis.line, moveInto.contentBox[axis.start] + distanceFromStartToBorderBoxCenter(axis, isMoving), getCrossAxisBorderBoxCenter(axis, moveInto.contentBox, isMoving));\n\nvar whenReordering = ({\n  impact,\n  draggable,\n  draggables,\n  droppable,\n  afterCritical\n}) => {\n  const insideDestination = getDraggablesInsideDroppable(droppable.descriptor.id, draggables);\n  const draggablePage = draggable.page;\n  const axis = droppable.axis;\n  if (!insideDestination.length) {\n    return goIntoStart({\n      axis,\n      moveInto: droppable.page,\n      isMoving: draggablePage\n    });\n  }\n  const {\n    displaced,\n    displacedBy\n  } = impact;\n  const closestAfter = displaced.all[0];\n  if (closestAfter) {\n    const closest = draggables[closestAfter];\n    if (didStartAfterCritical(closestAfter, afterCritical)) {\n      return goBefore({\n        axis,\n        moveRelativeTo: closest.page,\n        isMoving: draggablePage\n      });\n    }\n    const withDisplacement = offset(closest.page, displacedBy.point);\n    return goBefore({\n      axis,\n      moveRelativeTo: withDisplacement,\n      isMoving: draggablePage\n    });\n  }\n  const last = insideDestination[insideDestination.length - 1];\n  if (last.descriptor.id === draggable.descriptor.id) {\n    return draggablePage.borderBox.center;\n  }\n  if (didStartAfterCritical(last.descriptor.id, afterCritical)) {\n    const page = offset(last.page, negate(afterCritical.displacedBy.point));\n    return goAfter({\n      axis,\n      moveRelativeTo: page,\n      isMoving: draggablePage\n    });\n  }\n  return goAfter({\n    axis,\n    moveRelativeTo: last.page,\n    isMoving: draggablePage\n  });\n};\n\nvar withDroppableDisplacement = (droppable, point) => {\n  const frame = droppable.frame;\n  if (!frame) {\n    return point;\n  }\n  return add(point, frame.scroll.diff.displacement);\n};\n\nconst getResultWithoutDroppableDisplacement = ({\n  impact,\n  draggable,\n  droppable,\n  draggables,\n  afterCritical\n}) => {\n  const original = draggable.page.borderBox.center;\n  const at = impact.at;\n  if (!droppable) {\n    return original;\n  }\n  if (!at) {\n    return original;\n  }\n  if (at.type === 'REORDER') {\n    return whenReordering({\n      impact,\n      draggable,\n      draggables,\n      droppable,\n      afterCritical\n    });\n  }\n  return whenCombining({\n    impact,\n    draggables,\n    afterCritical\n  });\n};\nvar getPageBorderBoxCenterFromImpact = args => {\n  const withoutDisplacement = getResultWithoutDroppableDisplacement(args);\n  const droppable = args.droppable;\n  const withDisplacement = droppable ? withDroppableDisplacement(droppable, withoutDisplacement) : withoutDisplacement;\n  return withDisplacement;\n};\n\nvar scrollViewport = (viewport, newScroll) => {\n  const diff = subtract(newScroll, viewport.scroll.initial);\n  const displacement = negate(diff);\n  const frame = getRect({\n    top: newScroll.y,\n    bottom: newScroll.y + viewport.frame.height,\n    left: newScroll.x,\n    right: newScroll.x + viewport.frame.width\n  });\n  const updated = {\n    frame,\n    scroll: {\n      initial: viewport.scroll.initial,\n      max: viewport.scroll.max,\n      current: newScroll,\n      diff: {\n        value: diff,\n        displacement\n      }\n    }\n  };\n  return updated;\n};\n\nfunction getDraggables$1(ids, draggables) {\n  return ids.map(id => draggables[id]);\n}\nfunction tryGetVisible(id, groups) {\n  for (let i = 0; i < groups.length; i++) {\n    const displacement = groups[i].visible[id];\n    if (displacement) {\n      return displacement;\n    }\n  }\n  return null;\n}\nvar speculativelyIncrease = ({\n  impact,\n  viewport,\n  destination,\n  draggables,\n  maxScrollChange\n}) => {\n  const scrolledViewport = scrollViewport(viewport, add(viewport.scroll.current, maxScrollChange));\n  const scrolledDroppable = destination.frame ? scrollDroppable(destination, add(destination.frame.scroll.current, maxScrollChange)) : destination;\n  const last = impact.displaced;\n  const withViewportScroll = getDisplacementGroups({\n    afterDragging: getDraggables$1(last.all, draggables),\n    destination,\n    displacedBy: impact.displacedBy,\n    viewport: scrolledViewport.frame,\n    last,\n    forceShouldAnimate: false\n  });\n  const withDroppableScroll = getDisplacementGroups({\n    afterDragging: getDraggables$1(last.all, draggables),\n    destination: scrolledDroppable,\n    displacedBy: impact.displacedBy,\n    viewport: viewport.frame,\n    last,\n    forceShouldAnimate: false\n  });\n  const invisible = {};\n  const visible = {};\n  const groups = [last, withViewportScroll, withDroppableScroll];\n  last.all.forEach(id => {\n    const displacement = tryGetVisible(id, groups);\n    if (displacement) {\n      visible[id] = displacement;\n      return;\n    }\n    invisible[id] = true;\n  });\n  const newImpact = {\n    ...impact,\n    displaced: {\n      all: last.all,\n      invisible,\n      visible\n    }\n  };\n  return newImpact;\n};\n\nvar withViewportDisplacement = (viewport, point) => add(viewport.scroll.diff.displacement, point);\n\nvar getClientFromPageBorderBoxCenter = ({\n  pageBorderBoxCenter,\n  draggable,\n  viewport\n}) => {\n  const withoutPageScrollChange = withViewportDisplacement(viewport, pageBorderBoxCenter);\n  const offset = subtract(withoutPageScrollChange, draggable.page.borderBox.center);\n  return add(draggable.client.borderBox.center, offset);\n};\n\nvar isTotallyVisibleInNewLocation = ({\n  draggable,\n  destination,\n  newPageBorderBoxCenter,\n  viewport,\n  withDroppableDisplacement,\n  onlyOnMainAxis = false\n}) => {\n  const changeNeeded = subtract(newPageBorderBoxCenter, draggable.page.borderBox.center);\n  const shifted = offsetByPosition(draggable.page.borderBox, changeNeeded);\n  const args = {\n    target: shifted,\n    destination,\n    withDroppableDisplacement,\n    viewport\n  };\n  return onlyOnMainAxis ? isTotallyVisibleOnAxis(args) : isTotallyVisible(args);\n};\n\nvar moveToNextPlace = ({\n  isMovingForward,\n  draggable,\n  destination,\n  draggables,\n  previousImpact,\n  viewport,\n  previousPageBorderBoxCenter,\n  previousClientSelection,\n  afterCritical\n}) => {\n  if (!destination.isEnabled) {\n    return null;\n  }\n  const insideDestination = getDraggablesInsideDroppable(destination.descriptor.id, draggables);\n  const isInHomeList = isHomeOf(draggable, destination);\n  const impact = moveToNextCombine({\n    isMovingForward,\n    draggable,\n    destination,\n    insideDestination,\n    previousImpact\n  }) || moveToNextIndex({\n    isMovingForward,\n    isInHomeList,\n    draggable,\n    draggables,\n    destination,\n    insideDestination,\n    previousImpact,\n    viewport,\n    afterCritical\n  });\n  if (!impact) {\n    return null;\n  }\n  const pageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n    impact,\n    draggable,\n    droppable: destination,\n    draggables,\n    afterCritical\n  });\n  const isVisibleInNewLocation = isTotallyVisibleInNewLocation({\n    draggable,\n    destination,\n    newPageBorderBoxCenter: pageBorderBoxCenter,\n    viewport: viewport.frame,\n    withDroppableDisplacement: false,\n    onlyOnMainAxis: true\n  });\n  if (isVisibleInNewLocation) {\n    const clientSelection = getClientFromPageBorderBoxCenter({\n      pageBorderBoxCenter,\n      draggable,\n      viewport\n    });\n    return {\n      clientSelection,\n      impact,\n      scrollJumpRequest: null\n    };\n  }\n  const distance = subtract(pageBorderBoxCenter, previousPageBorderBoxCenter);\n  const cautious = speculativelyIncrease({\n    impact,\n    viewport,\n    destination,\n    draggables,\n    maxScrollChange: distance\n  });\n  return {\n    clientSelection: previousClientSelection,\n    impact: cautious,\n    scrollJumpRequest: distance\n  };\n};\n\nconst getKnownActive = droppable => {\n  const rect = droppable.subject.active;\n  !rect ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot get clipped area from droppable') : invariant() : void 0;\n  return rect;\n};\nvar getBestCrossAxisDroppable = ({\n  isMovingForward,\n  pageBorderBoxCenter,\n  source,\n  droppables,\n  viewport\n}) => {\n  const active = source.subject.active;\n  if (!active) {\n    return null;\n  }\n  const axis = source.axis;\n  const isBetweenSourceClipped = isWithin(active[axis.start], active[axis.end]);\n  const candidates = toDroppableList(droppables).filter(droppable => droppable !== source).filter(droppable => droppable.isEnabled).filter(droppable => Boolean(droppable.subject.active)).filter(droppable => isPartiallyVisibleThroughFrame(viewport.frame)(getKnownActive(droppable))).filter(droppable => {\n    const activeOfTarget = getKnownActive(droppable);\n    if (isMovingForward) {\n      return active[axis.crossAxisEnd] < activeOfTarget[axis.crossAxisEnd];\n    }\n    return activeOfTarget[axis.crossAxisStart] < active[axis.crossAxisStart];\n  }).filter(droppable => {\n    const activeOfTarget = getKnownActive(droppable);\n    const isBetweenDestinationClipped = isWithin(activeOfTarget[axis.start], activeOfTarget[axis.end]);\n    return isBetweenSourceClipped(activeOfTarget[axis.start]) || isBetweenSourceClipped(activeOfTarget[axis.end]) || isBetweenDestinationClipped(active[axis.start]) || isBetweenDestinationClipped(active[axis.end]);\n  }).sort((a, b) => {\n    const first = getKnownActive(a)[axis.crossAxisStart];\n    const second = getKnownActive(b)[axis.crossAxisStart];\n    if (isMovingForward) {\n      return first - second;\n    }\n    return second - first;\n  }).filter((droppable, index, array) => getKnownActive(droppable)[axis.crossAxisStart] === getKnownActive(array[0])[axis.crossAxisStart]);\n  if (!candidates.length) {\n    return null;\n  }\n  if (candidates.length === 1) {\n    return candidates[0];\n  }\n  const contains = candidates.filter(droppable => {\n    const isWithinDroppable = isWithin(getKnownActive(droppable)[axis.start], getKnownActive(droppable)[axis.end]);\n    return isWithinDroppable(pageBorderBoxCenter[axis.line]);\n  });\n  if (contains.length === 1) {\n    return contains[0];\n  }\n  if (contains.length > 1) {\n    return contains.sort((a, b) => getKnownActive(a)[axis.start] - getKnownActive(b)[axis.start])[0];\n  }\n  return candidates.sort((a, b) => {\n    const first = closest$1(pageBorderBoxCenter, getCorners(getKnownActive(a)));\n    const second = closest$1(pageBorderBoxCenter, getCorners(getKnownActive(b)));\n    if (first !== second) {\n      return first - second;\n    }\n    return getKnownActive(a)[axis.start] - getKnownActive(b)[axis.start];\n  })[0];\n};\n\nconst getCurrentPageBorderBoxCenter = (draggable, afterCritical) => {\n  const original = draggable.page.borderBox.center;\n  return didStartAfterCritical(draggable.descriptor.id, afterCritical) ? subtract(original, afterCritical.displacedBy.point) : original;\n};\nconst getCurrentPageBorderBox = (draggable, afterCritical) => {\n  const original = draggable.page.borderBox;\n  return didStartAfterCritical(draggable.descriptor.id, afterCritical) ? offsetByPosition(original, negate(afterCritical.displacedBy.point)) : original;\n};\n\nvar getClosestDraggable = ({\n  pageBorderBoxCenter,\n  viewport,\n  destination,\n  insideDestination,\n  afterCritical\n}) => {\n  const sorted = insideDestination.filter(draggable => isTotallyVisible({\n    target: getCurrentPageBorderBox(draggable, afterCritical),\n    destination,\n    viewport: viewport.frame,\n    withDroppableDisplacement: true\n  })).sort((a, b) => {\n    const distanceToA = distance(pageBorderBoxCenter, withDroppableDisplacement(destination, getCurrentPageBorderBoxCenter(a, afterCritical)));\n    const distanceToB = distance(pageBorderBoxCenter, withDroppableDisplacement(destination, getCurrentPageBorderBoxCenter(b, afterCritical)));\n    if (distanceToA < distanceToB) {\n      return -1;\n    }\n    if (distanceToB < distanceToA) {\n      return 1;\n    }\n    return a.descriptor.index - b.descriptor.index;\n  });\n  return sorted[0] || null;\n};\n\nvar getDisplacedBy = memoizeOne(function getDisplacedBy(axis, displaceBy) {\n  const displacement = displaceBy[axis.line];\n  return {\n    value: displacement,\n    point: patch(axis.line, displacement)\n  };\n});\n\nconst getRequiredGrowthForPlaceholder = (droppable, placeholderSize, draggables) => {\n  const axis = droppable.axis;\n  if (droppable.descriptor.mode === 'virtual') {\n    return patch(axis.line, placeholderSize[axis.line]);\n  }\n  const availableSpace = droppable.subject.page.contentBox[axis.size];\n  const insideDroppable = getDraggablesInsideDroppable(droppable.descriptor.id, draggables);\n  const spaceUsed = insideDroppable.reduce((sum, dimension) => sum + dimension.client.marginBox[axis.size], 0);\n  const requiredSpace = spaceUsed + placeholderSize[axis.line];\n  const needsToGrowBy = requiredSpace - availableSpace;\n  if (needsToGrowBy <= 0) {\n    return null;\n  }\n  return patch(axis.line, needsToGrowBy);\n};\nconst withMaxScroll = (frame, max) => ({\n  ...frame,\n  scroll: {\n    ...frame.scroll,\n    max\n  }\n});\nconst addPlaceholder = (droppable, draggable, draggables) => {\n  const frame = droppable.frame;\n  !!isHomeOf(draggable, droppable) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Should not add placeholder space to home list') : invariant() : void 0;\n  !!droppable.subject.withPlaceholder ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot add placeholder size to a subject when it already has one') : invariant() : void 0;\n  const placeholderSize = getDisplacedBy(droppable.axis, draggable.displaceBy).point;\n  const requiredGrowth = getRequiredGrowthForPlaceholder(droppable, placeholderSize, draggables);\n  const added = {\n    placeholderSize,\n    increasedBy: requiredGrowth,\n    oldFrameMaxScroll: droppable.frame ? droppable.frame.scroll.max : null\n  };\n  if (!frame) {\n    const subject = getSubject({\n      page: droppable.subject.page,\n      withPlaceholder: added,\n      axis: droppable.axis,\n      frame: droppable.frame\n    });\n    return {\n      ...droppable,\n      subject\n    };\n  }\n  const maxScroll = requiredGrowth ? add(frame.scroll.max, requiredGrowth) : frame.scroll.max;\n  const newFrame = withMaxScroll(frame, maxScroll);\n  const subject = getSubject({\n    page: droppable.subject.page,\n    withPlaceholder: added,\n    axis: droppable.axis,\n    frame: newFrame\n  });\n  return {\n    ...droppable,\n    subject,\n    frame: newFrame\n  };\n};\nconst removePlaceholder = droppable => {\n  const added = droppable.subject.withPlaceholder;\n  !added ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot remove placeholder form subject when there was none') : invariant() : void 0;\n  const frame = droppable.frame;\n  if (!frame) {\n    const subject = getSubject({\n      page: droppable.subject.page,\n      axis: droppable.axis,\n      frame: null,\n      withPlaceholder: null\n    });\n    return {\n      ...droppable,\n      subject\n    };\n  }\n  const oldMaxScroll = added.oldFrameMaxScroll;\n  !oldMaxScroll ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected droppable with frame to have old max frame scroll when removing placeholder') : invariant() : void 0;\n  const newFrame = withMaxScroll(frame, oldMaxScroll);\n  const subject = getSubject({\n    page: droppable.subject.page,\n    axis: droppable.axis,\n    frame: newFrame,\n    withPlaceholder: null\n  });\n  return {\n    ...droppable,\n    subject,\n    frame: newFrame\n  };\n};\n\nvar moveToNewDroppable = ({\n  previousPageBorderBoxCenter,\n  moveRelativeTo,\n  insideDestination,\n  draggable,\n  draggables,\n  destination,\n  viewport,\n  afterCritical\n}) => {\n  if (!moveRelativeTo) {\n    if (insideDestination.length) {\n      return null;\n    }\n    const proposed = {\n      displaced: emptyGroups,\n      displacedBy: noDisplacedBy,\n      at: {\n        type: 'REORDER',\n        destination: {\n          droppableId: destination.descriptor.id,\n          index: 0\n        }\n      }\n    };\n    const proposedPageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n      impact: proposed,\n      draggable,\n      droppable: destination,\n      draggables,\n      afterCritical\n    });\n    const withPlaceholder = isHomeOf(draggable, destination) ? destination : addPlaceholder(destination, draggable, draggables);\n    const isVisibleInNewLocation = isTotallyVisibleInNewLocation({\n      draggable,\n      destination: withPlaceholder,\n      newPageBorderBoxCenter: proposedPageBorderBoxCenter,\n      viewport: viewport.frame,\n      withDroppableDisplacement: false,\n      onlyOnMainAxis: true\n    });\n    return isVisibleInNewLocation ? proposed : null;\n  }\n  const isGoingBeforeTarget = Boolean(previousPageBorderBoxCenter[destination.axis.line] <= moveRelativeTo.page.borderBox.center[destination.axis.line]);\n  const proposedIndex = (() => {\n    const relativeTo = moveRelativeTo.descriptor.index;\n    if (moveRelativeTo.descriptor.id === draggable.descriptor.id) {\n      return relativeTo;\n    }\n    if (isGoingBeforeTarget) {\n      return relativeTo;\n    }\n    return relativeTo + 1;\n  })();\n  const displacedBy = getDisplacedBy(destination.axis, draggable.displaceBy);\n  return calculateReorderImpact({\n    draggable,\n    insideDestination,\n    destination,\n    viewport,\n    displacedBy,\n    last: emptyGroups,\n    index: proposedIndex\n  });\n};\n\nvar moveCrossAxis = ({\n  isMovingForward,\n  previousPageBorderBoxCenter,\n  draggable,\n  isOver,\n  draggables,\n  droppables,\n  viewport,\n  afterCritical\n}) => {\n  const destination = getBestCrossAxisDroppable({\n    isMovingForward,\n    pageBorderBoxCenter: previousPageBorderBoxCenter,\n    source: isOver,\n    droppables,\n    viewport\n  });\n  if (!destination) {\n    return null;\n  }\n  const insideDestination = getDraggablesInsideDroppable(destination.descriptor.id, draggables);\n  const moveRelativeTo = getClosestDraggable({\n    pageBorderBoxCenter: previousPageBorderBoxCenter,\n    viewport,\n    destination,\n    insideDestination,\n    afterCritical\n  });\n  const impact = moveToNewDroppable({\n    previousPageBorderBoxCenter,\n    destination,\n    draggable,\n    draggables,\n    moveRelativeTo,\n    insideDestination,\n    viewport,\n    afterCritical\n  });\n  if (!impact) {\n    return null;\n  }\n  const pageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n    impact,\n    draggable,\n    droppable: destination,\n    draggables,\n    afterCritical\n  });\n  const clientSelection = getClientFromPageBorderBoxCenter({\n    pageBorderBoxCenter,\n    draggable,\n    viewport\n  });\n  return {\n    clientSelection,\n    impact,\n    scrollJumpRequest: null\n  };\n};\n\nvar whatIsDraggedOver = impact => {\n  const at = impact.at;\n  if (!at) {\n    return null;\n  }\n  if (at.type === 'REORDER') {\n    return at.destination.droppableId;\n  }\n  return at.combine.droppableId;\n};\n\nconst getDroppableOver$1 = (impact, droppables) => {\n  const id = whatIsDraggedOver(impact);\n  return id ? droppables[id] : null;\n};\nvar moveInDirection = ({\n  state,\n  type\n}) => {\n  const isActuallyOver = getDroppableOver$1(state.impact, state.dimensions.droppables);\n  const isMainAxisMovementAllowed = Boolean(isActuallyOver);\n  const home = state.dimensions.droppables[state.critical.droppable.id];\n  const isOver = isActuallyOver || home;\n  const direction = isOver.axis.direction;\n  const isMovingOnMainAxis = direction === 'vertical' && (type === 'MOVE_UP' || type === 'MOVE_DOWN') || direction === 'horizontal' && (type === 'MOVE_LEFT' || type === 'MOVE_RIGHT');\n  if (isMovingOnMainAxis && !isMainAxisMovementAllowed) {\n    return null;\n  }\n  const isMovingForward = type === 'MOVE_DOWN' || type === 'MOVE_RIGHT';\n  const draggable = state.dimensions.draggables[state.critical.draggable.id];\n  const previousPageBorderBoxCenter = state.current.page.borderBoxCenter;\n  const {\n    draggables,\n    droppables\n  } = state.dimensions;\n  return isMovingOnMainAxis ? moveToNextPlace({\n    isMovingForward,\n    previousPageBorderBoxCenter,\n    draggable,\n    destination: isOver,\n    draggables,\n    viewport: state.viewport,\n    previousClientSelection: state.current.client.selection,\n    previousImpact: state.impact,\n    afterCritical: state.afterCritical\n  }) : moveCrossAxis({\n    isMovingForward,\n    previousPageBorderBoxCenter,\n    draggable,\n    isOver,\n    draggables,\n    droppables,\n    viewport: state.viewport,\n    afterCritical: state.afterCritical\n  });\n};\n\nfunction isMovementAllowed(state) {\n  return state.phase === 'DRAGGING' || state.phase === 'COLLECTING';\n}\n\nfunction isPositionInFrame(frame) {\n  const isWithinVertical = isWithin(frame.top, frame.bottom);\n  const isWithinHorizontal = isWithin(frame.left, frame.right);\n  return function run(point) {\n    return isWithinVertical(point.y) && isWithinHorizontal(point.x);\n  };\n}\n\nfunction getHasOverlap(first, second) {\n  return first.left < second.right && first.right > second.left && first.top < second.bottom && first.bottom > second.top;\n}\nfunction getFurthestAway({\n  pageBorderBox,\n  draggable,\n  candidates\n}) {\n  const startCenter = draggable.page.borderBox.center;\n  const sorted = candidates.map(candidate => {\n    const axis = candidate.axis;\n    const target = patch(candidate.axis.line, pageBorderBox.center[axis.line], candidate.page.borderBox.center[axis.crossAxisLine]);\n    return {\n      id: candidate.descriptor.id,\n      distance: distance(startCenter, target)\n    };\n  }).sort((a, b) => b.distance - a.distance);\n  return sorted[0] ? sorted[0].id : null;\n}\nfunction getDroppableOver({\n  pageBorderBox,\n  draggable,\n  droppables\n}) {\n  const candidates = toDroppableList(droppables).filter(item => {\n    if (!item.isEnabled) {\n      return false;\n    }\n    const active = item.subject.active;\n    if (!active) {\n      return false;\n    }\n    if (!getHasOverlap(pageBorderBox, active)) {\n      return false;\n    }\n    if (isPositionInFrame(active)(pageBorderBox.center)) {\n      return true;\n    }\n    const axis = item.axis;\n    const childCenter = active.center[axis.crossAxisLine];\n    const crossAxisStart = pageBorderBox[axis.crossAxisStart];\n    const crossAxisEnd = pageBorderBox[axis.crossAxisEnd];\n    const isContained = isWithin(active[axis.crossAxisStart], active[axis.crossAxisEnd]);\n    const isStartContained = isContained(crossAxisStart);\n    const isEndContained = isContained(crossAxisEnd);\n    if (!isStartContained && !isEndContained) {\n      return true;\n    }\n    if (isStartContained) {\n      return crossAxisStart < childCenter;\n    }\n    return crossAxisEnd > childCenter;\n  });\n  if (!candidates.length) {\n    return null;\n  }\n  if (candidates.length === 1) {\n    return candidates[0].descriptor.id;\n  }\n  return getFurthestAway({\n    pageBorderBox,\n    draggable,\n    candidates\n  });\n}\n\nconst offsetRectByPosition = (rect, point) => getRect(offsetByPosition(rect, point));\n\nvar withDroppableScroll = (droppable, area) => {\n  const frame = droppable.frame;\n  if (!frame) {\n    return area;\n  }\n  return offsetRectByPosition(area, frame.scroll.diff.value);\n};\n\nfunction getIsDisplaced({\n  displaced,\n  id\n}) {\n  return Boolean(displaced.visible[id] || displaced.invisible[id]);\n}\n\nfunction atIndex({\n  draggable,\n  closest,\n  inHomeList\n}) {\n  if (!closest) {\n    return null;\n  }\n  if (!inHomeList) {\n    return closest.descriptor.index;\n  }\n  if (closest.descriptor.index > draggable.descriptor.index) {\n    return closest.descriptor.index - 1;\n  }\n  return closest.descriptor.index;\n}\nvar getReorderImpact = ({\n  pageBorderBoxWithDroppableScroll: targetRect,\n  draggable,\n  destination,\n  insideDestination,\n  last,\n  viewport,\n  afterCritical\n}) => {\n  const axis = destination.axis;\n  const displacedBy = getDisplacedBy(destination.axis, draggable.displaceBy);\n  const displacement = displacedBy.value;\n  const targetStart = targetRect[axis.start];\n  const targetEnd = targetRect[axis.end];\n  const withoutDragging = removeDraggableFromList(draggable, insideDestination);\n  const closest = withoutDragging.find(child => {\n    const id = child.descriptor.id;\n    const childCenter = child.page.borderBox.center[axis.line];\n    const didStartAfterCritical$1 = didStartAfterCritical(id, afterCritical);\n    const isDisplaced = getIsDisplaced({\n      displaced: last,\n      id\n    });\n    if (didStartAfterCritical$1) {\n      if (isDisplaced) {\n        return targetEnd <= childCenter;\n      }\n      return targetStart < childCenter - displacement;\n    }\n    if (isDisplaced) {\n      return targetEnd <= childCenter + displacement;\n    }\n    return targetStart < childCenter;\n  }) || null;\n  const newIndex = atIndex({\n    draggable,\n    closest,\n    inHomeList: isHomeOf(draggable, destination)\n  });\n  return calculateReorderImpact({\n    draggable,\n    insideDestination,\n    destination,\n    viewport,\n    last,\n    displacedBy,\n    index: newIndex\n  });\n};\n\nconst combineThresholdDivisor = 4;\nvar getCombineImpact = ({\n  draggable,\n  pageBorderBoxWithDroppableScroll: targetRect,\n  previousImpact,\n  destination,\n  insideDestination,\n  afterCritical\n}) => {\n  if (!destination.isCombineEnabled) {\n    return null;\n  }\n  const axis = destination.axis;\n  const displacedBy = getDisplacedBy(destination.axis, draggable.displaceBy);\n  const displacement = displacedBy.value;\n  const targetStart = targetRect[axis.start];\n  const targetEnd = targetRect[axis.end];\n  const withoutDragging = removeDraggableFromList(draggable, insideDestination);\n  const combineWith = withoutDragging.find(child => {\n    const id = child.descriptor.id;\n    const childRect = child.page.borderBox;\n    const childSize = childRect[axis.size];\n    const threshold = childSize / combineThresholdDivisor;\n    const didStartAfterCritical$1 = didStartAfterCritical(id, afterCritical);\n    const isDisplaced = getIsDisplaced({\n      displaced: previousImpact.displaced,\n      id\n    });\n    if (didStartAfterCritical$1) {\n      if (isDisplaced) {\n        return targetEnd > childRect[axis.start] + threshold && targetEnd < childRect[axis.end] - threshold;\n      }\n      return targetStart > childRect[axis.start] - displacement + threshold && targetStart < childRect[axis.end] - displacement - threshold;\n    }\n    if (isDisplaced) {\n      return targetEnd > childRect[axis.start] + displacement + threshold && targetEnd < childRect[axis.end] + displacement - threshold;\n    }\n    return targetStart > childRect[axis.start] + threshold && targetStart < childRect[axis.end] - threshold;\n  });\n  if (!combineWith) {\n    return null;\n  }\n  const impact = {\n    displacedBy,\n    displaced: previousImpact.displaced,\n    at: {\n      type: 'COMBINE',\n      combine: {\n        draggableId: combineWith.descriptor.id,\n        droppableId: destination.descriptor.id\n      }\n    }\n  };\n  return impact;\n};\n\nvar getDragImpact = ({\n  pageOffset,\n  draggable,\n  draggables,\n  droppables,\n  previousImpact,\n  viewport,\n  afterCritical\n}) => {\n  const pageBorderBox = offsetRectByPosition(draggable.page.borderBox, pageOffset);\n  const destinationId = getDroppableOver({\n    pageBorderBox,\n    draggable,\n    droppables\n  });\n  if (!destinationId) {\n    return noImpact;\n  }\n  const destination = droppables[destinationId];\n  const insideDestination = getDraggablesInsideDroppable(destination.descriptor.id, draggables);\n  const pageBorderBoxWithDroppableScroll = withDroppableScroll(destination, pageBorderBox);\n  return getCombineImpact({\n    pageBorderBoxWithDroppableScroll,\n    draggable,\n    previousImpact,\n    destination,\n    insideDestination,\n    afterCritical\n  }) || getReorderImpact({\n    pageBorderBoxWithDroppableScroll,\n    draggable,\n    destination,\n    insideDestination,\n    last: previousImpact.displaced,\n    viewport,\n    afterCritical\n  });\n};\n\nvar patchDroppableMap = (droppables, updated) => ({\n  ...droppables,\n  [updated.descriptor.id]: updated\n});\n\nconst clearUnusedPlaceholder = ({\n  previousImpact,\n  impact,\n  droppables\n}) => {\n  const last = whatIsDraggedOver(previousImpact);\n  const now = whatIsDraggedOver(impact);\n  if (!last) {\n    return droppables;\n  }\n  if (last === now) {\n    return droppables;\n  }\n  const lastDroppable = droppables[last];\n  if (!lastDroppable.subject.withPlaceholder) {\n    return droppables;\n  }\n  const updated = removePlaceholder(lastDroppable);\n  return patchDroppableMap(droppables, updated);\n};\nvar recomputePlaceholders = ({\n  draggable,\n  draggables,\n  droppables,\n  previousImpact,\n  impact\n}) => {\n  const cleaned = clearUnusedPlaceholder({\n    previousImpact,\n    impact,\n    droppables\n  });\n  const isOver = whatIsDraggedOver(impact);\n  if (!isOver) {\n    return cleaned;\n  }\n  const droppable = droppables[isOver];\n  if (isHomeOf(draggable, droppable)) {\n    return cleaned;\n  }\n  if (droppable.subject.withPlaceholder) {\n    return cleaned;\n  }\n  const patched = addPlaceholder(droppable, draggable, draggables);\n  return patchDroppableMap(cleaned, patched);\n};\n\nvar update = ({\n  state,\n  clientSelection: forcedClientSelection,\n  dimensions: forcedDimensions,\n  viewport: forcedViewport,\n  impact: forcedImpact,\n  scrollJumpRequest\n}) => {\n  const viewport = forcedViewport || state.viewport;\n  const dimensions = forcedDimensions || state.dimensions;\n  const clientSelection = forcedClientSelection || state.current.client.selection;\n  const offset = subtract(clientSelection, state.initial.client.selection);\n  const client = {\n    offset,\n    selection: clientSelection,\n    borderBoxCenter: add(state.initial.client.borderBoxCenter, offset)\n  };\n  const page = {\n    selection: add(client.selection, viewport.scroll.current),\n    borderBoxCenter: add(client.borderBoxCenter, viewport.scroll.current),\n    offset: add(client.offset, viewport.scroll.diff.value)\n  };\n  const current = {\n    client,\n    page\n  };\n  if (state.phase === 'COLLECTING') {\n    return {\n      ...state,\n      dimensions,\n      viewport,\n      current\n    };\n  }\n  const draggable = dimensions.draggables[state.critical.draggable.id];\n  const newImpact = forcedImpact || getDragImpact({\n    pageOffset: page.offset,\n    draggable,\n    draggables: dimensions.draggables,\n    droppables: dimensions.droppables,\n    previousImpact: state.impact,\n    viewport,\n    afterCritical: state.afterCritical\n  });\n  const withUpdatedPlaceholders = recomputePlaceholders({\n    draggable,\n    impact: newImpact,\n    previousImpact: state.impact,\n    draggables: dimensions.draggables,\n    droppables: dimensions.droppables\n  });\n  const result = {\n    ...state,\n    current,\n    dimensions: {\n      draggables: dimensions.draggables,\n      droppables: withUpdatedPlaceholders\n    },\n    impact: newImpact,\n    viewport,\n    scrollJumpRequest: scrollJumpRequest || null,\n    forceShouldAnimate: scrollJumpRequest ? false : null\n  };\n  return result;\n};\n\nfunction getDraggables(ids, draggables) {\n  return ids.map(id => draggables[id]);\n}\nvar recompute = ({\n  impact,\n  viewport,\n  draggables,\n  destination,\n  forceShouldAnimate\n}) => {\n  const last = impact.displaced;\n  const afterDragging = getDraggables(last.all, draggables);\n  const displaced = getDisplacementGroups({\n    afterDragging,\n    destination,\n    displacedBy: impact.displacedBy,\n    viewport: viewport.frame,\n    forceShouldAnimate,\n    last\n  });\n  return {\n    ...impact,\n    displaced\n  };\n};\n\nvar getClientBorderBoxCenter = ({\n  impact,\n  draggable,\n  droppable,\n  draggables,\n  viewport,\n  afterCritical\n}) => {\n  const pageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n    impact,\n    draggable,\n    draggables,\n    droppable,\n    afterCritical\n  });\n  return getClientFromPageBorderBoxCenter({\n    pageBorderBoxCenter,\n    draggable,\n    viewport\n  });\n};\n\nvar refreshSnap = ({\n  state,\n  dimensions: forcedDimensions,\n  viewport: forcedViewport\n}) => {\n  !(state.movementMode === 'SNAP') ? process.env.NODE_ENV !== \"production\" ? invariant() : invariant() : void 0;\n  const needsVisibilityCheck = state.impact;\n  const viewport = forcedViewport || state.viewport;\n  const dimensions = forcedDimensions || state.dimensions;\n  const {\n    draggables,\n    droppables\n  } = dimensions;\n  const draggable = draggables[state.critical.draggable.id];\n  const isOver = whatIsDraggedOver(needsVisibilityCheck);\n  !isOver ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Must be over a destination in SNAP movement mode') : invariant() : void 0;\n  const destination = droppables[isOver];\n  const impact = recompute({\n    impact: needsVisibilityCheck,\n    viewport,\n    destination,\n    draggables\n  });\n  const clientSelection = getClientBorderBoxCenter({\n    impact,\n    draggable,\n    droppable: destination,\n    draggables,\n    viewport,\n    afterCritical: state.afterCritical\n  });\n  return update({\n    impact,\n    clientSelection,\n    state,\n    dimensions,\n    viewport\n  });\n};\n\nvar getHomeLocation = descriptor => ({\n  index: descriptor.index,\n  droppableId: descriptor.droppableId\n});\n\nvar getLiftEffect = ({\n  draggable,\n  home,\n  draggables,\n  viewport\n}) => {\n  const displacedBy = getDisplacedBy(home.axis, draggable.displaceBy);\n  const insideHome = getDraggablesInsideDroppable(home.descriptor.id, draggables);\n  const rawIndex = insideHome.indexOf(draggable);\n  !(rawIndex !== -1) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected draggable to be inside home list') : invariant() : void 0;\n  const afterDragging = insideHome.slice(rawIndex + 1);\n  const effected = afterDragging.reduce((previous, item) => {\n    previous[item.descriptor.id] = true;\n    return previous;\n  }, {});\n  const afterCritical = {\n    inVirtualList: home.descriptor.mode === 'virtual',\n    displacedBy,\n    effected\n  };\n  const displaced = getDisplacementGroups({\n    afterDragging,\n    destination: home,\n    displacedBy,\n    last: null,\n    viewport: viewport.frame,\n    forceShouldAnimate: false\n  });\n  const impact = {\n    displaced,\n    displacedBy,\n    at: {\n      type: 'REORDER',\n      destination: getHomeLocation(draggable.descriptor)\n    }\n  };\n  return {\n    impact,\n    afterCritical\n  };\n};\n\nvar patchDimensionMap = (dimensions, updated) => ({\n  draggables: dimensions.draggables,\n  droppables: patchDroppableMap(dimensions.droppables, updated)\n});\n\nconst start = key => {\n  if (process.env.NODE_ENV !== 'production') {\n    {\n      return;\n    }\n  }\n};\nconst finish = key => {\n  if (process.env.NODE_ENV !== 'production') {\n    {\n      return;\n    }\n  }\n};\n\nvar offsetDraggable = ({\n  draggable,\n  offset: offset$1,\n  initialWindowScroll\n}) => {\n  const client = offset(draggable.client, offset$1);\n  const page = withScroll(client, initialWindowScroll);\n  const moved = {\n    ...draggable,\n    placeholder: {\n      ...draggable.placeholder,\n      client\n    },\n    client,\n    page\n  };\n  return moved;\n};\n\nvar getFrame = droppable => {\n  const frame = droppable.frame;\n  !frame ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected Droppable to have a frame') : invariant() : void 0;\n  return frame;\n};\n\nvar adjustAdditionsForScrollChanges = ({\n  additions,\n  updatedDroppables,\n  viewport\n}) => {\n  const windowScrollChange = viewport.scroll.diff.value;\n  return additions.map(draggable => {\n    const droppableId = draggable.descriptor.droppableId;\n    const modified = updatedDroppables[droppableId];\n    const frame = getFrame(modified);\n    const droppableScrollChange = frame.scroll.diff.value;\n    const totalChange = add(windowScrollChange, droppableScrollChange);\n    const moved = offsetDraggable({\n      draggable,\n      offset: totalChange,\n      initialWindowScroll: viewport.scroll.initial\n    });\n    return moved;\n  });\n};\n\nconst timingsKey = 'Processing dynamic changes';\nvar publishWhileDraggingInVirtual = ({\n  state,\n  published\n}) => {\n  start();\n  const withScrollChange = published.modified.map(update => {\n    const existing = state.dimensions.droppables[update.droppableId];\n    const scrolled = scrollDroppable(existing, update.scroll);\n    return scrolled;\n  });\n  const droppables = {\n    ...state.dimensions.droppables,\n    ...toDroppableMap(withScrollChange)\n  };\n  const updatedAdditions = toDraggableMap(adjustAdditionsForScrollChanges({\n    additions: published.additions,\n    updatedDroppables: droppables,\n    viewport: state.viewport\n  }));\n  const draggables = {\n    ...state.dimensions.draggables,\n    ...updatedAdditions\n  };\n  published.removals.forEach(id => {\n    delete draggables[id];\n  });\n  const dimensions = {\n    droppables,\n    draggables\n  };\n  const wasOverId = whatIsDraggedOver(state.impact);\n  const wasOver = wasOverId ? dimensions.droppables[wasOverId] : null;\n  const draggable = dimensions.draggables[state.critical.draggable.id];\n  const home = dimensions.droppables[state.critical.droppable.id];\n  const {\n    impact: onLiftImpact,\n    afterCritical\n  } = getLiftEffect({\n    draggable,\n    home,\n    draggables,\n    viewport: state.viewport\n  });\n  const previousImpact = wasOver && wasOver.isCombineEnabled ? state.impact : onLiftImpact;\n  const impact = getDragImpact({\n    pageOffset: state.current.page.offset,\n    draggable: dimensions.draggables[state.critical.draggable.id],\n    draggables: dimensions.draggables,\n    droppables: dimensions.droppables,\n    previousImpact,\n    viewport: state.viewport,\n    afterCritical\n  });\n  finish(timingsKey);\n  const draggingState = {\n    ...state,\n    phase: 'DRAGGING',\n    impact,\n    onLiftImpact,\n    dimensions,\n    afterCritical,\n    forceShouldAnimate: false\n  };\n  if (state.phase === 'COLLECTING') {\n    return draggingState;\n  }\n  const dropPending = {\n    ...draggingState,\n    phase: 'DROP_PENDING',\n    reason: state.reason,\n    isWaiting: false\n  };\n  return dropPending;\n};\n\nconst isSnapping = state => state.movementMode === 'SNAP';\nconst postDroppableChange = (state, updated, isEnabledChanging) => {\n  const dimensions = patchDimensionMap(state.dimensions, updated);\n  if (!isSnapping(state) || isEnabledChanging) {\n    return update({\n      state,\n      dimensions\n    });\n  }\n  return refreshSnap({\n    state,\n    dimensions\n  });\n};\nfunction removeScrollJumpRequest(state) {\n  if (state.isDragging && state.movementMode === 'SNAP') {\n    return {\n      ...state,\n      scrollJumpRequest: null\n    };\n  }\n  return state;\n}\nconst idle$2 = {\n  phase: 'IDLE',\n  completed: null,\n  shouldFlush: false\n};\nvar reducer = (state = idle$2, action) => {\n  if (action.type === 'FLUSH') {\n    return {\n      ...idle$2,\n      shouldFlush: true\n    };\n  }\n  if (action.type === 'INITIAL_PUBLISH') {\n    !(state.phase === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'INITIAL_PUBLISH must come after a IDLE phase') : invariant() : void 0;\n    const {\n      critical,\n      clientSelection,\n      viewport,\n      dimensions,\n      movementMode\n    } = action.payload;\n    const draggable = dimensions.draggables[critical.draggable.id];\n    const home = dimensions.droppables[critical.droppable.id];\n    const client = {\n      selection: clientSelection,\n      borderBoxCenter: draggable.client.borderBox.center,\n      offset: origin\n    };\n    const initial = {\n      client,\n      page: {\n        selection: add(client.selection, viewport.scroll.initial),\n        borderBoxCenter: add(client.selection, viewport.scroll.initial),\n        offset: add(client.selection, viewport.scroll.diff.value)\n      }\n    };\n    const isWindowScrollAllowed = toDroppableList(dimensions.droppables).every(item => !item.isFixedOnPage);\n    const {\n      impact,\n      afterCritical\n    } = getLiftEffect({\n      draggable,\n      home,\n      draggables: dimensions.draggables,\n      viewport\n    });\n    const result = {\n      phase: 'DRAGGING',\n      isDragging: true,\n      critical,\n      movementMode,\n      dimensions,\n      initial,\n      current: initial,\n      isWindowScrollAllowed,\n      impact,\n      afterCritical,\n      onLiftImpact: impact,\n      viewport,\n      scrollJumpRequest: null,\n      forceShouldAnimate: null\n    };\n    return result;\n  }\n  if (action.type === 'COLLECTION_STARTING') {\n    if (state.phase === 'COLLECTING' || state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !(state.phase === 'DRAGGING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Collection cannot start from phase ${state.phase}`) : invariant() : void 0;\n    const result = {\n      ...state,\n      phase: 'COLLECTING'\n    };\n    return result;\n  }\n  if (action.type === 'PUBLISH_WHILE_DRAGGING') {\n    !(state.phase === 'COLLECTING' || state.phase === 'DROP_PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Unexpected ${action.type} received in phase ${state.phase}`) : invariant() : void 0;\n    return publishWhileDraggingInVirtual({\n      state,\n      published: action.payload\n    });\n  }\n  if (action.type === 'MOVE') {\n    if (state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `${action.type} not permitted in phase ${state.phase}`) : invariant() : void 0;\n    const {\n      client: clientSelection\n    } = action.payload;\n    if (isEqual$1(clientSelection, state.current.client.selection)) {\n      return state;\n    }\n    return update({\n      state,\n      clientSelection,\n      impact: isSnapping(state) ? state.impact : null\n    });\n  }\n  if (action.type === 'UPDATE_DROPPABLE_SCROLL') {\n    if (state.phase === 'DROP_PENDING') {\n      return removeScrollJumpRequest(state);\n    }\n    if (state.phase === 'COLLECTING') {\n      return removeScrollJumpRequest(state);\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `${action.type} not permitted in phase ${state.phase}`) : invariant() : void 0;\n    const {\n      id,\n      newScroll\n    } = action.payload;\n    const target = state.dimensions.droppables[id];\n    if (!target) {\n      return state;\n    }\n    const scrolled = scrollDroppable(target, newScroll);\n    return postDroppableChange(state, scrolled, false);\n  }\n  if (action.type === 'UPDATE_DROPPABLE_IS_ENABLED') {\n    if (state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Attempting to move in an unsupported phase ${state.phase}`) : invariant() : void 0;\n    const {\n      id,\n      isEnabled\n    } = action.payload;\n    const target = state.dimensions.droppables[id];\n    !target ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot find Droppable[id: ${id}] to toggle its enabled state`) : invariant() : void 0;\n    !(target.isEnabled !== isEnabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Trying to set droppable isEnabled to ${String(isEnabled)}\n      but it is already ${String(target.isEnabled)}`) : invariant() : void 0;\n    const updated = {\n      ...target,\n      isEnabled\n    };\n    return postDroppableChange(state, updated, true);\n  }\n  if (action.type === 'UPDATE_DROPPABLE_IS_COMBINE_ENABLED') {\n    if (state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Attempting to move in an unsupported phase ${state.phase}`) : invariant() : void 0;\n    const {\n      id,\n      isCombineEnabled\n    } = action.payload;\n    const target = state.dimensions.droppables[id];\n    !target ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot find Droppable[id: ${id}] to toggle its isCombineEnabled state`) : invariant() : void 0;\n    !(target.isCombineEnabled !== isCombineEnabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Trying to set droppable isCombineEnabled to ${String(isCombineEnabled)}\n      but it is already ${String(target.isCombineEnabled)}`) : invariant() : void 0;\n    const updated = {\n      ...target,\n      isCombineEnabled\n    };\n    return postDroppableChange(state, updated, true);\n  }\n  if (action.type === 'MOVE_BY_WINDOW_SCROLL') {\n    if (state.phase === 'DROP_PENDING' || state.phase === 'DROP_ANIMATING') {\n      return state;\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot move by window in phase ${state.phase}`) : invariant() : void 0;\n    !state.isWindowScrollAllowed ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Window scrolling is currently not supported for fixed lists') : invariant() : void 0;\n    const newScroll = action.payload.newScroll;\n    if (isEqual$1(state.viewport.scroll.current, newScroll)) {\n      return removeScrollJumpRequest(state);\n    }\n    const viewport = scrollViewport(state.viewport, newScroll);\n    if (isSnapping(state)) {\n      return refreshSnap({\n        state,\n        viewport\n      });\n    }\n    return update({\n      state,\n      viewport\n    });\n  }\n  if (action.type === 'UPDATE_VIEWPORT_MAX_SCROLL') {\n    if (!isMovementAllowed(state)) {\n      return state;\n    }\n    const maxScroll = action.payload.maxScroll;\n    if (isEqual$1(maxScroll, state.viewport.scroll.max)) {\n      return state;\n    }\n    const withMaxScroll = {\n      ...state.viewport,\n      scroll: {\n        ...state.viewport.scroll,\n        max: maxScroll\n      }\n    };\n    return {\n      ...state,\n      viewport: withMaxScroll\n    };\n  }\n  if (action.type === 'MOVE_UP' || action.type === 'MOVE_DOWN' || action.type === 'MOVE_LEFT' || action.type === 'MOVE_RIGHT') {\n    if (state.phase === 'COLLECTING' || state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !(state.phase === 'DRAGGING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `${action.type} received while not in DRAGGING phase`) : invariant() : void 0;\n    const result = moveInDirection({\n      state,\n      type: action.type\n    });\n    if (!result) {\n      return state;\n    }\n    return update({\n      state,\n      impact: result.impact,\n      clientSelection: result.clientSelection,\n      scrollJumpRequest: result.scrollJumpRequest\n    });\n  }\n  if (action.type === 'DROP_PENDING') {\n    const reason = action.payload.reason;\n    !(state.phase === 'COLLECTING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only move into the DROP_PENDING phase from the COLLECTING phase') : invariant() : void 0;\n    const newState = {\n      ...state,\n      phase: 'DROP_PENDING',\n      isWaiting: true,\n      reason\n    };\n    return newState;\n  }\n  if (action.type === 'DROP_ANIMATE') {\n    const {\n      completed,\n      dropDuration,\n      newHomeClientOffset\n    } = action.payload;\n    !(state.phase === 'DRAGGING' || state.phase === 'DROP_PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot animate drop from phase ${state.phase}`) : invariant() : void 0;\n    const result = {\n      phase: 'DROP_ANIMATING',\n      completed,\n      dropDuration,\n      newHomeClientOffset,\n      dimensions: state.dimensions\n    };\n    return result;\n  }\n  if (action.type === 'DROP_COMPLETE') {\n    const {\n      completed\n    } = action.payload;\n    return {\n      phase: 'IDLE',\n      completed,\n      shouldFlush: false\n    };\n  }\n  return state;\n};\n\nfunction guard(action, predicate) {\n  return action instanceof Object && 'type' in action && action.type === predicate;\n}\nconst beforeInitialCapture = args => ({\n  type: 'BEFORE_INITIAL_CAPTURE',\n  payload: args\n});\nconst lift$1 = args => ({\n  type: 'LIFT',\n  payload: args\n});\nconst initialPublish = args => ({\n  type: 'INITIAL_PUBLISH',\n  payload: args\n});\nconst publishWhileDragging = args => ({\n  type: 'PUBLISH_WHILE_DRAGGING',\n  payload: args\n});\nconst collectionStarting = () => ({\n  type: 'COLLECTION_STARTING',\n  payload: null\n});\nconst updateDroppableScroll = args => ({\n  type: 'UPDATE_DROPPABLE_SCROLL',\n  payload: args\n});\nconst updateDroppableIsEnabled = args => ({\n  type: 'UPDATE_DROPPABLE_IS_ENABLED',\n  payload: args\n});\nconst updateDroppableIsCombineEnabled = args => ({\n  type: 'UPDATE_DROPPABLE_IS_COMBINE_ENABLED',\n  payload: args\n});\nconst move = args => ({\n  type: 'MOVE',\n  payload: args\n});\nconst moveByWindowScroll = args => ({\n  type: 'MOVE_BY_WINDOW_SCROLL',\n  payload: args\n});\nconst updateViewportMaxScroll = args => ({\n  type: 'UPDATE_VIEWPORT_MAX_SCROLL',\n  payload: args\n});\nconst moveUp = () => ({\n  type: 'MOVE_UP',\n  payload: null\n});\nconst moveDown = () => ({\n  type: 'MOVE_DOWN',\n  payload: null\n});\nconst moveRight = () => ({\n  type: 'MOVE_RIGHT',\n  payload: null\n});\nconst moveLeft = () => ({\n  type: 'MOVE_LEFT',\n  payload: null\n});\nconst flush = () => ({\n  type: 'FLUSH',\n  payload: null\n});\nconst animateDrop = args => ({\n  type: 'DROP_ANIMATE',\n  payload: args\n});\nconst completeDrop = args => ({\n  type: 'DROP_COMPLETE',\n  payload: args\n});\nconst drop = args => ({\n  type: 'DROP',\n  payload: args\n});\nconst dropPending = args => ({\n  type: 'DROP_PENDING',\n  payload: args\n});\nconst dropAnimationFinished = () => ({\n  type: 'DROP_ANIMATION_FINISHED',\n  payload: null\n});\n\nfunction checkIndexes(insideDestination) {\n  if (insideDestination.length <= 1) {\n    return;\n  }\n  const indexes = insideDestination.map(d => d.descriptor.index);\n  const errors = {};\n  for (let i = 1; i < indexes.length; i++) {\n    const current = indexes[i];\n    const previous = indexes[i - 1];\n    if (current !== previous + 1) {\n      errors[current] = true;\n    }\n  }\n  if (!Object.keys(errors).length) {\n    return;\n  }\n  const formatted = indexes.map(index => {\n    const hasError = Boolean(errors[index]);\n    return hasError ? `[🔥${index}]` : `${index}`;\n  }).join(', ');\n  process.env.NODE_ENV !== \"production\" ? warning(`\n    Detected non-consecutive <Draggable /> indexes.\n\n    (This can cause unexpected bugs)\n\n    ${formatted}\n  `) : void 0;\n}\nfunction validateDimensions(critical, dimensions) {\n  if (process.env.NODE_ENV !== 'production') {\n    const insideDestination = getDraggablesInsideDroppable(critical.droppable.id, dimensions.draggables);\n    checkIndexes(insideDestination);\n  }\n}\n\nvar lift = marshal => ({\n  getState,\n  dispatch\n}) => next => action => {\n  if (!guard(action, 'LIFT')) {\n    next(action);\n    return;\n  }\n  const {\n    id,\n    clientSelection,\n    movementMode\n  } = action.payload;\n  const initial = getState();\n  if (initial.phase === 'DROP_ANIMATING') {\n    dispatch(completeDrop({\n      completed: initial.completed\n    }));\n  }\n  !(getState().phase === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Unexpected phase to start a drag') : invariant() : void 0;\n  dispatch(flush());\n  dispatch(beforeInitialCapture({\n    draggableId: id,\n    movementMode\n  }));\n  const scrollOptions = {\n    shouldPublishImmediately: movementMode === 'SNAP'\n  };\n  const request = {\n    draggableId: id,\n    scrollOptions\n  };\n  const {\n    critical,\n    dimensions,\n    viewport\n  } = marshal.startPublishing(request);\n  validateDimensions(critical, dimensions);\n  dispatch(initialPublish({\n    critical,\n    dimensions,\n    clientSelection,\n    movementMode,\n    viewport\n  }));\n};\n\nvar style = marshal => () => next => action => {\n  if (guard(action, 'INITIAL_PUBLISH')) {\n    marshal.dragging();\n  }\n  if (guard(action, 'DROP_ANIMATE')) {\n    marshal.dropping(action.payload.completed.result.reason);\n  }\n  if (guard(action, 'FLUSH') || guard(action, 'DROP_COMPLETE')) {\n    marshal.resting();\n  }\n  next(action);\n};\n\nconst curves = {\n  outOfTheWay: 'cubic-bezier(0.2, 0, 0, 1)',\n  drop: 'cubic-bezier(.2,1,.1,1)'\n};\nconst combine = {\n  opacity: {\n    drop: 0,\n    combining: 0.7\n  },\n  scale: {\n    drop: 0.75\n  }\n};\nconst timings = {\n  outOfTheWay: 0.2,\n  minDropTime: 0.33,\n  maxDropTime: 0.55\n};\nconst outOfTheWayTiming = `${timings.outOfTheWay}s ${curves.outOfTheWay}`;\nconst transitions = {\n  fluid: `opacity ${outOfTheWayTiming}`,\n  snap: `transform ${outOfTheWayTiming}, opacity ${outOfTheWayTiming}`,\n  drop: duration => {\n    const timing = `${duration}s ${curves.drop}`;\n    return `transform ${timing}, opacity ${timing}`;\n  },\n  outOfTheWay: `transform ${outOfTheWayTiming}`,\n  placeholder: `height ${outOfTheWayTiming}, width ${outOfTheWayTiming}, margin ${outOfTheWayTiming}`\n};\nconst moveTo = offset => isEqual$1(offset, origin) ? undefined : `translate(${offset.x}px, ${offset.y}px)`;\nconst transforms = {\n  moveTo,\n  drop: (offset, isCombining) => {\n    const translate = moveTo(offset);\n    if (!translate) {\n      return undefined;\n    }\n    if (!isCombining) {\n      return translate;\n    }\n    return `${translate} scale(${combine.scale.drop})`;\n  }\n};\n\nconst {\n  minDropTime,\n  maxDropTime\n} = timings;\nconst dropTimeRange = maxDropTime - minDropTime;\nconst maxDropTimeAtDistance = 1500;\nconst cancelDropModifier = 0.6;\nvar getDropDuration = ({\n  current,\n  destination,\n  reason\n}) => {\n  const distance$1 = distance(current, destination);\n  if (distance$1 <= 0) {\n    return minDropTime;\n  }\n  if (distance$1 >= maxDropTimeAtDistance) {\n    return maxDropTime;\n  }\n  const percentage = distance$1 / maxDropTimeAtDistance;\n  const duration = minDropTime + dropTimeRange * percentage;\n  const withDuration = reason === 'CANCEL' ? duration * cancelDropModifier : duration;\n  return Number(withDuration.toFixed(2));\n};\n\nvar getNewHomeClientOffset = ({\n  impact,\n  draggable,\n  dimensions,\n  viewport,\n  afterCritical\n}) => {\n  const {\n    draggables,\n    droppables\n  } = dimensions;\n  const droppableId = whatIsDraggedOver(impact);\n  const destination = droppableId ? droppables[droppableId] : null;\n  const home = droppables[draggable.descriptor.droppableId];\n  const newClientCenter = getClientBorderBoxCenter({\n    impact,\n    draggable,\n    draggables,\n    afterCritical,\n    droppable: destination || home,\n    viewport\n  });\n  const offset = subtract(newClientCenter, draggable.client.borderBox.center);\n  return offset;\n};\n\nvar getDropImpact = ({\n  draggables,\n  reason,\n  lastImpact,\n  home,\n  viewport,\n  onLiftImpact\n}) => {\n  if (!lastImpact.at || reason !== 'DROP') {\n    const recomputedHomeImpact = recompute({\n      draggables,\n      impact: onLiftImpact,\n      destination: home,\n      viewport,\n      forceShouldAnimate: true\n    });\n    return {\n      impact: recomputedHomeImpact,\n      didDropInsideDroppable: false\n    };\n  }\n  if (lastImpact.at.type === 'REORDER') {\n    return {\n      impact: lastImpact,\n      didDropInsideDroppable: true\n    };\n  }\n  const withoutMovement = {\n    ...lastImpact,\n    displaced: emptyGroups\n  };\n  return {\n    impact: withoutMovement,\n    didDropInsideDroppable: true\n  };\n};\n\nconst dropMiddleware = ({\n  getState,\n  dispatch\n}) => next => action => {\n  if (!guard(action, 'DROP')) {\n    next(action);\n    return;\n  }\n  const state = getState();\n  const reason = action.payload.reason;\n  if (state.phase === 'COLLECTING') {\n    dispatch(dropPending({\n      reason\n    }));\n    return;\n  }\n  if (state.phase === 'IDLE') {\n    return;\n  }\n  const isWaitingForDrop = state.phase === 'DROP_PENDING' && state.isWaiting;\n  !!isWaitingForDrop ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'A DROP action occurred while DROP_PENDING and still waiting') : invariant() : void 0;\n  !(state.phase === 'DRAGGING' || state.phase === 'DROP_PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot drop in phase: ${state.phase}`) : invariant() : void 0;\n  const critical = state.critical;\n  const dimensions = state.dimensions;\n  const draggable = dimensions.draggables[state.critical.draggable.id];\n  const {\n    impact,\n    didDropInsideDroppable\n  } = getDropImpact({\n    reason,\n    lastImpact: state.impact,\n    afterCritical: state.afterCritical,\n    onLiftImpact: state.onLiftImpact,\n    home: state.dimensions.droppables[state.critical.droppable.id],\n    viewport: state.viewport,\n    draggables: state.dimensions.draggables\n  });\n  const destination = didDropInsideDroppable ? tryGetDestination(impact) : null;\n  const combine = didDropInsideDroppable ? tryGetCombine(impact) : null;\n  const source = {\n    index: critical.draggable.index,\n    droppableId: critical.droppable.id\n  };\n  const result = {\n    draggableId: draggable.descriptor.id,\n    type: draggable.descriptor.type,\n    source,\n    reason,\n    mode: state.movementMode,\n    destination,\n    combine\n  };\n  const newHomeClientOffset = getNewHomeClientOffset({\n    impact,\n    draggable,\n    dimensions,\n    viewport: state.viewport,\n    afterCritical: state.afterCritical\n  });\n  const completed = {\n    critical: state.critical,\n    afterCritical: state.afterCritical,\n    result,\n    impact\n  };\n  const isAnimationRequired = !isEqual$1(state.current.client.offset, newHomeClientOffset) || Boolean(result.combine);\n  if (!isAnimationRequired) {\n    dispatch(completeDrop({\n      completed\n    }));\n    return;\n  }\n  const dropDuration = getDropDuration({\n    current: state.current.client.offset,\n    destination: newHomeClientOffset,\n    reason\n  });\n  const args = {\n    newHomeClientOffset,\n    dropDuration,\n    completed\n  };\n  dispatch(animateDrop(args));\n};\n\nvar getWindowScroll = () => ({\n  x: window.pageXOffset,\n  y: window.pageYOffset\n});\n\nfunction getWindowScrollBinding(update) {\n  return {\n    eventName: 'scroll',\n    options: {\n      passive: true,\n      capture: false\n    },\n    fn: event => {\n      if (event.target !== window && event.target !== window.document) {\n        return;\n      }\n      update();\n    }\n  };\n}\nfunction getScrollListener({\n  onWindowScroll\n}) {\n  function updateScroll() {\n    onWindowScroll(getWindowScroll());\n  }\n  const scheduled = rafSchd(updateScroll);\n  const binding = getWindowScrollBinding(scheduled);\n  let unbind = noop$2;\n  function isActive() {\n    return unbind !== noop$2;\n  }\n  function start() {\n    !!isActive() ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot start scroll listener when already active') : invariant() : void 0;\n    unbind = bindEvents(window, [binding]);\n  }\n  function stop() {\n    !isActive() ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot stop scroll listener when not active') : invariant() : void 0;\n    scheduled.cancel();\n    unbind();\n    unbind = noop$2;\n  }\n  return {\n    start,\n    stop,\n    isActive\n  };\n}\n\nconst shouldStop$1 = action => guard(action, 'DROP_COMPLETE') || guard(action, 'DROP_ANIMATE') || guard(action, 'FLUSH');\nconst scrollListener = store => {\n  const listener = getScrollListener({\n    onWindowScroll: newScroll => {\n      store.dispatch(moveByWindowScroll({\n        newScroll\n      }));\n    }\n  });\n  return next => action => {\n    if (!listener.isActive() && guard(action, 'INITIAL_PUBLISH')) {\n      listener.start();\n    }\n    if (listener.isActive() && shouldStop$1(action)) {\n      listener.stop();\n    }\n    next(action);\n  };\n};\n\nvar getExpiringAnnounce = announce => {\n  let wasCalled = false;\n  let isExpired = false;\n  const timeoutId = setTimeout(() => {\n    isExpired = true;\n  });\n  const result = message => {\n    if (wasCalled) {\n      process.env.NODE_ENV !== \"production\" ? warning('Announcement already made. Not making a second announcement') : void 0;\n      return;\n    }\n    if (isExpired) {\n      process.env.NODE_ENV !== \"production\" ? warning(`\n        Announcements cannot be made asynchronously.\n        Default message has already been announced.\n      `) : void 0;\n      return;\n    }\n    wasCalled = true;\n    announce(message);\n    clearTimeout(timeoutId);\n  };\n  result.wasCalled = () => wasCalled;\n  return result;\n};\n\nvar getAsyncMarshal = () => {\n  const entries = [];\n  const execute = timerId => {\n    const index = entries.findIndex(item => item.timerId === timerId);\n    !(index !== -1) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find timer') : invariant() : void 0;\n    const [entry] = entries.splice(index, 1);\n    entry.callback();\n  };\n  const add = fn => {\n    const timerId = setTimeout(() => execute(timerId));\n    const entry = {\n      timerId,\n      callback: fn\n    };\n    entries.push(entry);\n  };\n  const flush = () => {\n    if (!entries.length) {\n      return;\n    }\n    const shallow = [...entries];\n    entries.length = 0;\n    shallow.forEach(entry => {\n      clearTimeout(entry.timerId);\n      entry.callback();\n    });\n  };\n  return {\n    add,\n    flush\n  };\n};\n\nconst areLocationsEqual = (first, second) => {\n  if (first == null && second == null) {\n    return true;\n  }\n  if (first == null || second == null) {\n    return false;\n  }\n  return first.droppableId === second.droppableId && first.index === second.index;\n};\nconst isCombineEqual = (first, second) => {\n  if (first == null && second == null) {\n    return true;\n  }\n  if (first == null || second == null) {\n    return false;\n  }\n  return first.draggableId === second.draggableId && first.droppableId === second.droppableId;\n};\nconst isCriticalEqual = (first, second) => {\n  if (first === second) {\n    return true;\n  }\n  const isDraggableEqual = first.draggable.id === second.draggable.id && first.draggable.droppableId === second.draggable.droppableId && first.draggable.type === second.draggable.type && first.draggable.index === second.draggable.index;\n  const isDroppableEqual = first.droppable.id === second.droppable.id && first.droppable.type === second.droppable.type;\n  return isDraggableEqual && isDroppableEqual;\n};\n\nconst withTimings = (key, fn) => {\n  start();\n  fn();\n  finish();\n};\nconst getDragStart = (critical, mode) => ({\n  draggableId: critical.draggable.id,\n  type: critical.droppable.type,\n  source: {\n    droppableId: critical.droppable.id,\n    index: critical.draggable.index\n  },\n  mode\n});\nfunction execute(responder, data, announce, getDefaultMessage) {\n  if (!responder) {\n    announce(getDefaultMessage(data));\n    return;\n  }\n  const willExpire = getExpiringAnnounce(announce);\n  const provided = {\n    announce: willExpire\n  };\n  responder(data, provided);\n  if (!willExpire.wasCalled()) {\n    announce(getDefaultMessage(data));\n  }\n}\nvar getPublisher = (getResponders, announce) => {\n  const asyncMarshal = getAsyncMarshal();\n  let dragging = null;\n  const beforeCapture = (draggableId, mode) => {\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onBeforeCapture as a drag start has already been published') : invariant() : void 0;\n    withTimings('onBeforeCapture', () => {\n      const fn = getResponders().onBeforeCapture;\n      if (fn) {\n        const before = {\n          draggableId,\n          mode\n        };\n        fn(before);\n      }\n    });\n  };\n  const beforeStart = (critical, mode) => {\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onBeforeDragStart as a drag start has already been published') : invariant() : void 0;\n    withTimings('onBeforeDragStart', () => {\n      const fn = getResponders().onBeforeDragStart;\n      if (fn) {\n        fn(getDragStart(critical, mode));\n      }\n    });\n  };\n  const start = (critical, mode) => {\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onBeforeDragStart as a drag start has already been published') : invariant() : void 0;\n    const data = getDragStart(critical, mode);\n    dragging = {\n      mode,\n      lastCritical: critical,\n      lastLocation: data.source,\n      lastCombine: null\n    };\n    asyncMarshal.add(() => {\n      withTimings('onDragStart', () => execute(getResponders().onDragStart, data, announce, preset.onDragStart));\n    });\n  };\n  const update = (critical, impact) => {\n    const location = tryGetDestination(impact);\n    const combine = tryGetCombine(impact);\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onDragMove when onDragStart has not been called') : invariant() : void 0;\n    const hasCriticalChanged = !isCriticalEqual(critical, dragging.lastCritical);\n    if (hasCriticalChanged) {\n      dragging.lastCritical = critical;\n    }\n    const hasLocationChanged = !areLocationsEqual(dragging.lastLocation, location);\n    if (hasLocationChanged) {\n      dragging.lastLocation = location;\n    }\n    const hasGroupingChanged = !isCombineEqual(dragging.lastCombine, combine);\n    if (hasGroupingChanged) {\n      dragging.lastCombine = combine;\n    }\n    if (!hasCriticalChanged && !hasLocationChanged && !hasGroupingChanged) {\n      return;\n    }\n    const data = {\n      ...getDragStart(critical, dragging.mode),\n      combine,\n      destination: location\n    };\n    asyncMarshal.add(() => {\n      withTimings('onDragUpdate', () => execute(getResponders().onDragUpdate, data, announce, preset.onDragUpdate));\n    });\n  };\n  const flush = () => {\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only flush responders while dragging') : invariant() : void 0;\n    asyncMarshal.flush();\n  };\n  const drop = result => {\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onDragEnd when there is no matching onDragStart') : invariant() : void 0;\n    dragging = null;\n    withTimings('onDragEnd', () => execute(getResponders().onDragEnd, result, announce, preset.onDragEnd));\n  };\n  const abort = () => {\n    if (!dragging) {\n      return;\n    }\n    const result = {\n      ...getDragStart(dragging.lastCritical, dragging.mode),\n      combine: null,\n      destination: null,\n      reason: 'CANCEL'\n    };\n    drop(result);\n  };\n  return {\n    beforeCapture,\n    beforeStart,\n    start,\n    update,\n    flush,\n    drop,\n    abort\n  };\n};\n\nvar responders = (getResponders, announce) => {\n  const publisher = getPublisher(getResponders, announce);\n  return store => next => action => {\n    if (guard(action, 'BEFORE_INITIAL_CAPTURE')) {\n      publisher.beforeCapture(action.payload.draggableId, action.payload.movementMode);\n      return;\n    }\n    if (guard(action, 'INITIAL_PUBLISH')) {\n      const critical = action.payload.critical;\n      publisher.beforeStart(critical, action.payload.movementMode);\n      next(action);\n      publisher.start(critical, action.payload.movementMode);\n      return;\n    }\n    if (guard(action, 'DROP_COMPLETE')) {\n      const result = action.payload.completed.result;\n      publisher.flush();\n      next(action);\n      publisher.drop(result);\n      return;\n    }\n    next(action);\n    if (guard(action, 'FLUSH')) {\n      publisher.abort();\n      return;\n    }\n    const state = store.getState();\n    if (state.phase === 'DRAGGING') {\n      publisher.update(state.critical, state.impact);\n    }\n  };\n};\n\nconst dropAnimationFinishMiddleware = store => next => action => {\n  if (!guard(action, 'DROP_ANIMATION_FINISHED')) {\n    next(action);\n    return;\n  }\n  const state = store.getState();\n  !(state.phase === 'DROP_ANIMATING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot finish a drop animating when no drop is occurring') : invariant() : void 0;\n  store.dispatch(completeDrop({\n    completed: state.completed\n  }));\n};\n\nconst dropAnimationFlushOnScrollMiddleware = store => {\n  let unbind = null;\n  let frameId = null;\n  function clear() {\n    if (frameId) {\n      cancelAnimationFrame(frameId);\n      frameId = null;\n    }\n    if (unbind) {\n      unbind();\n      unbind = null;\n    }\n  }\n  return next => action => {\n    if (guard(action, 'FLUSH') || guard(action, 'DROP_COMPLETE') || guard(action, 'DROP_ANIMATION_FINISHED')) {\n      clear();\n    }\n    next(action);\n    if (!guard(action, 'DROP_ANIMATE')) {\n      return;\n    }\n    const binding = {\n      eventName: 'scroll',\n      options: {\n        capture: true,\n        passive: false,\n        once: true\n      },\n      fn: function flushDropAnimation() {\n        const state = store.getState();\n        if (state.phase === 'DROP_ANIMATING') {\n          store.dispatch(dropAnimationFinished());\n        }\n      }\n    };\n    frameId = requestAnimationFrame(() => {\n      frameId = null;\n      unbind = bindEvents(window, [binding]);\n    });\n  };\n};\n\nvar dimensionMarshalStopper = marshal => () => next => action => {\n  if (guard(action, 'DROP_COMPLETE') || guard(action, 'FLUSH') || guard(action, 'DROP_ANIMATE')) {\n    marshal.stopPublishing();\n  }\n  next(action);\n};\n\nvar focus = marshal => {\n  let isWatching = false;\n  return () => next => action => {\n    if (guard(action, 'INITIAL_PUBLISH')) {\n      isWatching = true;\n      marshal.tryRecordFocus(action.payload.critical.draggable.id);\n      next(action);\n      marshal.tryRestoreFocusRecorded();\n      return;\n    }\n    next(action);\n    if (!isWatching) {\n      return;\n    }\n    if (guard(action, 'FLUSH')) {\n      isWatching = false;\n      marshal.tryRestoreFocusRecorded();\n      return;\n    }\n    if (guard(action, 'DROP_COMPLETE')) {\n      isWatching = false;\n      const result = action.payload.completed.result;\n      if (result.combine) {\n        marshal.tryShiftRecord(result.draggableId, result.combine.draggableId);\n      }\n      marshal.tryRestoreFocusRecorded();\n    }\n  };\n};\n\nconst shouldStop = action => guard(action, 'DROP_COMPLETE') || guard(action, 'DROP_ANIMATE') || guard(action, 'FLUSH');\nvar autoScroll = autoScroller => store => next => action => {\n  if (shouldStop(action)) {\n    autoScroller.stop();\n    next(action);\n    return;\n  }\n  if (guard(action, 'INITIAL_PUBLISH')) {\n    next(action);\n    const state = store.getState();\n    !(state.phase === 'DRAGGING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected phase to be DRAGGING after INITIAL_PUBLISH') : invariant() : void 0;\n    autoScroller.start(state);\n    return;\n  }\n  next(action);\n  autoScroller.scroll(store.getState());\n};\n\nconst pendingDrop = store => next => action => {\n  next(action);\n  if (!guard(action, 'PUBLISH_WHILE_DRAGGING')) {\n    return;\n  }\n  const postActionState = store.getState();\n  if (postActionState.phase !== 'DROP_PENDING') {\n    return;\n  }\n  if (postActionState.isWaiting) {\n    return;\n  }\n  store.dispatch(drop({\n    reason: postActionState.reason\n  }));\n};\n\nconst composeEnhancers = process.env.NODE_ENV !== 'production' && typeof window !== 'undefined' && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__({\n  name: '@hello-pangea/dnd'\n}) : compose;\nvar createStore = ({\n  dimensionMarshal,\n  focusMarshal,\n  styleMarshal,\n  getResponders,\n  announce,\n  autoScroller\n}) => createStore$1(reducer, composeEnhancers(applyMiddleware(style(styleMarshal), dimensionMarshalStopper(dimensionMarshal), lift(dimensionMarshal), dropMiddleware, dropAnimationFinishMiddleware, dropAnimationFlushOnScrollMiddleware, pendingDrop, autoScroll(autoScroller), scrollListener, focus(focusMarshal), responders(getResponders, announce))));\n\nconst clean$1 = () => ({\n  additions: {},\n  removals: {},\n  modified: {}\n});\nfunction createPublisher({\n  registry,\n  callbacks\n}) {\n  let staging = clean$1();\n  let frameId = null;\n  const collect = () => {\n    if (frameId) {\n      return;\n    }\n    callbacks.collectionStarting();\n    frameId = requestAnimationFrame(() => {\n      frameId = null;\n      start();\n      const {\n        additions,\n        removals,\n        modified\n      } = staging;\n      const added = Object.keys(additions).map(id => registry.draggable.getById(id).getDimension(origin)).sort((a, b) => a.descriptor.index - b.descriptor.index);\n      const updated = Object.keys(modified).map(id => {\n        const entry = registry.droppable.getById(id);\n        const scroll = entry.callbacks.getScrollWhileDragging();\n        return {\n          droppableId: id,\n          scroll\n        };\n      });\n      const result = {\n        additions: added,\n        removals: Object.keys(removals),\n        modified: updated\n      };\n      staging = clean$1();\n      finish();\n      callbacks.publish(result);\n    });\n  };\n  const add = entry => {\n    const id = entry.descriptor.id;\n    staging.additions[id] = entry;\n    staging.modified[entry.descriptor.droppableId] = true;\n    if (staging.removals[id]) {\n      delete staging.removals[id];\n    }\n    collect();\n  };\n  const remove = entry => {\n    const descriptor = entry.descriptor;\n    staging.removals[descriptor.id] = true;\n    staging.modified[descriptor.droppableId] = true;\n    if (staging.additions[descriptor.id]) {\n      delete staging.additions[descriptor.id];\n    }\n    collect();\n  };\n  const stop = () => {\n    if (!frameId) {\n      return;\n    }\n    cancelAnimationFrame(frameId);\n    frameId = null;\n    staging = clean$1();\n  };\n  return {\n    add,\n    remove,\n    stop\n  };\n}\n\nvar getMaxScroll = ({\n  scrollHeight,\n  scrollWidth,\n  height,\n  width\n}) => {\n  const maxScroll = subtract({\n    x: scrollWidth,\n    y: scrollHeight\n  }, {\n    x: width,\n    y: height\n  });\n  const adjustedMaxScroll = {\n    x: Math.max(0, maxScroll.x),\n    y: Math.max(0, maxScroll.y)\n  };\n  return adjustedMaxScroll;\n};\n\nvar getDocumentElement = () => {\n  const doc = document.documentElement;\n  !doc ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot find document.documentElement') : invariant() : void 0;\n  return doc;\n};\n\nvar getMaxWindowScroll = () => {\n  const doc = getDocumentElement();\n  const maxScroll = getMaxScroll({\n    scrollHeight: doc.scrollHeight,\n    scrollWidth: doc.scrollWidth,\n    width: doc.clientWidth,\n    height: doc.clientHeight\n  });\n  return maxScroll;\n};\n\nvar getViewport = () => {\n  const scroll = getWindowScroll();\n  const maxScroll = getMaxWindowScroll();\n  const top = scroll.y;\n  const left = scroll.x;\n  const doc = getDocumentElement();\n  const width = doc.clientWidth;\n  const height = doc.clientHeight;\n  const right = left + width;\n  const bottom = top + height;\n  const frame = getRect({\n    top,\n    left,\n    right,\n    bottom\n  });\n  const viewport = {\n    frame,\n    scroll: {\n      initial: scroll,\n      current: scroll,\n      max: maxScroll,\n      diff: {\n        value: origin,\n        displacement: origin\n      }\n    }\n  };\n  return viewport;\n};\n\nvar getInitialPublish = ({\n  critical,\n  scrollOptions,\n  registry\n}) => {\n  start();\n  const viewport = getViewport();\n  const windowScroll = viewport.scroll.current;\n  const home = critical.droppable;\n  const droppables = registry.droppable.getAllByType(home.type).map(entry => entry.callbacks.getDimensionAndWatchScroll(windowScroll, scrollOptions));\n  const draggables = registry.draggable.getAllByType(critical.draggable.type).map(entry => entry.getDimension(windowScroll));\n  const dimensions = {\n    draggables: toDraggableMap(draggables),\n    droppables: toDroppableMap(droppables)\n  };\n  finish();\n  const result = {\n    dimensions,\n    critical,\n    viewport\n  };\n  return result;\n};\n\nfunction shouldPublishUpdate(registry, dragging, entry) {\n  if (entry.descriptor.id === dragging.id) {\n    return false;\n  }\n  if (entry.descriptor.type !== dragging.type) {\n    return false;\n  }\n  const home = registry.droppable.getById(entry.descriptor.droppableId);\n  if (home.descriptor.mode !== 'virtual') {\n    process.env.NODE_ENV !== \"production\" ? warning(`\n      You are attempting to add or remove a Draggable [id: ${entry.descriptor.id}]\n      while a drag is occurring. This is only supported for virtual lists.\n\n      See https://github.com/hello-pangea/dnd/blob/main/docs/patterns/virtual-lists.md\n    `) : void 0;\n    return false;\n  }\n  return true;\n}\nvar createDimensionMarshal = (registry, callbacks) => {\n  let collection = null;\n  const publisher = createPublisher({\n    callbacks: {\n      publish: callbacks.publishWhileDragging,\n      collectionStarting: callbacks.collectionStarting\n    },\n    registry\n  });\n  const updateDroppableIsEnabled = (id, isEnabled) => {\n    !registry.droppable.exists(id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot update is enabled flag of Droppable ${id} as it is not registered`) : invariant() : void 0;\n    if (!collection) {\n      return;\n    }\n    callbacks.updateDroppableIsEnabled({\n      id,\n      isEnabled\n    });\n  };\n  const updateDroppableIsCombineEnabled = (id, isCombineEnabled) => {\n    if (!collection) {\n      return;\n    }\n    !registry.droppable.exists(id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot update isCombineEnabled flag of Droppable ${id} as it is not registered`) : invariant() : void 0;\n    callbacks.updateDroppableIsCombineEnabled({\n      id,\n      isCombineEnabled\n    });\n  };\n  const updateDroppableScroll = (id, newScroll) => {\n    if (!collection) {\n      return;\n    }\n    !registry.droppable.exists(id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot update the scroll on Droppable ${id} as it is not registered`) : invariant() : void 0;\n    callbacks.updateDroppableScroll({\n      id,\n      newScroll\n    });\n  };\n  const scrollDroppable = (id, change) => {\n    if (!collection) {\n      return;\n    }\n    registry.droppable.getById(id).callbacks.scroll(change);\n  };\n  const stopPublishing = () => {\n    if (!collection) {\n      return;\n    }\n    publisher.stop();\n    const home = collection.critical.droppable;\n    registry.droppable.getAllByType(home.type).forEach(entry => entry.callbacks.dragStopped());\n    collection.unsubscribe();\n    collection = null;\n  };\n  const subscriber = event => {\n    !collection ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Should only be subscribed when a collection is occurring') : invariant() : void 0;\n    const dragging = collection.critical.draggable;\n    if (event.type === 'ADDITION') {\n      if (shouldPublishUpdate(registry, dragging, event.value)) {\n        publisher.add(event.value);\n      }\n    }\n    if (event.type === 'REMOVAL') {\n      if (shouldPublishUpdate(registry, dragging, event.value)) {\n        publisher.remove(event.value);\n      }\n    }\n  };\n  const startPublishing = request => {\n    !!collection ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot start capturing critical dimensions as there is already a collection') : invariant() : void 0;\n    const entry = registry.draggable.getById(request.draggableId);\n    const home = registry.droppable.getById(entry.descriptor.droppableId);\n    const critical = {\n      draggable: entry.descriptor,\n      droppable: home.descriptor\n    };\n    const unsubscribe = registry.subscribe(subscriber);\n    collection = {\n      critical,\n      unsubscribe\n    };\n    return getInitialPublish({\n      critical,\n      registry,\n      scrollOptions: request.scrollOptions\n    });\n  };\n  const marshal = {\n    updateDroppableIsEnabled,\n    updateDroppableIsCombineEnabled,\n    scrollDroppable,\n    updateDroppableScroll,\n    startPublishing,\n    stopPublishing\n  };\n  return marshal;\n};\n\nvar canStartDrag = (state, id) => {\n  if (state.phase === 'IDLE') {\n    return true;\n  }\n  if (state.phase !== 'DROP_ANIMATING') {\n    return false;\n  }\n  if (state.completed.result.draggableId === id) {\n    return false;\n  }\n  return state.completed.result.reason === 'DROP';\n};\n\nvar scrollWindow = change => {\n  window.scrollBy(change.x, change.y);\n};\n\nconst getScrollableDroppables = memoizeOne(droppables => toDroppableList(droppables).filter(droppable => {\n  if (!droppable.isEnabled) {\n    return false;\n  }\n  if (!droppable.frame) {\n    return false;\n  }\n  return true;\n}));\nconst getScrollableDroppableOver = (target, droppables) => {\n  const maybe = getScrollableDroppables(droppables).find(droppable => {\n    !droppable.frame ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Invalid result') : invariant() : void 0;\n    return isPositionInFrame(droppable.frame.pageMarginBox)(target);\n  }) || null;\n  return maybe;\n};\nvar getBestScrollableDroppable = ({\n  center,\n  destination,\n  droppables\n}) => {\n  if (destination) {\n    const dimension = droppables[destination];\n    if (!dimension.frame) {\n      return null;\n    }\n    return dimension;\n  }\n  const dimension = getScrollableDroppableOver(center, droppables);\n  return dimension;\n};\n\nconst defaultAutoScrollerOptions = {\n  startFromPercentage: 0.25,\n  maxScrollAtPercentage: 0.05,\n  maxPixelScroll: 28,\n  ease: percentage => percentage ** 2,\n  durationDampening: {\n    stopDampeningAt: 1200,\n    accelerateAt: 360\n  },\n  disabled: false\n};\n\nvar getDistanceThresholds = (container, axis, getAutoScrollerOptions = () => defaultAutoScrollerOptions) => {\n  const autoScrollerOptions = getAutoScrollerOptions();\n  const startScrollingFrom = container[axis.size] * autoScrollerOptions.startFromPercentage;\n  const maxScrollValueAt = container[axis.size] * autoScrollerOptions.maxScrollAtPercentage;\n  const thresholds = {\n    startScrollingFrom,\n    maxScrollValueAt\n  };\n  return thresholds;\n};\n\nvar getPercentage = ({\n  startOfRange,\n  endOfRange,\n  current\n}) => {\n  const range = endOfRange - startOfRange;\n  if (range === 0) {\n    process.env.NODE_ENV !== \"production\" ? warning(`\n      Detected distance range of 0 in the fluid auto scroller\n      This is unexpected and would cause a divide by 0 issue.\n      Not allowing an auto scroll\n    `) : void 0;\n    return 0;\n  }\n  const currentInRange = current - startOfRange;\n  const percentage = currentInRange / range;\n  return percentage;\n};\n\nvar minScroll = 1;\n\nvar getValueFromDistance = (distanceToEdge, thresholds, getAutoScrollerOptions = () => defaultAutoScrollerOptions) => {\n  const autoScrollerOptions = getAutoScrollerOptions();\n  if (distanceToEdge > thresholds.startScrollingFrom) {\n    return 0;\n  }\n  if (distanceToEdge <= thresholds.maxScrollValueAt) {\n    return autoScrollerOptions.maxPixelScroll;\n  }\n  if (distanceToEdge === thresholds.startScrollingFrom) {\n    return minScroll;\n  }\n  const percentageFromMaxScrollValueAt = getPercentage({\n    startOfRange: thresholds.maxScrollValueAt,\n    endOfRange: thresholds.startScrollingFrom,\n    current: distanceToEdge\n  });\n  const percentageFromStartScrollingFrom = 1 - percentageFromMaxScrollValueAt;\n  const scroll = autoScrollerOptions.maxPixelScroll * autoScrollerOptions.ease(percentageFromStartScrollingFrom);\n  return Math.ceil(scroll);\n};\n\nvar dampenValueByTime = (proposedScroll, dragStartTime, getAutoScrollerOptions) => {\n  const autoScrollerOptions = getAutoScrollerOptions();\n  const accelerateAt = autoScrollerOptions.durationDampening.accelerateAt;\n  const stopAt = autoScrollerOptions.durationDampening.stopDampeningAt;\n  const startOfRange = dragStartTime;\n  const endOfRange = stopAt;\n  const now = Date.now();\n  const runTime = now - startOfRange;\n  if (runTime >= stopAt) {\n    return proposedScroll;\n  }\n  if (runTime < accelerateAt) {\n    return minScroll;\n  }\n  const betweenAccelerateAtAndStopAtPercentage = getPercentage({\n    startOfRange: accelerateAt,\n    endOfRange,\n    current: runTime\n  });\n  const scroll = proposedScroll * autoScrollerOptions.ease(betweenAccelerateAtAndStopAtPercentage);\n  return Math.ceil(scroll);\n};\n\nvar getValue = ({\n  distanceToEdge,\n  thresholds,\n  dragStartTime,\n  shouldUseTimeDampening,\n  getAutoScrollerOptions\n}) => {\n  const scroll = getValueFromDistance(distanceToEdge, thresholds, getAutoScrollerOptions);\n  if (scroll === 0) {\n    return 0;\n  }\n  if (!shouldUseTimeDampening) {\n    return scroll;\n  }\n  return Math.max(dampenValueByTime(scroll, dragStartTime, getAutoScrollerOptions), minScroll);\n};\n\nvar getScrollOnAxis = ({\n  container,\n  distanceToEdges,\n  dragStartTime,\n  axis,\n  shouldUseTimeDampening,\n  getAutoScrollerOptions\n}) => {\n  const thresholds = getDistanceThresholds(container, axis, getAutoScrollerOptions);\n  const isCloserToEnd = distanceToEdges[axis.end] < distanceToEdges[axis.start];\n  if (isCloserToEnd) {\n    return getValue({\n      distanceToEdge: distanceToEdges[axis.end],\n      thresholds,\n      dragStartTime,\n      shouldUseTimeDampening,\n      getAutoScrollerOptions\n    });\n  }\n  return -1 * getValue({\n    distanceToEdge: distanceToEdges[axis.start],\n    thresholds,\n    dragStartTime,\n    shouldUseTimeDampening,\n    getAutoScrollerOptions\n  });\n};\n\nvar adjustForSizeLimits = ({\n  container,\n  subject,\n  proposedScroll\n}) => {\n  const isTooBigVertically = subject.height > container.height;\n  const isTooBigHorizontally = subject.width > container.width;\n  if (!isTooBigHorizontally && !isTooBigVertically) {\n    return proposedScroll;\n  }\n  if (isTooBigHorizontally && isTooBigVertically) {\n    return null;\n  }\n  return {\n    x: isTooBigHorizontally ? 0 : proposedScroll.x,\n    y: isTooBigVertically ? 0 : proposedScroll.y\n  };\n};\n\nconst clean = apply(value => value === 0 ? 0 : value);\nvar getScroll$1 = ({\n  dragStartTime,\n  container,\n  subject,\n  center,\n  shouldUseTimeDampening,\n  getAutoScrollerOptions\n}) => {\n  const distanceToEdges = {\n    top: center.y - container.top,\n    right: container.right - center.x,\n    bottom: container.bottom - center.y,\n    left: center.x - container.left\n  };\n  const y = getScrollOnAxis({\n    container,\n    distanceToEdges,\n    dragStartTime,\n    axis: vertical,\n    shouldUseTimeDampening,\n    getAutoScrollerOptions\n  });\n  const x = getScrollOnAxis({\n    container,\n    distanceToEdges,\n    dragStartTime,\n    axis: horizontal,\n    shouldUseTimeDampening,\n    getAutoScrollerOptions\n  });\n  const required = clean({\n    x,\n    y\n  });\n  if (isEqual$1(required, origin)) {\n    return null;\n  }\n  const limited = adjustForSizeLimits({\n    container,\n    subject,\n    proposedScroll: required\n  });\n  if (!limited) {\n    return null;\n  }\n  return isEqual$1(limited, origin) ? null : limited;\n};\n\nconst smallestSigned = apply(value => {\n  if (value === 0) {\n    return 0;\n  }\n  return value > 0 ? 1 : -1;\n});\nconst getOverlap = (() => {\n  const getRemainder = (target, max) => {\n    if (target < 0) {\n      return target;\n    }\n    if (target > max) {\n      return target - max;\n    }\n    return 0;\n  };\n  return ({\n    current,\n    max,\n    change\n  }) => {\n    const targetScroll = add(current, change);\n    const overlap = {\n      x: getRemainder(targetScroll.x, max.x),\n      y: getRemainder(targetScroll.y, max.y)\n    };\n    if (isEqual$1(overlap, origin)) {\n      return null;\n    }\n    return overlap;\n  };\n})();\nconst canPartiallyScroll = ({\n  max: rawMax,\n  current,\n  change\n}) => {\n  const max = {\n    x: Math.max(current.x, rawMax.x),\n    y: Math.max(current.y, rawMax.y)\n  };\n  const smallestChange = smallestSigned(change);\n  const overlap = getOverlap({\n    max,\n    current,\n    change: smallestChange\n  });\n  if (!overlap) {\n    return true;\n  }\n  if (smallestChange.x !== 0 && overlap.x === 0) {\n    return true;\n  }\n  if (smallestChange.y !== 0 && overlap.y === 0) {\n    return true;\n  }\n  return false;\n};\nconst canScrollWindow = (viewport, change) => canPartiallyScroll({\n  current: viewport.scroll.current,\n  max: viewport.scroll.max,\n  change\n});\nconst getWindowOverlap = (viewport, change) => {\n  if (!canScrollWindow(viewport, change)) {\n    return null;\n  }\n  const max = viewport.scroll.max;\n  const current = viewport.scroll.current;\n  return getOverlap({\n    current,\n    max,\n    change\n  });\n};\nconst canScrollDroppable = (droppable, change) => {\n  const frame = droppable.frame;\n  if (!frame) {\n    return false;\n  }\n  return canPartiallyScroll({\n    current: frame.scroll.current,\n    max: frame.scroll.max,\n    change\n  });\n};\nconst getDroppableOverlap = (droppable, change) => {\n  const frame = droppable.frame;\n  if (!frame) {\n    return null;\n  }\n  if (!canScrollDroppable(droppable, change)) {\n    return null;\n  }\n  return getOverlap({\n    current: frame.scroll.current,\n    max: frame.scroll.max,\n    change\n  });\n};\n\nvar getWindowScrollChange = ({\n  viewport,\n  subject,\n  center,\n  dragStartTime,\n  shouldUseTimeDampening,\n  getAutoScrollerOptions\n}) => {\n  const scroll = getScroll$1({\n    dragStartTime,\n    container: viewport.frame,\n    subject,\n    center,\n    shouldUseTimeDampening,\n    getAutoScrollerOptions\n  });\n  return scroll && canScrollWindow(viewport, scroll) ? scroll : null;\n};\n\nvar getDroppableScrollChange = ({\n  droppable,\n  subject,\n  center,\n  dragStartTime,\n  shouldUseTimeDampening,\n  getAutoScrollerOptions\n}) => {\n  const frame = droppable.frame;\n  if (!frame) {\n    return null;\n  }\n  const scroll = getScroll$1({\n    dragStartTime,\n    container: frame.pageMarginBox,\n    subject,\n    center,\n    shouldUseTimeDampening,\n    getAutoScrollerOptions\n  });\n  return scroll && canScrollDroppable(droppable, scroll) ? scroll : null;\n};\n\nvar scroll = ({\n  state,\n  dragStartTime,\n  shouldUseTimeDampening,\n  scrollWindow,\n  scrollDroppable,\n  getAutoScrollerOptions\n}) => {\n  const center = state.current.page.borderBoxCenter;\n  const draggable = state.dimensions.draggables[state.critical.draggable.id];\n  const subject = draggable.page.marginBox;\n  if (state.isWindowScrollAllowed) {\n    const viewport = state.viewport;\n    const change = getWindowScrollChange({\n      dragStartTime,\n      viewport,\n      subject,\n      center,\n      shouldUseTimeDampening,\n      getAutoScrollerOptions\n    });\n    if (change) {\n      scrollWindow(change);\n      return;\n    }\n  }\n  const droppable = getBestScrollableDroppable({\n    center,\n    destination: whatIsDraggedOver(state.impact),\n    droppables: state.dimensions.droppables\n  });\n  if (!droppable) {\n    return;\n  }\n  const change = getDroppableScrollChange({\n    dragStartTime,\n    droppable,\n    subject,\n    center,\n    shouldUseTimeDampening,\n    getAutoScrollerOptions\n  });\n  if (change) {\n    scrollDroppable(droppable.descriptor.id, change);\n  }\n};\n\nvar createFluidScroller = ({\n  scrollWindow,\n  scrollDroppable,\n  getAutoScrollerOptions = () => defaultAutoScrollerOptions\n}) => {\n  const scheduleWindowScroll = rafSchd(scrollWindow);\n  const scheduleDroppableScroll = rafSchd(scrollDroppable);\n  let dragging = null;\n  const tryScroll = state => {\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fluid scroll if not dragging') : invariant() : void 0;\n    const {\n      shouldUseTimeDampening,\n      dragStartTime\n    } = dragging;\n    scroll({\n      state,\n      scrollWindow: scheduleWindowScroll,\n      scrollDroppable: scheduleDroppableScroll,\n      dragStartTime,\n      shouldUseTimeDampening,\n      getAutoScrollerOptions\n    });\n  };\n  const start$1 = state => {\n    start();\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot start auto scrolling when already started') : invariant() : void 0;\n    const dragStartTime = Date.now();\n    let wasScrollNeeded = false;\n    const fakeScrollCallback = () => {\n      wasScrollNeeded = true;\n    };\n    scroll({\n      state,\n      dragStartTime: 0,\n      shouldUseTimeDampening: false,\n      scrollWindow: fakeScrollCallback,\n      scrollDroppable: fakeScrollCallback,\n      getAutoScrollerOptions\n    });\n    dragging = {\n      dragStartTime,\n      shouldUseTimeDampening: wasScrollNeeded\n    };\n    finish();\n    if (wasScrollNeeded) {\n      tryScroll(state);\n    }\n  };\n  const stop = () => {\n    if (!dragging) {\n      return;\n    }\n    scheduleWindowScroll.cancel();\n    scheduleDroppableScroll.cancel();\n    dragging = null;\n  };\n  return {\n    start: start$1,\n    stop,\n    scroll: tryScroll\n  };\n};\n\nvar createJumpScroller = ({\n  move,\n  scrollDroppable,\n  scrollWindow\n}) => {\n  const moveByOffset = (state, offset) => {\n    const client = add(state.current.client.selection, offset);\n    move({\n      client\n    });\n  };\n  const scrollDroppableAsMuchAsItCan = (droppable, change) => {\n    if (!canScrollDroppable(droppable, change)) {\n      return change;\n    }\n    const overlap = getDroppableOverlap(droppable, change);\n    if (!overlap) {\n      scrollDroppable(droppable.descriptor.id, change);\n      return null;\n    }\n    const whatTheDroppableCanScroll = subtract(change, overlap);\n    scrollDroppable(droppable.descriptor.id, whatTheDroppableCanScroll);\n    const remainder = subtract(change, whatTheDroppableCanScroll);\n    return remainder;\n  };\n  const scrollWindowAsMuchAsItCan = (isWindowScrollAllowed, viewport, change) => {\n    if (!isWindowScrollAllowed) {\n      return change;\n    }\n    if (!canScrollWindow(viewport, change)) {\n      return change;\n    }\n    const overlap = getWindowOverlap(viewport, change);\n    if (!overlap) {\n      scrollWindow(change);\n      return null;\n    }\n    const whatTheWindowCanScroll = subtract(change, overlap);\n    scrollWindow(whatTheWindowCanScroll);\n    const remainder = subtract(change, whatTheWindowCanScroll);\n    return remainder;\n  };\n  const jumpScroller = state => {\n    const request = state.scrollJumpRequest;\n    if (!request) {\n      return;\n    }\n    const destination = whatIsDraggedOver(state.impact);\n    !destination ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot perform a jump scroll when there is no destination') : invariant() : void 0;\n    const droppableRemainder = scrollDroppableAsMuchAsItCan(state.dimensions.droppables[destination], request);\n    if (!droppableRemainder) {\n      return;\n    }\n    const viewport = state.viewport;\n    const windowRemainder = scrollWindowAsMuchAsItCan(state.isWindowScrollAllowed, viewport, droppableRemainder);\n    if (!windowRemainder) {\n      return;\n    }\n    moveByOffset(state, windowRemainder);\n  };\n  return jumpScroller;\n};\n\nvar createAutoScroller = ({\n  scrollDroppable,\n  scrollWindow,\n  move,\n  getAutoScrollerOptions\n}) => {\n  const fluidScroller = createFluidScroller({\n    scrollWindow,\n    scrollDroppable,\n    getAutoScrollerOptions\n  });\n  const jumpScroll = createJumpScroller({\n    move,\n    scrollWindow,\n    scrollDroppable\n  });\n  const scroll = state => {\n    const autoScrollerOptions = getAutoScrollerOptions();\n    if (autoScrollerOptions.disabled || state.phase !== 'DRAGGING') {\n      return;\n    }\n    if (state.movementMode === 'FLUID') {\n      fluidScroller.scroll(state);\n      return;\n    }\n    if (!state.scrollJumpRequest) {\n      return;\n    }\n    jumpScroll(state);\n  };\n  const scroller = {\n    scroll,\n    start: fluidScroller.start,\n    stop: fluidScroller.stop\n  };\n  return scroller;\n};\n\nconst prefix = 'data-rfd';\nconst dragHandle = (() => {\n  const base = `${prefix}-drag-handle`;\n  return {\n    base,\n    draggableId: `${base}-draggable-id`,\n    contextId: `${base}-context-id`\n  };\n})();\nconst draggable = (() => {\n  const base = `${prefix}-draggable`;\n  return {\n    base,\n    contextId: `${base}-context-id`,\n    id: `${base}-id`\n  };\n})();\nconst droppable = (() => {\n  const base = `${prefix}-droppable`;\n  return {\n    base,\n    contextId: `${base}-context-id`,\n    id: `${base}-id`\n  };\n})();\nconst scrollContainer = {\n  contextId: `${prefix}-scroll-container-context-id`\n};\n\nconst makeGetSelector = context => attribute => `[${attribute}=\"${context}\"]`;\nconst getStyles = (rules, property) => rules.map(rule => {\n  const value = rule.styles[property];\n  if (!value) {\n    return '';\n  }\n  return `${rule.selector} { ${value} }`;\n}).join(' ');\nconst noPointerEvents = 'pointer-events: none;';\nvar getStyles$1 = contextId => {\n  const getSelector = makeGetSelector(contextId);\n  const dragHandle$1 = (() => {\n    const grabCursor = `\n      cursor: -webkit-grab;\n      cursor: grab;\n    `;\n    return {\n      selector: getSelector(dragHandle.contextId),\n      styles: {\n        always: `\n          -webkit-touch-callout: none;\n          -webkit-tap-highlight-color: rgba(0,0,0,0);\n          touch-action: manipulation;\n        `,\n        resting: grabCursor,\n        dragging: noPointerEvents,\n        dropAnimating: grabCursor\n      }\n    };\n  })();\n  const draggable$1 = (() => {\n    const transition = `\n      transition: ${transitions.outOfTheWay};\n    `;\n    return {\n      selector: getSelector(draggable.contextId),\n      styles: {\n        dragging: transition,\n        dropAnimating: transition,\n        userCancel: transition\n      }\n    };\n  })();\n  const droppable$1 = {\n    selector: getSelector(droppable.contextId),\n    styles: {\n      always: `overflow-anchor: none;`\n    }\n  };\n  const body = {\n    selector: 'body',\n    styles: {\n      dragging: `\n        cursor: grabbing;\n        cursor: -webkit-grabbing;\n        user-select: none;\n        -webkit-user-select: none;\n        -moz-user-select: none;\n        -ms-user-select: none;\n        overflow-anchor: none;\n      `\n    }\n  };\n  const rules = [draggable$1, dragHandle$1, droppable$1, body];\n  return {\n    always: getStyles(rules, 'always'),\n    resting: getStyles(rules, 'resting'),\n    dragging: getStyles(rules, 'dragging'),\n    dropAnimating: getStyles(rules, 'dropAnimating'),\n    userCancel: getStyles(rules, 'userCancel')\n  };\n};\n\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined' ? useLayoutEffect : useEffect;\n\nconst getHead = () => {\n  const head = document.querySelector('head');\n  !head ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot find the head to append a style to') : invariant() : void 0;\n  return head;\n};\nconst createStyleEl = nonce => {\n  const el = document.createElement('style');\n  if (nonce) {\n    el.setAttribute('nonce', nonce);\n  }\n  el.type = 'text/css';\n  return el;\n};\nfunction useStyleMarshal(contextId, nonce) {\n  const styles = useMemo(() => getStyles$1(contextId), [contextId]);\n  const alwaysRef = useRef(null);\n  const dynamicRef = useRef(null);\n  const setDynamicStyle = useCallback(memoizeOne(proposed => {\n    const el = dynamicRef.current;\n    !el ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot set dynamic style element if it is not set') : invariant() : void 0;\n    el.textContent = proposed;\n  }), []);\n  const setAlwaysStyle = useCallback(proposed => {\n    const el = alwaysRef.current;\n    !el ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot set dynamic style element if it is not set') : invariant() : void 0;\n    el.textContent = proposed;\n  }, []);\n  useIsomorphicLayoutEffect(() => {\n    !(!alwaysRef.current && !dynamicRef.current) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'style elements already mounted') : invariant() : void 0;\n    const always = createStyleEl(nonce);\n    const dynamic = createStyleEl(nonce);\n    alwaysRef.current = always;\n    dynamicRef.current = dynamic;\n    always.setAttribute(`${prefix}-always`, contextId);\n    dynamic.setAttribute(`${prefix}-dynamic`, contextId);\n    getHead().appendChild(always);\n    getHead().appendChild(dynamic);\n    setAlwaysStyle(styles.always);\n    setDynamicStyle(styles.resting);\n    return () => {\n      const remove = ref => {\n        const current = ref.current;\n        !current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot unmount ref as it is not set') : invariant() : void 0;\n        getHead().removeChild(current);\n        ref.current = null;\n      };\n      remove(alwaysRef);\n      remove(dynamicRef);\n    };\n  }, [nonce, setAlwaysStyle, setDynamicStyle, styles.always, styles.resting, contextId]);\n  const dragging = useCallback(() => setDynamicStyle(styles.dragging), [setDynamicStyle, styles.dragging]);\n  const dropping = useCallback(reason => {\n    if (reason === 'DROP') {\n      setDynamicStyle(styles.dropAnimating);\n      return;\n    }\n    setDynamicStyle(styles.userCancel);\n  }, [setDynamicStyle, styles.dropAnimating, styles.userCancel]);\n  const resting = useCallback(() => {\n    if (!dynamicRef.current) {\n      return;\n    }\n    setDynamicStyle(styles.resting);\n  }, [setDynamicStyle, styles.resting]);\n  const marshal = useMemo(() => ({\n    dragging,\n    dropping,\n    resting\n  }), [dragging, dropping, resting]);\n  return marshal;\n}\n\nfunction querySelectorAll(parentNode, selector) {\n  return Array.from(parentNode.querySelectorAll(selector));\n}\n\nvar getWindowFromEl = el => {\n  if (el && el.ownerDocument && el.ownerDocument.defaultView) {\n    return el.ownerDocument.defaultView;\n  }\n  return window;\n};\n\nfunction isHtmlElement(el) {\n  return el instanceof getWindowFromEl(el).HTMLElement;\n}\n\nfunction findDragHandle(contextId, draggableId) {\n  const selector = `[${dragHandle.contextId}=\"${contextId}\"]`;\n  const possible = querySelectorAll(document, selector);\n  if (!possible.length) {\n    process.env.NODE_ENV !== \"production\" ? warning(`Unable to find any drag handles in the context \"${contextId}\"`) : void 0;\n    return null;\n  }\n  const handle = possible.find(el => {\n    return el.getAttribute(dragHandle.draggableId) === draggableId;\n  });\n  if (!handle) {\n    process.env.NODE_ENV !== \"production\" ? warning(`Unable to find drag handle with id \"${draggableId}\" as no handle with a matching id was found`) : void 0;\n    return null;\n  }\n  if (!isHtmlElement(handle)) {\n    process.env.NODE_ENV !== \"production\" ? warning('drag handle needs to be a HTMLElement') : void 0;\n    return null;\n  }\n  return handle;\n}\n\nfunction useFocusMarshal(contextId) {\n  const entriesRef = useRef({});\n  const recordRef = useRef(null);\n  const restoreFocusFrameRef = useRef(null);\n  const isMountedRef = useRef(false);\n  const register = useCallback(function register(id, focus) {\n    const entry = {\n      id,\n      focus\n    };\n    entriesRef.current[id] = entry;\n    return function unregister() {\n      const entries = entriesRef.current;\n      const current = entries[id];\n      if (current !== entry) {\n        delete entries[id];\n      }\n    };\n  }, []);\n  const tryGiveFocus = useCallback(function tryGiveFocus(tryGiveFocusTo) {\n    const handle = findDragHandle(contextId, tryGiveFocusTo);\n    if (handle && handle !== document.activeElement) {\n      handle.focus();\n    }\n  }, [contextId]);\n  const tryShiftRecord = useCallback(function tryShiftRecord(previous, redirectTo) {\n    if (recordRef.current === previous) {\n      recordRef.current = redirectTo;\n    }\n  }, []);\n  const tryRestoreFocusRecorded = useCallback(function tryRestoreFocusRecorded() {\n    if (restoreFocusFrameRef.current) {\n      return;\n    }\n    if (!isMountedRef.current) {\n      return;\n    }\n    restoreFocusFrameRef.current = requestAnimationFrame(() => {\n      restoreFocusFrameRef.current = null;\n      const record = recordRef.current;\n      if (record) {\n        tryGiveFocus(record);\n      }\n    });\n  }, [tryGiveFocus]);\n  const tryRecordFocus = useCallback(function tryRecordFocus(id) {\n    recordRef.current = null;\n    const focused = document.activeElement;\n    if (!focused) {\n      return;\n    }\n    if (focused.getAttribute(dragHandle.draggableId) !== id) {\n      return;\n    }\n    recordRef.current = id;\n  }, []);\n  useIsomorphicLayoutEffect(() => {\n    isMountedRef.current = true;\n    return function clearFrameOnUnmount() {\n      isMountedRef.current = false;\n      const frameId = restoreFocusFrameRef.current;\n      if (frameId) {\n        cancelAnimationFrame(frameId);\n      }\n    };\n  }, []);\n  const marshal = useMemo(() => ({\n    register,\n    tryRecordFocus,\n    tryRestoreFocusRecorded,\n    tryShiftRecord\n  }), [register, tryRecordFocus, tryRestoreFocusRecorded, tryShiftRecord]);\n  return marshal;\n}\n\nfunction createRegistry() {\n  const entries = {\n    draggables: {},\n    droppables: {}\n  };\n  const subscribers = [];\n  function subscribe(cb) {\n    subscribers.push(cb);\n    return function unsubscribe() {\n      const index = subscribers.indexOf(cb);\n      if (index === -1) {\n        return;\n      }\n      subscribers.splice(index, 1);\n    };\n  }\n  function notify(event) {\n    if (subscribers.length) {\n      subscribers.forEach(cb => cb(event));\n    }\n  }\n  function findDraggableById(id) {\n    return entries.draggables[id] || null;\n  }\n  function getDraggableById(id) {\n    const entry = findDraggableById(id);\n    !entry ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot find draggable entry with id [${id}]`) : invariant() : void 0;\n    return entry;\n  }\n  const draggableAPI = {\n    register: entry => {\n      entries.draggables[entry.descriptor.id] = entry;\n      notify({\n        type: 'ADDITION',\n        value: entry\n      });\n    },\n    update: (entry, last) => {\n      const current = entries.draggables[last.descriptor.id];\n      if (!current) {\n        return;\n      }\n      if (current.uniqueId !== entry.uniqueId) {\n        return;\n      }\n      delete entries.draggables[last.descriptor.id];\n      entries.draggables[entry.descriptor.id] = entry;\n    },\n    unregister: entry => {\n      const draggableId = entry.descriptor.id;\n      const current = findDraggableById(draggableId);\n      if (!current) {\n        return;\n      }\n      if (entry.uniqueId !== current.uniqueId) {\n        return;\n      }\n      delete entries.draggables[draggableId];\n      if (entries.droppables[entry.descriptor.droppableId]) {\n        notify({\n          type: 'REMOVAL',\n          value: entry\n        });\n      }\n    },\n    getById: getDraggableById,\n    findById: findDraggableById,\n    exists: id => Boolean(findDraggableById(id)),\n    getAllByType: type => Object.values(entries.draggables).filter(entry => entry.descriptor.type === type)\n  };\n  function findDroppableById(id) {\n    return entries.droppables[id] || null;\n  }\n  function getDroppableById(id) {\n    const entry = findDroppableById(id);\n    !entry ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot find droppable entry with id [${id}]`) : invariant() : void 0;\n    return entry;\n  }\n  const droppableAPI = {\n    register: entry => {\n      entries.droppables[entry.descriptor.id] = entry;\n    },\n    unregister: entry => {\n      const current = findDroppableById(entry.descriptor.id);\n      if (!current) {\n        return;\n      }\n      if (entry.uniqueId !== current.uniqueId) {\n        return;\n      }\n      delete entries.droppables[entry.descriptor.id];\n    },\n    getById: getDroppableById,\n    findById: findDroppableById,\n    exists: id => Boolean(findDroppableById(id)),\n    getAllByType: type => Object.values(entries.droppables).filter(entry => entry.descriptor.type === type)\n  };\n  function clean() {\n    entries.draggables = {};\n    entries.droppables = {};\n    subscribers.length = 0;\n  }\n  return {\n    draggable: draggableAPI,\n    droppable: droppableAPI,\n    subscribe,\n    clean\n  };\n}\n\nfunction useRegistry() {\n  const registry = useMemo(createRegistry, []);\n  useEffect(() => {\n    return function unmount() {\n      registry.clean();\n    };\n  }, [registry]);\n  return registry;\n}\n\nvar StoreContext = React.createContext(null);\n\nvar getBodyElement = () => {\n  const body = document.body;\n  !body ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot find document.body') : invariant() : void 0;\n  return body;\n};\n\nconst visuallyHidden = {\n  position: 'absolute',\n  width: '1px',\n  height: '1px',\n  margin: '-1px',\n  border: '0',\n  padding: '0',\n  overflow: 'hidden',\n  clip: 'rect(0 0 0 0)',\n  'clip-path': 'inset(100%)'\n};\n\nconst getId = contextId => `rfd-announcement-${contextId}`;\nfunction useAnnouncer(contextId) {\n  const id = useMemo(() => getId(contextId), [contextId]);\n  const ref = useRef(null);\n  useEffect(function setup() {\n    const el = document.createElement('div');\n    ref.current = el;\n    el.id = id;\n    el.setAttribute('aria-live', 'assertive');\n    el.setAttribute('aria-atomic', 'true');\n    _extends(el.style, visuallyHidden);\n    getBodyElement().appendChild(el);\n    return function cleanup() {\n      setTimeout(function remove() {\n        const body = getBodyElement();\n        if (body.contains(el)) {\n          body.removeChild(el);\n        }\n        if (el === ref.current) {\n          ref.current = null;\n        }\n      });\n    };\n  }, [id]);\n  const announce = useCallback(message => {\n    const el = ref.current;\n    if (el) {\n      el.textContent = message;\n      return;\n    }\n    process.env.NODE_ENV !== \"production\" ? warning(`\n      A screen reader message was trying to be announced but it was unable to do so.\n      This can occur if you unmount your <DragDropContext /> in your onDragEnd.\n      Consider calling provided.announce() before the unmount so that the instruction will\n      not be lost for users relying on a screen reader.\n\n      Message not passed to screen reader:\n\n      \"${message}\"\n    `) : void 0;\n  }, []);\n  return announce;\n}\n\nconst defaults = {\n  separator: '::'\n};\nfunction useUniqueId(prefix, options = defaults) {\n  const id = React.useId();\n  return useMemo(() => `${prefix}${options.separator}${id}`, [options.separator, prefix, id]);\n}\n\nfunction getElementId({\n  contextId,\n  uniqueId\n}) {\n  return `rfd-hidden-text-${contextId}-${uniqueId}`;\n}\nfunction useHiddenTextElement({\n  contextId,\n  text\n}) {\n  const uniqueId = useUniqueId('hidden-text', {\n    separator: '-'\n  });\n  const id = useMemo(() => getElementId({\n    contextId,\n    uniqueId\n  }), [uniqueId, contextId]);\n  useEffect(function mount() {\n    const el = document.createElement('div');\n    el.id = id;\n    el.textContent = text;\n    el.style.display = 'none';\n    getBodyElement().appendChild(el);\n    return function unmount() {\n      const body = getBodyElement();\n      if (body.contains(el)) {\n        body.removeChild(el);\n      }\n    };\n  }, [id, text]);\n  return id;\n}\n\nvar AppContext = React.createContext(null);\n\nvar peerDependencies = {\n\treact: \"^18.0.0\",\n\t\"react-dom\": \"^18.0.0\"\n};\n\nconst semver = /(\\d+)\\.(\\d+)\\.(\\d+)/;\nconst getVersion = value => {\n  const result = semver.exec(value);\n  !(result != null) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Unable to parse React version ${value}`) : invariant() : void 0;\n  const major = Number(result[1]);\n  const minor = Number(result[2]);\n  const patch = Number(result[3]);\n  return {\n    major,\n    minor,\n    patch,\n    raw: value\n  };\n};\nconst isSatisfied = (expected, actual) => {\n  if (actual.major > expected.major) {\n    return true;\n  }\n  if (actual.major < expected.major) {\n    return false;\n  }\n  if (actual.minor > expected.minor) {\n    return true;\n  }\n  if (actual.minor < expected.minor) {\n    return false;\n  }\n  return actual.patch >= expected.patch;\n};\nvar checkReactVersion = (peerDepValue, actualValue) => {\n  const peerDep = getVersion(peerDepValue);\n  const actual = getVersion(actualValue);\n  if (isSatisfied(peerDep, actual)) {\n    return;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(`\n    React version: [${actual.raw}]\n    does not satisfy expected peer dependency version: [${peerDep.raw}]\n\n    This can result in run time bugs, and even fatal crashes\n  `) : void 0;\n};\n\nconst suffix = `\n  We expect a html5 doctype: <!doctype html>\n  This is to ensure consistent browser layout and measurement\n\n  More information: https://github.com/hello-pangea/dnd/blob/main/docs/guides/doctype.md\n`;\nvar checkDoctype = doc => {\n  const doctype = doc.doctype;\n  if (!doctype) {\n    process.env.NODE_ENV !== \"production\" ? warning(`\n      No <!doctype html> found.\n\n      ${suffix}\n    `) : void 0;\n    return;\n  }\n  if (doctype.name.toLowerCase() !== 'html') {\n    process.env.NODE_ENV !== \"production\" ? warning(`\n      Unexpected <!doctype> found: (${doctype.name})\n\n      ${suffix}\n    `) : void 0;\n  }\n  if (doctype.publicId !== '') {\n    process.env.NODE_ENV !== \"production\" ? warning(`\n      Unexpected <!doctype> publicId found: (${doctype.publicId})\n      A html5 doctype does not have a publicId\n\n      ${suffix}\n    `) : void 0;\n  }\n};\n\nfunction useDev(useHook) {\n  if (process.env.NODE_ENV !== 'production') {\n    useHook();\n  }\n}\n\nfunction useDevSetupWarning(fn, inputs) {\n  useDev(() => {\n    useEffect(() => {\n      try {\n        fn();\n      } catch (e) {\n        error(`\n          A setup problem was encountered.\n\n          > ${e.message}\n        `);\n      }\n    }, inputs);\n  });\n}\n\nfunction useStartupValidation() {\n  useDevSetupWarning(() => {\n    checkReactVersion(peerDependencies.react, React.version);\n    checkDoctype(document);\n  }, []);\n}\n\nfunction usePrevious(current) {\n  const ref = useRef(current);\n  useEffect(() => {\n    ref.current = current;\n  });\n  return ref;\n}\n\nfunction create() {\n  let lock = null;\n  function isClaimed() {\n    return Boolean(lock);\n  }\n  function isActive(value) {\n    return value === lock;\n  }\n  function claim(abandon) {\n    !!lock ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot claim lock as it is already claimed') : invariant() : void 0;\n    const newLock = {\n      abandon\n    };\n    lock = newLock;\n    return newLock;\n  }\n  function release() {\n    !lock ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot release lock when there is no lock') : invariant() : void 0;\n    lock = null;\n  }\n  function tryAbandon() {\n    if (lock) {\n      lock.abandon();\n      release();\n    }\n  }\n  return {\n    isClaimed,\n    isActive,\n    claim,\n    release,\n    tryAbandon\n  };\n}\n\nfunction isDragging(state) {\n  if (state.phase === 'IDLE' || state.phase === 'DROP_ANIMATING') {\n    return false;\n  }\n  return state.isDragging;\n}\n\nconst tab = 9;\nconst enter = 13;\nconst escape = 27;\nconst space = 32;\nconst pageUp = 33;\nconst pageDown = 34;\nconst end = 35;\nconst home = 36;\nconst arrowLeft = 37;\nconst arrowUp = 38;\nconst arrowRight = 39;\nconst arrowDown = 40;\n\nconst preventedKeys = {\n  [enter]: true,\n  [tab]: true\n};\nvar preventStandardKeyEvents = event => {\n  if (preventedKeys[event.keyCode]) {\n    event.preventDefault();\n  }\n};\n\nconst supportedEventName = (() => {\n  const base = 'visibilitychange';\n  if (typeof document === 'undefined') {\n    return base;\n  }\n  const candidates = [base, `ms${base}`, `webkit${base}`, `moz${base}`, `o${base}`];\n  const supported = candidates.find(eventName => `on${eventName}` in document);\n  return supported || base;\n})();\n\nconst primaryButton = 0;\nconst sloppyClickThreshold = 5;\nfunction isSloppyClickThresholdExceeded(original, current) {\n  return Math.abs(current.x - original.x) >= sloppyClickThreshold || Math.abs(current.y - original.y) >= sloppyClickThreshold;\n}\nconst idle$1 = {\n  type: 'IDLE'\n};\nfunction getCaptureBindings({\n  cancel,\n  completed,\n  getPhase,\n  setPhase\n}) {\n  return [{\n    eventName: 'mousemove',\n    fn: event => {\n      const {\n        button,\n        clientX,\n        clientY\n      } = event;\n      if (button !== primaryButton) {\n        return;\n      }\n      const point = {\n        x: clientX,\n        y: clientY\n      };\n      const phase = getPhase();\n      if (phase.type === 'DRAGGING') {\n        event.preventDefault();\n        phase.actions.move(point);\n        return;\n      }\n      !(phase.type === 'PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot be IDLE') : invariant() : void 0;\n      const pending = phase.point;\n      if (!isSloppyClickThresholdExceeded(pending, point)) {\n        return;\n      }\n      event.preventDefault();\n      const actions = phase.actions.fluidLift(point);\n      setPhase({\n        type: 'DRAGGING',\n        actions\n      });\n    }\n  }, {\n    eventName: 'mouseup',\n    fn: event => {\n      const phase = getPhase();\n      if (phase.type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      event.preventDefault();\n      phase.actions.drop({\n        shouldBlockNextClick: true\n      });\n      completed();\n    }\n  }, {\n    eventName: 'mousedown',\n    fn: event => {\n      if (getPhase().type === 'DRAGGING') {\n        event.preventDefault();\n      }\n      cancel();\n    }\n  }, {\n    eventName: 'keydown',\n    fn: event => {\n      const phase = getPhase();\n      if (phase.type === 'PENDING') {\n        cancel();\n        return;\n      }\n      if (event.keyCode === escape) {\n        event.preventDefault();\n        cancel();\n        return;\n      }\n      preventStandardKeyEvents(event);\n    }\n  }, {\n    eventName: 'resize',\n    fn: cancel\n  }, {\n    eventName: 'scroll',\n    options: {\n      passive: true,\n      capture: false\n    },\n    fn: () => {\n      if (getPhase().type === 'PENDING') {\n        cancel();\n      }\n    }\n  }, {\n    eventName: 'webkitmouseforcedown',\n    fn: event => {\n      const phase = getPhase();\n      !(phase.type !== 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Unexpected phase') : invariant() : void 0;\n      if (phase.actions.shouldRespectForcePress()) {\n        cancel();\n        return;\n      }\n      event.preventDefault();\n    }\n  }, {\n    eventName: supportedEventName,\n    fn: cancel\n  }];\n}\nfunction useMouseSensor(api) {\n  const phaseRef = useRef(idle$1);\n  const unbindEventsRef = useRef(noop$2);\n  const startCaptureBinding = useMemo(() => ({\n    eventName: 'mousedown',\n    fn: function onMouseDown(event) {\n      if (event.defaultPrevented) {\n        return;\n      }\n      if (event.button !== primaryButton) {\n        return;\n      }\n      if (event.ctrlKey || event.metaKey || event.shiftKey || event.altKey) {\n        return;\n      }\n      const draggableId = api.findClosestDraggableId(event);\n      if (!draggableId) {\n        return;\n      }\n      const actions = api.tryGetLock(draggableId, stop, {\n        sourceEvent: event\n      });\n      if (!actions) {\n        return;\n      }\n      event.preventDefault();\n      const point = {\n        x: event.clientX,\n        y: event.clientY\n      };\n      unbindEventsRef.current();\n      startPendingDrag(actions, point);\n    }\n  }), [api]);\n  const preventForcePressBinding = useMemo(() => ({\n    eventName: 'webkitmouseforcewillbegin',\n    fn: event => {\n      if (event.defaultPrevented) {\n        return;\n      }\n      const id = api.findClosestDraggableId(event);\n      if (!id) {\n        return;\n      }\n      const options = api.findOptionsForDraggable(id);\n      if (!options) {\n        return;\n      }\n      if (options.shouldRespectForcePress) {\n        return;\n      }\n      if (!api.canGetLock(id)) {\n        return;\n      }\n      event.preventDefault();\n    }\n  }), [api]);\n  const listenForCapture = useCallback(function listenForCapture() {\n    const options = {\n      passive: false,\n      capture: true\n    };\n    unbindEventsRef.current = bindEvents(window, [preventForcePressBinding, startCaptureBinding], options);\n  }, [preventForcePressBinding, startCaptureBinding]);\n  const stop = useCallback(() => {\n    const current = phaseRef.current;\n    if (current.type === 'IDLE') {\n      return;\n    }\n    phaseRef.current = idle$1;\n    unbindEventsRef.current();\n    listenForCapture();\n  }, [listenForCapture]);\n  const cancel = useCallback(() => {\n    const phase = phaseRef.current;\n    stop();\n    if (phase.type === 'DRAGGING') {\n      phase.actions.cancel({\n        shouldBlockNextClick: true\n      });\n    }\n    if (phase.type === 'PENDING') {\n      phase.actions.abort();\n    }\n  }, [stop]);\n  const bindCapturingEvents = useCallback(function bindCapturingEvents() {\n    const options = {\n      capture: true,\n      passive: false\n    };\n    const bindings = getCaptureBindings({\n      cancel,\n      completed: stop,\n      getPhase: () => phaseRef.current,\n      setPhase: phase => {\n        phaseRef.current = phase;\n      }\n    });\n    unbindEventsRef.current = bindEvents(window, bindings, options);\n  }, [cancel, stop]);\n  const startPendingDrag = useCallback(function startPendingDrag(actions, point) {\n    !(phaseRef.current.type === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected to move from IDLE to PENDING drag') : invariant() : void 0;\n    phaseRef.current = {\n      type: 'PENDING',\n      point,\n      actions\n    };\n    bindCapturingEvents();\n  }, [bindCapturingEvents]);\n  useIsomorphicLayoutEffect(function mount() {\n    listenForCapture();\n    return function unmount() {\n      unbindEventsRef.current();\n    };\n  }, [listenForCapture]);\n}\n\nfunction noop$1() {}\nconst scrollJumpKeys = {\n  [pageDown]: true,\n  [pageUp]: true,\n  [home]: true,\n  [end]: true\n};\nfunction getDraggingBindings(actions, stop) {\n  function cancel() {\n    stop();\n    actions.cancel();\n  }\n  function drop() {\n    stop();\n    actions.drop();\n  }\n  return [{\n    eventName: 'keydown',\n    fn: event => {\n      if (event.keyCode === escape) {\n        event.preventDefault();\n        cancel();\n        return;\n      }\n      if (event.keyCode === space) {\n        event.preventDefault();\n        drop();\n        return;\n      }\n      if (event.keyCode === arrowDown) {\n        event.preventDefault();\n        actions.moveDown();\n        return;\n      }\n      if (event.keyCode === arrowUp) {\n        event.preventDefault();\n        actions.moveUp();\n        return;\n      }\n      if (event.keyCode === arrowRight) {\n        event.preventDefault();\n        actions.moveRight();\n        return;\n      }\n      if (event.keyCode === arrowLeft) {\n        event.preventDefault();\n        actions.moveLeft();\n        return;\n      }\n      if (scrollJumpKeys[event.keyCode]) {\n        event.preventDefault();\n        return;\n      }\n      preventStandardKeyEvents(event);\n    }\n  }, {\n    eventName: 'mousedown',\n    fn: cancel\n  }, {\n    eventName: 'mouseup',\n    fn: cancel\n  }, {\n    eventName: 'click',\n    fn: cancel\n  }, {\n    eventName: 'touchstart',\n    fn: cancel\n  }, {\n    eventName: 'resize',\n    fn: cancel\n  }, {\n    eventName: 'wheel',\n    fn: cancel,\n    options: {\n      passive: true\n    }\n  }, {\n    eventName: supportedEventName,\n    fn: cancel\n  }];\n}\nfunction useKeyboardSensor(api) {\n  const unbindEventsRef = useRef(noop$1);\n  const startCaptureBinding = useMemo(() => ({\n    eventName: 'keydown',\n    fn: function onKeyDown(event) {\n      if (event.defaultPrevented) {\n        return;\n      }\n      if (event.keyCode !== space) {\n        return;\n      }\n      const draggableId = api.findClosestDraggableId(event);\n      if (!draggableId) {\n        return;\n      }\n      const preDrag = api.tryGetLock(draggableId, stop, {\n        sourceEvent: event\n      });\n      if (!preDrag) {\n        return;\n      }\n      event.preventDefault();\n      let isCapturing = true;\n      const actions = preDrag.snapLift();\n      unbindEventsRef.current();\n      function stop() {\n        !isCapturing ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot stop capturing a keyboard drag when not capturing') : invariant() : void 0;\n        isCapturing = false;\n        unbindEventsRef.current();\n        listenForCapture();\n      }\n      unbindEventsRef.current = bindEvents(window, getDraggingBindings(actions, stop), {\n        capture: true,\n        passive: false\n      });\n    }\n  }), [api]);\n  const listenForCapture = useCallback(function tryStartCapture() {\n    const options = {\n      passive: false,\n      capture: true\n    };\n    unbindEventsRef.current = bindEvents(window, [startCaptureBinding], options);\n  }, [startCaptureBinding]);\n  useIsomorphicLayoutEffect(function mount() {\n    listenForCapture();\n    return function unmount() {\n      unbindEventsRef.current();\n    };\n  }, [listenForCapture]);\n}\n\nconst idle = {\n  type: 'IDLE'\n};\nconst timeForLongPress = 120;\nconst forcePressThreshold = 0.15;\nfunction getWindowBindings({\n  cancel,\n  getPhase\n}) {\n  return [{\n    eventName: 'orientationchange',\n    fn: cancel\n  }, {\n    eventName: 'resize',\n    fn: cancel\n  }, {\n    eventName: 'contextmenu',\n    fn: event => {\n      event.preventDefault();\n    }\n  }, {\n    eventName: 'keydown',\n    fn: event => {\n      if (getPhase().type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      if (event.keyCode === escape) {\n        event.preventDefault();\n      }\n      cancel();\n    }\n  }, {\n    eventName: supportedEventName,\n    fn: cancel\n  }];\n}\nfunction getHandleBindings({\n  cancel,\n  completed,\n  getPhase\n}) {\n  return [{\n    eventName: 'touchmove',\n    options: {\n      capture: false\n    },\n    fn: event => {\n      const phase = getPhase();\n      if (phase.type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      phase.hasMoved = true;\n      const {\n        clientX,\n        clientY\n      } = event.touches[0];\n      const point = {\n        x: clientX,\n        y: clientY\n      };\n      event.preventDefault();\n      phase.actions.move(point);\n    }\n  }, {\n    eventName: 'touchend',\n    fn: event => {\n      const phase = getPhase();\n      if (phase.type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      event.preventDefault();\n      phase.actions.drop({\n        shouldBlockNextClick: true\n      });\n      completed();\n    }\n  }, {\n    eventName: 'touchcancel',\n    fn: event => {\n      if (getPhase().type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      event.preventDefault();\n      cancel();\n    }\n  }, {\n    eventName: 'touchforcechange',\n    fn: event => {\n      const phase = getPhase();\n      !(phase.type !== 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant() : invariant() : void 0;\n      const touch = event.touches[0];\n      if (!touch) {\n        return;\n      }\n      const isForcePress = touch.force >= forcePressThreshold;\n      if (!isForcePress) {\n        return;\n      }\n      const shouldRespect = phase.actions.shouldRespectForcePress();\n      if (phase.type === 'PENDING') {\n        if (shouldRespect) {\n          cancel();\n        }\n        return;\n      }\n      if (shouldRespect) {\n        if (phase.hasMoved) {\n          event.preventDefault();\n          return;\n        }\n        cancel();\n        return;\n      }\n      event.preventDefault();\n    }\n  }, {\n    eventName: supportedEventName,\n    fn: cancel\n  }];\n}\nfunction useTouchSensor(api) {\n  const phaseRef = useRef(idle);\n  const unbindEventsRef = useRef(noop$2);\n  const getPhase = useCallback(function getPhase() {\n    return phaseRef.current;\n  }, []);\n  const setPhase = useCallback(function setPhase(phase) {\n    phaseRef.current = phase;\n  }, []);\n  const startCaptureBinding = useMemo(() => ({\n    eventName: 'touchstart',\n    fn: function onTouchStart(event) {\n      if (event.defaultPrevented) {\n        return;\n      }\n      const draggableId = api.findClosestDraggableId(event);\n      if (!draggableId) {\n        return;\n      }\n      const actions = api.tryGetLock(draggableId, stop, {\n        sourceEvent: event\n      });\n      if (!actions) {\n        return;\n      }\n      const touch = event.touches[0];\n      const {\n        clientX,\n        clientY\n      } = touch;\n      const point = {\n        x: clientX,\n        y: clientY\n      };\n      unbindEventsRef.current();\n      startPendingDrag(actions, point);\n    }\n  }), [api]);\n  const listenForCapture = useCallback(function listenForCapture() {\n    const options = {\n      capture: true,\n      passive: false\n    };\n    unbindEventsRef.current = bindEvents(window, [startCaptureBinding], options);\n  }, [startCaptureBinding]);\n  const stop = useCallback(() => {\n    const current = phaseRef.current;\n    if (current.type === 'IDLE') {\n      return;\n    }\n    if (current.type === 'PENDING') {\n      clearTimeout(current.longPressTimerId);\n    }\n    setPhase(idle);\n    unbindEventsRef.current();\n    listenForCapture();\n  }, [listenForCapture, setPhase]);\n  const cancel = useCallback(() => {\n    const phase = phaseRef.current;\n    stop();\n    if (phase.type === 'DRAGGING') {\n      phase.actions.cancel({\n        shouldBlockNextClick: true\n      });\n    }\n    if (phase.type === 'PENDING') {\n      phase.actions.abort();\n    }\n  }, [stop]);\n  const bindCapturingEvents = useCallback(function bindCapturingEvents() {\n    const options = {\n      capture: true,\n      passive: false\n    };\n    const args = {\n      cancel,\n      completed: stop,\n      getPhase\n    };\n    const unbindTarget = bindEvents(window, getHandleBindings(args), options);\n    const unbindWindow = bindEvents(window, getWindowBindings(args), options);\n    unbindEventsRef.current = function unbindAll() {\n      unbindTarget();\n      unbindWindow();\n    };\n  }, [cancel, getPhase, stop]);\n  const startDragging = useCallback(function startDragging() {\n    const phase = getPhase();\n    !(phase.type === 'PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot start dragging from phase ${phase.type}`) : invariant() : void 0;\n    const actions = phase.actions.fluidLift(phase.point);\n    setPhase({\n      type: 'DRAGGING',\n      actions,\n      hasMoved: false\n    });\n  }, [getPhase, setPhase]);\n  const startPendingDrag = useCallback(function startPendingDrag(actions, point) {\n    !(getPhase().type === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected to move from IDLE to PENDING drag') : invariant() : void 0;\n    const longPressTimerId = setTimeout(startDragging, timeForLongPress);\n    setPhase({\n      type: 'PENDING',\n      point,\n      actions,\n      longPressTimerId\n    });\n    bindCapturingEvents();\n  }, [bindCapturingEvents, getPhase, setPhase, startDragging]);\n  useIsomorphicLayoutEffect(function mount() {\n    listenForCapture();\n    return function unmount() {\n      unbindEventsRef.current();\n      const phase = getPhase();\n      if (phase.type === 'PENDING') {\n        clearTimeout(phase.longPressTimerId);\n        setPhase(idle);\n      }\n    };\n  }, [getPhase, listenForCapture, setPhase]);\n  useIsomorphicLayoutEffect(function webkitHack() {\n    const unbind = bindEvents(window, [{\n      eventName: 'touchmove',\n      fn: () => {},\n      options: {\n        capture: false,\n        passive: false\n      }\n    }]);\n    return unbind;\n  }, []);\n}\n\nfunction useValidateSensorHooks(sensorHooks) {\n  useDev(() => {\n    const previousRef = usePrevious(sensorHooks);\n    useDevSetupWarning(() => {\n      !(previousRef.current.length === sensorHooks.length) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot change the amount of sensor hooks after mounting') : invariant(false) : void 0;\n    });\n  });\n}\n\nconst interactiveTagNames = ['input', 'button', 'textarea', 'select', 'option', 'optgroup', 'video', 'audio'];\nfunction isAnInteractiveElement(parent, current) {\n  if (current == null) {\n    return false;\n  }\n  const hasAnInteractiveTag = interactiveTagNames.includes(current.tagName.toLowerCase());\n  if (hasAnInteractiveTag) {\n    return true;\n  }\n  const attribute = current.getAttribute('contenteditable');\n  if (attribute === 'true' || attribute === '') {\n    return true;\n  }\n  if (current === parent) {\n    return false;\n  }\n  return isAnInteractiveElement(parent, current.parentElement);\n}\nfunction isEventInInteractiveElement(draggable, event) {\n  const target = event.target;\n  if (!isHtmlElement(target)) {\n    return false;\n  }\n  return isAnInteractiveElement(draggable, target);\n}\n\nvar getBorderBoxCenterPosition = el => getRect(el.getBoundingClientRect()).center;\n\nfunction isElement(el) {\n  return el instanceof getWindowFromEl(el).Element;\n}\n\nconst supportedMatchesName = (() => {\n  const base = 'matches';\n  if (typeof document === 'undefined') {\n    return base;\n  }\n  const candidates = [base, 'msMatchesSelector', 'webkitMatchesSelector'];\n  const value = candidates.find(name => name in Element.prototype);\n  return value || base;\n})();\nfunction closestPonyfill(el, selector) {\n  if (el == null) {\n    return null;\n  }\n  if (el[supportedMatchesName](selector)) {\n    return el;\n  }\n  return closestPonyfill(el.parentElement, selector);\n}\nfunction closest(el, selector) {\n  if (el.closest) {\n    return el.closest(selector);\n  }\n  return closestPonyfill(el, selector);\n}\n\nfunction getSelector(contextId) {\n  return `[${dragHandle.contextId}=\"${contextId}\"]`;\n}\nfunction findClosestDragHandleFromEvent(contextId, event) {\n  const target = event.target;\n  if (!isElement(target)) {\n    process.env.NODE_ENV !== \"production\" ? warning('event.target must be a Element') : void 0;\n    return null;\n  }\n  const selector = getSelector(contextId);\n  const handle = closest(target, selector);\n  if (!handle) {\n    return null;\n  }\n  if (!isHtmlElement(handle)) {\n    process.env.NODE_ENV !== \"production\" ? warning('drag handle must be a HTMLElement') : void 0;\n    return null;\n  }\n  return handle;\n}\nfunction tryGetClosestDraggableIdFromEvent(contextId, event) {\n  const handle = findClosestDragHandleFromEvent(contextId, event);\n  if (!handle) {\n    return null;\n  }\n  return handle.getAttribute(dragHandle.draggableId);\n}\n\nfunction findDraggable(contextId, draggableId) {\n  const selector = `[${draggable.contextId}=\"${contextId}\"]`;\n  const possible = querySelectorAll(document, selector);\n  const draggable$1 = possible.find(el => {\n    return el.getAttribute(draggable.id) === draggableId;\n  });\n  if (!draggable$1) {\n    return null;\n  }\n  if (!isHtmlElement(draggable$1)) {\n    process.env.NODE_ENV !== \"production\" ? warning('Draggable element is not a HTMLElement') : void 0;\n    return null;\n  }\n  return draggable$1;\n}\n\nfunction preventDefault(event) {\n  event.preventDefault();\n}\nfunction isActive({\n  expected,\n  phase,\n  isLockActive,\n  shouldWarn\n}) {\n  if (!isLockActive()) {\n    if (shouldWarn) {\n      process.env.NODE_ENV !== \"production\" ? warning(`\n        Cannot perform action.\n        The sensor no longer has an action lock.\n\n        Tips:\n\n        - Throw away your action handlers when forceStop() is called\n        - Check actions.isActive() if you really need to\n      `) : void 0;\n    }\n    return false;\n  }\n  if (expected !== phase) {\n    if (shouldWarn) {\n      process.env.NODE_ENV !== \"production\" ? warning(`\n        Cannot perform action.\n        The actions you used belong to an outdated phase\n\n        Current phase: ${expected}\n        You called an action from outdated phase: ${phase}\n\n        Tips:\n\n        - Do not use preDragActions actions after calling preDragActions.lift()\n      `) : void 0;\n    }\n    return false;\n  }\n  return true;\n}\nfunction canStart({\n  lockAPI,\n  store,\n  registry,\n  draggableId\n}) {\n  if (lockAPI.isClaimed()) {\n    return false;\n  }\n  const entry = registry.draggable.findById(draggableId);\n  if (!entry) {\n    process.env.NODE_ENV !== \"production\" ? warning(`Unable to find draggable with id: ${draggableId}`) : void 0;\n    return false;\n  }\n  if (!entry.options.isEnabled) {\n    return false;\n  }\n  if (!canStartDrag(store.getState(), draggableId)) {\n    return false;\n  }\n  return true;\n}\nfunction tryStart({\n  lockAPI,\n  contextId,\n  store,\n  registry,\n  draggableId,\n  forceSensorStop,\n  sourceEvent\n}) {\n  const shouldStart = canStart({\n    lockAPI,\n    store,\n    registry,\n    draggableId\n  });\n  if (!shouldStart) {\n    return null;\n  }\n  const entry = registry.draggable.getById(draggableId);\n  const el = findDraggable(contextId, entry.descriptor.id);\n  if (!el) {\n    process.env.NODE_ENV !== \"production\" ? warning(`Unable to find draggable element with id: ${draggableId}`) : void 0;\n    return null;\n  }\n  if (sourceEvent && !entry.options.canDragInteractiveElements && isEventInInteractiveElement(el, sourceEvent)) {\n    return null;\n  }\n  const lock = lockAPI.claim(forceSensorStop || noop$2);\n  let phase = 'PRE_DRAG';\n  function getShouldRespectForcePress() {\n    return entry.options.shouldRespectForcePress;\n  }\n  function isLockActive() {\n    return lockAPI.isActive(lock);\n  }\n  function tryDispatch(expected, getAction) {\n    if (isActive({\n      expected,\n      phase,\n      isLockActive,\n      shouldWarn: true\n    })) {\n      store.dispatch(getAction());\n    }\n  }\n  const tryDispatchWhenDragging = tryDispatch.bind(null, 'DRAGGING');\n  function lift(args) {\n    function completed() {\n      lockAPI.release();\n      phase = 'COMPLETED';\n    }\n    if (phase !== 'PRE_DRAG') {\n      completed();\n      process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot lift in phase ${phase}`) : invariant() ;\n    }\n    store.dispatch(lift$1(args.liftActionArgs));\n    phase = 'DRAGGING';\n    function finish(reason, options = {\n      shouldBlockNextClick: false\n    }) {\n      args.cleanup();\n      if (options.shouldBlockNextClick) {\n        const unbind = bindEvents(window, [{\n          eventName: 'click',\n          fn: preventDefault,\n          options: {\n            once: true,\n            passive: false,\n            capture: true\n          }\n        }]);\n        setTimeout(unbind);\n      }\n      completed();\n      store.dispatch(drop({\n        reason\n      }));\n    }\n    return {\n      isActive: () => isActive({\n        expected: 'DRAGGING',\n        phase,\n        isLockActive,\n        shouldWarn: false\n      }),\n      shouldRespectForcePress: getShouldRespectForcePress,\n      drop: options => finish('DROP', options),\n      cancel: options => finish('CANCEL', options),\n      ...args.actions\n    };\n  }\n  function fluidLift(clientSelection) {\n    const move$1 = rafSchd(client => {\n      tryDispatchWhenDragging(() => move({\n        client\n      }));\n    });\n    const api = lift({\n      liftActionArgs: {\n        id: draggableId,\n        clientSelection,\n        movementMode: 'FLUID'\n      },\n      cleanup: () => move$1.cancel(),\n      actions: {\n        move: move$1\n      }\n    });\n    return {\n      ...api,\n      move: move$1\n    };\n  }\n  function snapLift() {\n    const actions = {\n      moveUp: () => tryDispatchWhenDragging(moveUp),\n      moveRight: () => tryDispatchWhenDragging(moveRight),\n      moveDown: () => tryDispatchWhenDragging(moveDown),\n      moveLeft: () => tryDispatchWhenDragging(moveLeft)\n    };\n    return lift({\n      liftActionArgs: {\n        id: draggableId,\n        clientSelection: getBorderBoxCenterPosition(el),\n        movementMode: 'SNAP'\n      },\n      cleanup: noop$2,\n      actions\n    });\n  }\n  function abortPreDrag() {\n    const shouldRelease = isActive({\n      expected: 'PRE_DRAG',\n      phase,\n      isLockActive,\n      shouldWarn: true\n    });\n    if (shouldRelease) {\n      lockAPI.release();\n    }\n  }\n  const preDrag = {\n    isActive: () => isActive({\n      expected: 'PRE_DRAG',\n      phase,\n      isLockActive,\n      shouldWarn: false\n    }),\n    shouldRespectForcePress: getShouldRespectForcePress,\n    fluidLift,\n    snapLift,\n    abort: abortPreDrag\n  };\n  return preDrag;\n}\nconst defaultSensors = [useMouseSensor, useKeyboardSensor, useTouchSensor];\nfunction useSensorMarshal({\n  contextId,\n  store,\n  registry,\n  customSensors,\n  enableDefaultSensors\n}) {\n  const useSensors = [...(enableDefaultSensors ? defaultSensors : []), ...(customSensors || [])];\n  const lockAPI = useState(() => create())[0];\n  const tryAbandonLock = useCallback(function tryAbandonLock(previous, current) {\n    if (isDragging(previous) && !isDragging(current)) {\n      lockAPI.tryAbandon();\n    }\n  }, [lockAPI]);\n  useIsomorphicLayoutEffect(function listenToStore() {\n    let previous = store.getState();\n    const unsubscribe = store.subscribe(() => {\n      const current = store.getState();\n      tryAbandonLock(previous, current);\n      previous = current;\n    });\n    return unsubscribe;\n  }, [lockAPI, store, tryAbandonLock]);\n  useIsomorphicLayoutEffect(() => {\n    return lockAPI.tryAbandon;\n  }, [lockAPI.tryAbandon]);\n  const canGetLock = useCallback(draggableId => {\n    return canStart({\n      lockAPI,\n      registry,\n      store,\n      draggableId\n    });\n  }, [lockAPI, registry, store]);\n  const tryGetLock = useCallback((draggableId, forceStop, options) => tryStart({\n    lockAPI,\n    registry,\n    contextId,\n    store,\n    draggableId,\n    forceSensorStop: forceStop || null,\n    sourceEvent: options && options.sourceEvent ? options.sourceEvent : null\n  }), [contextId, lockAPI, registry, store]);\n  const findClosestDraggableId = useCallback(event => tryGetClosestDraggableIdFromEvent(contextId, event), [contextId]);\n  const findOptionsForDraggable = useCallback(id => {\n    const entry = registry.draggable.findById(id);\n    return entry ? entry.options : null;\n  }, [registry.draggable]);\n  const tryReleaseLock = useCallback(function tryReleaseLock() {\n    if (!lockAPI.isClaimed()) {\n      return;\n    }\n    lockAPI.tryAbandon();\n    if (store.getState().phase !== 'IDLE') {\n      store.dispatch(flush());\n    }\n  }, [lockAPI, store]);\n  const isLockClaimed = useCallback(() => lockAPI.isClaimed(), [lockAPI]);\n  const api = useMemo(() => ({\n    canGetLock,\n    tryGetLock,\n    findClosestDraggableId,\n    findOptionsForDraggable,\n    tryReleaseLock,\n    isLockClaimed\n  }), [canGetLock, tryGetLock, findClosestDraggableId, findOptionsForDraggable, tryReleaseLock, isLockClaimed]);\n  useValidateSensorHooks(useSensors);\n  for (let i = 0; i < useSensors.length; i++) {\n    useSensors[i](api);\n  }\n}\n\nconst createResponders = props => ({\n  onBeforeCapture: t => {\n    const onBeforeCapureCallback = () => {\n      if (props.onBeforeCapture) {\n        props.onBeforeCapture(t);\n      }\n    };\n    flushSync(onBeforeCapureCallback);\n  },\n  onBeforeDragStart: props.onBeforeDragStart,\n  onDragStart: props.onDragStart,\n  onDragEnd: props.onDragEnd,\n  onDragUpdate: props.onDragUpdate\n});\nconst createAutoScrollerOptions = props => ({\n  ...defaultAutoScrollerOptions,\n  ...props.autoScrollerOptions,\n  durationDampening: {\n    ...defaultAutoScrollerOptions.durationDampening,\n    ...props.autoScrollerOptions\n  }\n});\nfunction getStore(lazyRef) {\n  !lazyRef.current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find store from lazy ref') : invariant() : void 0;\n  return lazyRef.current;\n}\nfunction App(props) {\n  const {\n    contextId,\n    setCallbacks,\n    sensors,\n    nonce,\n    dragHandleUsageInstructions\n  } = props;\n  const lazyStoreRef = useRef(null);\n  useStartupValidation();\n  const lastPropsRef = usePrevious(props);\n  const getResponders = useCallback(() => {\n    return createResponders(lastPropsRef.current);\n  }, [lastPropsRef]);\n  const getAutoScrollerOptions = useCallback(() => {\n    return createAutoScrollerOptions(lastPropsRef.current);\n  }, [lastPropsRef]);\n  const announce = useAnnouncer(contextId);\n  const dragHandleUsageInstructionsId = useHiddenTextElement({\n    contextId,\n    text: dragHandleUsageInstructions\n  });\n  const styleMarshal = useStyleMarshal(contextId, nonce);\n  const lazyDispatch = useCallback(action => {\n    getStore(lazyStoreRef).dispatch(action);\n  }, []);\n  const marshalCallbacks = useMemo(() => bindActionCreators({\n    publishWhileDragging,\n    updateDroppableScroll,\n    updateDroppableIsEnabled,\n    updateDroppableIsCombineEnabled,\n    collectionStarting\n  }, lazyDispatch), [lazyDispatch]);\n  const registry = useRegistry();\n  const dimensionMarshal = useMemo(() => {\n    return createDimensionMarshal(registry, marshalCallbacks);\n  }, [registry, marshalCallbacks]);\n  const autoScroller = useMemo(() => createAutoScroller({\n    scrollWindow,\n    scrollDroppable: dimensionMarshal.scrollDroppable,\n    getAutoScrollerOptions,\n    ...bindActionCreators({\n      move\n    }, lazyDispatch)\n  }), [dimensionMarshal.scrollDroppable, lazyDispatch, getAutoScrollerOptions]);\n  const focusMarshal = useFocusMarshal(contextId);\n  const store = useMemo(() => createStore({\n    announce,\n    autoScroller,\n    dimensionMarshal,\n    focusMarshal,\n    getResponders,\n    styleMarshal\n  }), [announce, autoScroller, dimensionMarshal, focusMarshal, getResponders, styleMarshal]);\n  if (process.env.NODE_ENV !== 'production') {\n    if (lazyStoreRef.current && lazyStoreRef.current !== store) {\n      process.env.NODE_ENV !== \"production\" ? warning('unexpected store change') : void 0;\n    }\n  }\n  lazyStoreRef.current = store;\n  const tryResetStore = useCallback(() => {\n    const current = getStore(lazyStoreRef);\n    const state = current.getState();\n    if (state.phase !== 'IDLE') {\n      current.dispatch(flush());\n    }\n  }, []);\n  const isDragging = useCallback(() => {\n    const state = getStore(lazyStoreRef).getState();\n    if (state.phase === 'DROP_ANIMATING') {\n      return true;\n    }\n    if (state.phase === 'IDLE') {\n      return false;\n    }\n    return state.isDragging;\n  }, []);\n  const appCallbacks = useMemo(() => ({\n    isDragging,\n    tryAbort: tryResetStore\n  }), [isDragging, tryResetStore]);\n  setCallbacks(appCallbacks);\n  const getCanLift = useCallback(id => canStartDrag(getStore(lazyStoreRef).getState(), id), []);\n  const getIsMovementAllowed = useCallback(() => isMovementAllowed(getStore(lazyStoreRef).getState()), []);\n  const appContext = useMemo(() => ({\n    marshal: dimensionMarshal,\n    focus: focusMarshal,\n    contextId,\n    canLift: getCanLift,\n    isMovementAllowed: getIsMovementAllowed,\n    dragHandleUsageInstructionsId,\n    registry\n  }), [contextId, dimensionMarshal, dragHandleUsageInstructionsId, focusMarshal, getCanLift, getIsMovementAllowed, registry]);\n  useSensorMarshal({\n    contextId,\n    store,\n    registry,\n    customSensors: sensors || null,\n    enableDefaultSensors: props.enableDefaultSensors !== false\n  });\n  useEffect(() => {\n    return tryResetStore;\n  }, [tryResetStore]);\n  return React.createElement(AppContext.Provider, {\n    value: appContext\n  }, React.createElement(Provider, {\n    context: StoreContext,\n    store: store\n  }, props.children));\n}\n\nfunction useUniqueContextId() {\n  return React.useId();\n}\n\nfunction DragDropContext(props) {\n  const contextId = useUniqueContextId();\n  const dragHandleUsageInstructions = props.dragHandleUsageInstructions || preset.dragHandleUsageInstructions;\n  return React.createElement(ErrorBoundary, null, setCallbacks => React.createElement(App, {\n    nonce: props.nonce,\n    contextId: contextId,\n    setCallbacks: setCallbacks,\n    dragHandleUsageInstructions: dragHandleUsageInstructions,\n    enableDefaultSensors: props.enableDefaultSensors,\n    sensors: props.sensors,\n    onBeforeCapture: props.onBeforeCapture,\n    onBeforeDragStart: props.onBeforeDragStart,\n    onDragStart: props.onDragStart,\n    onDragUpdate: props.onDragUpdate,\n    onDragEnd: props.onDragEnd,\n    autoScrollerOptions: props.autoScrollerOptions\n  }, props.children));\n}\n\nconst zIndexOptions = {\n  dragging: 5000,\n  dropAnimating: 4500\n};\nconst getDraggingTransition = (shouldAnimateDragMovement, dropping) => {\n  if (dropping) {\n    return transitions.drop(dropping.duration);\n  }\n  if (shouldAnimateDragMovement) {\n    return transitions.snap;\n  }\n  return transitions.fluid;\n};\nconst getDraggingOpacity = (isCombining, isDropAnimating) => {\n  if (!isCombining) {\n    return undefined;\n  }\n  return isDropAnimating ? combine.opacity.drop : combine.opacity.combining;\n};\nconst getShouldDraggingAnimate = dragging => {\n  if (dragging.forceShouldAnimate != null) {\n    return dragging.forceShouldAnimate;\n  }\n  return dragging.mode === 'SNAP';\n};\nfunction getDraggingStyle(dragging) {\n  const dimension = dragging.dimension;\n  const box = dimension.client;\n  const {\n    offset,\n    combineWith,\n    dropping\n  } = dragging;\n  const isCombining = Boolean(combineWith);\n  const shouldAnimate = getShouldDraggingAnimate(dragging);\n  const isDropAnimating = Boolean(dropping);\n  const transform = isDropAnimating ? transforms.drop(offset, isCombining) : transforms.moveTo(offset);\n  const style = {\n    position: 'fixed',\n    top: box.marginBox.top,\n    left: box.marginBox.left,\n    boxSizing: 'border-box',\n    width: box.borderBox.width,\n    height: box.borderBox.height,\n    transition: getDraggingTransition(shouldAnimate, dropping),\n    transform,\n    opacity: getDraggingOpacity(isCombining, isDropAnimating),\n    zIndex: isDropAnimating ? zIndexOptions.dropAnimating : zIndexOptions.dragging,\n    pointerEvents: 'none'\n  };\n  return style;\n}\nfunction getSecondaryStyle(secondary) {\n  return {\n    transform: transforms.moveTo(secondary.offset),\n    transition: secondary.shouldAnimateDisplacement ? undefined : 'none'\n  };\n}\nfunction getStyle$1(mapped) {\n  return mapped.type === 'DRAGGING' ? getDraggingStyle(mapped) : getSecondaryStyle(mapped);\n}\n\nfunction getDimension$1(descriptor, el, windowScroll = origin) {\n  const computedStyles = window.getComputedStyle(el);\n  const borderBox = el.getBoundingClientRect();\n  const client = calculateBox(borderBox, computedStyles);\n  const page = withScroll(client, windowScroll);\n  const placeholder = {\n    client,\n    tagName: el.tagName.toLowerCase(),\n    display: computedStyles.display\n  };\n  const displaceBy = {\n    x: client.marginBox.width,\n    y: client.marginBox.height\n  };\n  const dimension = {\n    descriptor,\n    placeholder,\n    displaceBy,\n    client,\n    page\n  };\n  return dimension;\n}\n\nfunction useDraggablePublisher(args) {\n  const uniqueId = useUniqueId('draggable');\n  const {\n    descriptor,\n    registry,\n    getDraggableRef,\n    canDragInteractiveElements,\n    shouldRespectForcePress,\n    isEnabled\n  } = args;\n  const options = useMemo(() => ({\n    canDragInteractiveElements,\n    shouldRespectForcePress,\n    isEnabled\n  }), [canDragInteractiveElements, isEnabled, shouldRespectForcePress]);\n  const getDimension = useCallback(windowScroll => {\n    const el = getDraggableRef();\n    !el ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot get dimension when no ref is set') : invariant() : void 0;\n    return getDimension$1(descriptor, el, windowScroll);\n  }, [descriptor, getDraggableRef]);\n  const entry = useMemo(() => ({\n    uniqueId,\n    descriptor,\n    options,\n    getDimension\n  }), [descriptor, getDimension, options, uniqueId]);\n  const publishedRef = useRef(entry);\n  const isFirstPublishRef = useRef(true);\n  useIsomorphicLayoutEffect(() => {\n    registry.draggable.register(publishedRef.current);\n    return () => registry.draggable.unregister(publishedRef.current);\n  }, [registry.draggable]);\n  useIsomorphicLayoutEffect(() => {\n    if (isFirstPublishRef.current) {\n      isFirstPublishRef.current = false;\n      return;\n    }\n    const last = publishedRef.current;\n    publishedRef.current = entry;\n    registry.draggable.update(entry, last);\n  }, [entry, registry.draggable]);\n}\n\nvar DroppableContext = React.createContext(null);\n\nfunction checkIsValidInnerRef(el) {\n  !(el && isHtmlElement(el)) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `\n    provided.innerRef has not been provided with a HTMLElement.\n\n    You can find a guide on using the innerRef callback functions at:\n    https://github.com/hello-pangea/dnd/blob/main/docs/guides/using-inner-ref.md\n  `) : invariant() : void 0;\n}\n\nfunction useValidation$1(props, contextId, getRef) {\n  useDevSetupWarning(() => {\n    function prefix(id) {\n      return `Draggable[id: ${id}]: `;\n    }\n    const id = props.draggableId;\n    !id ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Draggable requires a draggableId') : invariant(false) : void 0;\n    !(typeof id === 'string') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Draggable requires a [string] draggableId.\n      Provided: [type: ${typeof id}] (value: ${id})`) : invariant(false) : void 0;\n    !Number.isInteger(props.index) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `${prefix(id)} requires an integer index prop`) : invariant(false) : void 0;\n    if (props.mapped.type === 'DRAGGING') {\n      return;\n    }\n    checkIsValidInnerRef(getRef());\n    if (props.isEnabled) {\n      !findDragHandle(contextId, id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `${prefix(id)} Unable to find drag handle`) : invariant(false) : void 0;\n    }\n  });\n}\nfunction useClonePropValidation(isClone) {\n  useDev(() => {\n    const initialRef = useRef(isClone);\n    useDevSetupWarning(() => {\n      !(isClone === initialRef.current) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Draggable isClone prop value changed during component life') : invariant(false) : void 0;\n    }, [isClone]);\n  });\n}\n\nfunction useRequiredContext(Context) {\n  const result = useContext(Context);\n  !result ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find required context') : invariant() : void 0;\n  return result;\n}\n\nfunction preventHtml5Dnd(event) {\n  event.preventDefault();\n}\nconst Draggable = props => {\n  const ref = useRef(null);\n  const setRef = useCallback((el = null) => {\n    ref.current = el;\n  }, []);\n  const getRef = useCallback(() => ref.current, []);\n  const {\n    contextId,\n    dragHandleUsageInstructionsId,\n    registry\n  } = useRequiredContext(AppContext);\n  const {\n    type,\n    droppableId\n  } = useRequiredContext(DroppableContext);\n  const descriptor = useMemo(() => ({\n    id: props.draggableId,\n    index: props.index,\n    type,\n    droppableId\n  }), [props.draggableId, props.index, type, droppableId]);\n  const {\n    children,\n    draggableId,\n    isEnabled,\n    shouldRespectForcePress,\n    canDragInteractiveElements,\n    isClone,\n    mapped,\n    dropAnimationFinished: dropAnimationFinishedAction\n  } = props;\n  useValidation$1(props, contextId, getRef);\n  useClonePropValidation(isClone);\n  if (!isClone) {\n    const forPublisher = useMemo(() => ({\n      descriptor,\n      registry,\n      getDraggableRef: getRef,\n      canDragInteractiveElements,\n      shouldRespectForcePress,\n      isEnabled\n    }), [descriptor, registry, getRef, canDragInteractiveElements, shouldRespectForcePress, isEnabled]);\n    useDraggablePublisher(forPublisher);\n  }\n  const dragHandleProps = useMemo(() => isEnabled ? {\n    tabIndex: 0,\n    role: 'button',\n    'aria-describedby': dragHandleUsageInstructionsId,\n    'data-rfd-drag-handle-draggable-id': draggableId,\n    'data-rfd-drag-handle-context-id': contextId,\n    draggable: false,\n    onDragStart: preventHtml5Dnd\n  } : null, [contextId, dragHandleUsageInstructionsId, draggableId, isEnabled]);\n  const onMoveEnd = useCallback(event => {\n    if (mapped.type !== 'DRAGGING') {\n      return;\n    }\n    if (!mapped.dropping) {\n      return;\n    }\n    if (event.propertyName !== 'transform') {\n      return;\n    }\n    flushSync(dropAnimationFinishedAction);\n  }, [dropAnimationFinishedAction, mapped]);\n  const provided = useMemo(() => {\n    const style = getStyle$1(mapped);\n    const onTransitionEnd = mapped.type === 'DRAGGING' && mapped.dropping ? onMoveEnd : undefined;\n    const result = {\n      innerRef: setRef,\n      draggableProps: {\n        'data-rfd-draggable-context-id': contextId,\n        'data-rfd-draggable-id': draggableId,\n        style,\n        onTransitionEnd\n      },\n      dragHandleProps\n    };\n    return result;\n  }, [contextId, dragHandleProps, draggableId, mapped, onMoveEnd, setRef]);\n  const rubric = useMemo(() => ({\n    draggableId: descriptor.id,\n    type: descriptor.type,\n    source: {\n      index: descriptor.index,\n      droppableId: descriptor.droppableId\n    }\n  }), [descriptor.droppableId, descriptor.id, descriptor.index, descriptor.type]);\n  return React.createElement(React.Fragment, null, children(provided, mapped.snapshot, rubric));\n};\n\nvar isStrictEqual = (a, b) => a === b;\n\nvar whatIsDraggedOverFromResult = result => {\n  const {\n    combine,\n    destination\n  } = result;\n  if (destination) {\n    return destination.droppableId;\n  }\n  if (combine) {\n    return combine.droppableId;\n  }\n  return null;\n};\n\nconst getCombineWithFromResult = result => {\n  return result.combine ? result.combine.draggableId : null;\n};\nconst getCombineWithFromImpact = impact => {\n  return impact.at && impact.at.type === 'COMBINE' ? impact.at.combine.draggableId : null;\n};\nfunction getDraggableSelector() {\n  const memoizedOffset = memoizeOne((x, y) => ({\n    x,\n    y\n  }));\n  const getMemoizedSnapshot = memoizeOne((mode, isClone, draggingOver = null, combineWith = null, dropping = null) => ({\n    isDragging: true,\n    isClone,\n    isDropAnimating: Boolean(dropping),\n    dropAnimation: dropping,\n    mode,\n    draggingOver,\n    combineWith,\n    combineTargetFor: null\n  }));\n  const getMemoizedProps = memoizeOne((offset, mode, dimension, isClone, draggingOver = null, combineWith = null, forceShouldAnimate = null) => ({\n    mapped: {\n      type: 'DRAGGING',\n      dropping: null,\n      draggingOver,\n      combineWith,\n      mode,\n      offset,\n      dimension,\n      forceShouldAnimate,\n      snapshot: getMemoizedSnapshot(mode, isClone, draggingOver, combineWith, null)\n    }\n  }));\n  const selector = (state, ownProps) => {\n    if (isDragging(state)) {\n      if (state.critical.draggable.id !== ownProps.draggableId) {\n        return null;\n      }\n      const offset = state.current.client.offset;\n      const dimension = state.dimensions.draggables[ownProps.draggableId];\n      const draggingOver = whatIsDraggedOver(state.impact);\n      const combineWith = getCombineWithFromImpact(state.impact);\n      const forceShouldAnimate = state.forceShouldAnimate;\n      return getMemoizedProps(memoizedOffset(offset.x, offset.y), state.movementMode, dimension, ownProps.isClone, draggingOver, combineWith, forceShouldAnimate);\n    }\n    if (state.phase === 'DROP_ANIMATING') {\n      const completed = state.completed;\n      if (completed.result.draggableId !== ownProps.draggableId) {\n        return null;\n      }\n      const isClone = ownProps.isClone;\n      const dimension = state.dimensions.draggables[ownProps.draggableId];\n      const result = completed.result;\n      const mode = result.mode;\n      const draggingOver = whatIsDraggedOverFromResult(result);\n      const combineWith = getCombineWithFromResult(result);\n      const duration = state.dropDuration;\n      const dropping = {\n        duration,\n        curve: curves.drop,\n        moveTo: state.newHomeClientOffset,\n        opacity: combineWith ? combine.opacity.drop : null,\n        scale: combineWith ? combine.scale.drop : null\n      };\n      return {\n        mapped: {\n          type: 'DRAGGING',\n          offset: state.newHomeClientOffset,\n          dimension,\n          dropping,\n          draggingOver,\n          combineWith,\n          mode,\n          forceShouldAnimate: null,\n          snapshot: getMemoizedSnapshot(mode, isClone, draggingOver, combineWith, dropping)\n        }\n      };\n    }\n    return null;\n  };\n  return selector;\n}\nfunction getSecondarySnapshot(combineTargetFor = null) {\n  return {\n    isDragging: false,\n    isDropAnimating: false,\n    isClone: false,\n    dropAnimation: null,\n    mode: null,\n    draggingOver: null,\n    combineTargetFor,\n    combineWith: null\n  };\n}\nconst atRest = {\n  mapped: {\n    type: 'SECONDARY',\n    offset: origin,\n    combineTargetFor: null,\n    shouldAnimateDisplacement: true,\n    snapshot: getSecondarySnapshot(null)\n  }\n};\nfunction getSecondarySelector() {\n  const memoizedOffset = memoizeOne((x, y) => ({\n    x,\n    y\n  }));\n  const getMemoizedSnapshot = memoizeOne(getSecondarySnapshot);\n  const getMemoizedProps = memoizeOne((offset, combineTargetFor = null, shouldAnimateDisplacement) => ({\n    mapped: {\n      type: 'SECONDARY',\n      offset,\n      combineTargetFor,\n      shouldAnimateDisplacement,\n      snapshot: getMemoizedSnapshot(combineTargetFor)\n    }\n  }));\n  const getFallback = combineTargetFor => {\n    return combineTargetFor ? getMemoizedProps(origin, combineTargetFor, true) : null;\n  };\n  const getProps = (ownId, draggingId, impact, afterCritical) => {\n    const visualDisplacement = impact.displaced.visible[ownId];\n    const isAfterCriticalInVirtualList = Boolean(afterCritical.inVirtualList && afterCritical.effected[ownId]);\n    const combine = tryGetCombine(impact);\n    const combineTargetFor = combine && combine.draggableId === ownId ? draggingId : null;\n    if (!visualDisplacement) {\n      if (!isAfterCriticalInVirtualList) {\n        return getFallback(combineTargetFor);\n      }\n      if (impact.displaced.invisible[ownId]) {\n        return null;\n      }\n      const change = negate(afterCritical.displacedBy.point);\n      const offset = memoizedOffset(change.x, change.y);\n      return getMemoizedProps(offset, combineTargetFor, true);\n    }\n    if (isAfterCriticalInVirtualList) {\n      return getFallback(combineTargetFor);\n    }\n    const displaceBy = impact.displacedBy.point;\n    const offset = memoizedOffset(displaceBy.x, displaceBy.y);\n    return getMemoizedProps(offset, combineTargetFor, visualDisplacement.shouldAnimate);\n  };\n  const selector = (state, ownProps) => {\n    if (isDragging(state)) {\n      if (state.critical.draggable.id === ownProps.draggableId) {\n        return null;\n      }\n      return getProps(ownProps.draggableId, state.critical.draggable.id, state.impact, state.afterCritical);\n    }\n    if (state.phase === 'DROP_ANIMATING') {\n      const completed = state.completed;\n      if (completed.result.draggableId === ownProps.draggableId) {\n        return null;\n      }\n      return getProps(ownProps.draggableId, completed.result.draggableId, completed.impact, completed.afterCritical);\n    }\n    return null;\n  };\n  return selector;\n}\nconst makeMapStateToProps$1 = () => {\n  const draggingSelector = getDraggableSelector();\n  const secondarySelector = getSecondarySelector();\n  const selector = (state, ownProps) => draggingSelector(state, ownProps) || secondarySelector(state, ownProps) || atRest;\n  return selector;\n};\nconst mapDispatchToProps$1 = {\n  dropAnimationFinished: dropAnimationFinished\n};\nconst ConnectedDraggable = connect(makeMapStateToProps$1, mapDispatchToProps$1, null, {\n  context: StoreContext,\n  areStatePropsEqual: isStrictEqual\n})(Draggable);\n\nfunction PrivateDraggable(props) {\n  const droppableContext = useRequiredContext(DroppableContext);\n  const isUsingCloneFor = droppableContext.isUsingCloneFor;\n  if (isUsingCloneFor === props.draggableId && !props.isClone) {\n    return null;\n  }\n  return React.createElement(ConnectedDraggable, props);\n}\nfunction PublicDraggable(props) {\n  const isEnabled = typeof props.isDragDisabled === 'boolean' ? !props.isDragDisabled : true;\n  const canDragInteractiveElements = Boolean(props.disableInteractiveElementBlocking);\n  const shouldRespectForcePress = Boolean(props.shouldRespectForcePress);\n  return React.createElement(PrivateDraggable, _extends({}, props, {\n    isClone: false,\n    isEnabled: isEnabled,\n    canDragInteractiveElements: canDragInteractiveElements,\n    shouldRespectForcePress: shouldRespectForcePress\n  }));\n}\n\nconst isEqual = base => value => base === value;\nconst isScroll = isEqual('scroll');\nconst isAuto = isEqual('auto');\nconst isVisible = isEqual('visible');\nconst isEither = (overflow, fn) => fn(overflow.overflowX) || fn(overflow.overflowY);\nconst isBoth = (overflow, fn) => fn(overflow.overflowX) && fn(overflow.overflowY);\nconst isElementScrollable = el => {\n  const style = window.getComputedStyle(el);\n  const overflow = {\n    overflowX: style.overflowX,\n    overflowY: style.overflowY\n  };\n  return isEither(overflow, isScroll) || isEither(overflow, isAuto);\n};\nconst isBodyScrollable = () => {\n  if (process.env.NODE_ENV === 'production') {\n    return false;\n  }\n  const body = getBodyElement();\n  const html = document.documentElement;\n  !html ? process.env.NODE_ENV !== \"production\" ? invariant() : invariant() : void 0;\n  if (!isElementScrollable(body)) {\n    return false;\n  }\n  const htmlStyle = window.getComputedStyle(html);\n  const htmlOverflow = {\n    overflowX: htmlStyle.overflowX,\n    overflowY: htmlStyle.overflowY\n  };\n  if (isBoth(htmlOverflow, isVisible)) {\n    return false;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(`\n    We have detected that your <body> element might be a scroll container.\n    We have found no reliable way of detecting whether the <body> element is a scroll container.\n    Under most circumstances a <body> scroll bar will be on the <html> element (document.documentElement)\n\n    Because we cannot determine if the <body> is a scroll container, and generally it is not one,\n    we will be treating the <body> as *not* a scroll container\n\n    More information: https://github.com/hello-pangea/dnd/blob/main/docs/guides/how-we-detect-scroll-containers.md\n  `) : void 0;\n  return false;\n};\nconst getClosestScrollable = el => {\n  if (el == null) {\n    return null;\n  }\n  if (el === document.body) {\n    return isBodyScrollable() ? el : null;\n  }\n  if (el === document.documentElement) {\n    return null;\n  }\n  if (!isElementScrollable(el)) {\n    return getClosestScrollable(el.parentElement);\n  }\n  return el;\n};\n\nvar checkForNestedScrollContainers = scrollable => {\n  if (!scrollable) {\n    return;\n  }\n  const anotherScrollParent = getClosestScrollable(scrollable.parentElement);\n  if (!anotherScrollParent) {\n    return;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(`\n    Droppable: unsupported nested scroll container detected.\n    A Droppable can only have one scroll parent (which can be itself)\n    Nested scroll containers are currently not supported.\n\n    We hope to support nested scroll containers soon: https://github.com/atlassian/react-beautiful-dnd/issues/131\n  `) : void 0;\n};\n\nvar getScroll = el => ({\n  x: el.scrollLeft,\n  y: el.scrollTop\n});\n\nconst getIsFixed = el => {\n  if (!el) {\n    return false;\n  }\n  const style = window.getComputedStyle(el);\n  if (style.position === 'fixed') {\n    return true;\n  }\n  return getIsFixed(el.parentElement);\n};\nvar getEnv = start => {\n  const closestScrollable = getClosestScrollable(start);\n  const isFixedOnPage = getIsFixed(start);\n  return {\n    closestScrollable,\n    isFixedOnPage\n  };\n};\n\nvar getDroppableDimension = ({\n  descriptor,\n  isEnabled,\n  isCombineEnabled,\n  isFixedOnPage,\n  direction,\n  client,\n  page,\n  closest\n}) => {\n  const frame = (() => {\n    if (!closest) {\n      return null;\n    }\n    const {\n      scrollSize,\n      client: frameClient\n    } = closest;\n    const maxScroll = getMaxScroll({\n      scrollHeight: scrollSize.scrollHeight,\n      scrollWidth: scrollSize.scrollWidth,\n      height: frameClient.paddingBox.height,\n      width: frameClient.paddingBox.width\n    });\n    return {\n      pageMarginBox: closest.page.marginBox,\n      frameClient,\n      scrollSize,\n      shouldClipSubject: closest.shouldClipSubject,\n      scroll: {\n        initial: closest.scroll,\n        current: closest.scroll,\n        max: maxScroll,\n        diff: {\n          value: origin,\n          displacement: origin\n        }\n      }\n    };\n  })();\n  const axis = direction === 'vertical' ? vertical : horizontal;\n  const subject = getSubject({\n    page,\n    withPlaceholder: null,\n    axis,\n    frame\n  });\n  const dimension = {\n    descriptor,\n    isCombineEnabled,\n    isFixedOnPage,\n    axis,\n    isEnabled,\n    client,\n    page,\n    frame,\n    subject\n  };\n  return dimension;\n};\n\nconst getClient = (targetRef, closestScrollable) => {\n  const base = getBox(targetRef);\n  if (!closestScrollable) {\n    return base;\n  }\n  if (targetRef !== closestScrollable) {\n    return base;\n  }\n  const top = base.paddingBox.top - closestScrollable.scrollTop;\n  const left = base.paddingBox.left - closestScrollable.scrollLeft;\n  const bottom = top + closestScrollable.scrollHeight;\n  const right = left + closestScrollable.scrollWidth;\n  const paddingBox = {\n    top,\n    right,\n    bottom,\n    left\n  };\n  const borderBox = expand(paddingBox, base.border);\n  const client = createBox({\n    borderBox,\n    margin: base.margin,\n    border: base.border,\n    padding: base.padding\n  });\n  return client;\n};\nvar getDimension = ({\n  ref,\n  descriptor,\n  env,\n  windowScroll,\n  direction,\n  isDropDisabled,\n  isCombineEnabled,\n  shouldClipSubject\n}) => {\n  const closestScrollable = env.closestScrollable;\n  const client = getClient(ref, closestScrollable);\n  const page = withScroll(client, windowScroll);\n  const closest = (() => {\n    if (!closestScrollable) {\n      return null;\n    }\n    const frameClient = getBox(closestScrollable);\n    const scrollSize = {\n      scrollHeight: closestScrollable.scrollHeight,\n      scrollWidth: closestScrollable.scrollWidth\n    };\n    return {\n      client: frameClient,\n      page: withScroll(frameClient, windowScroll),\n      scroll: getScroll(closestScrollable),\n      scrollSize,\n      shouldClipSubject\n    };\n  })();\n  const dimension = getDroppableDimension({\n    descriptor,\n    isEnabled: !isDropDisabled,\n    isCombineEnabled,\n    isFixedOnPage: env.isFixedOnPage,\n    direction,\n    client,\n    page,\n    closest\n  });\n  return dimension;\n};\n\nconst immediate = {\n  passive: false\n};\nconst delayed = {\n  passive: true\n};\nvar getListenerOptions = options => options.shouldPublishImmediately ? immediate : delayed;\n\nconst getClosestScrollableFromDrag = dragging => dragging && dragging.env.closestScrollable || null;\nfunction useDroppablePublisher(args) {\n  const whileDraggingRef = useRef(null);\n  const appContext = useRequiredContext(AppContext);\n  const uniqueId = useUniqueId('droppable');\n  const {\n    registry,\n    marshal\n  } = appContext;\n  const previousRef = usePrevious(args);\n  const descriptor = useMemo(() => ({\n    id: args.droppableId,\n    type: args.type,\n    mode: args.mode\n  }), [args.droppableId, args.mode, args.type]);\n  const publishedDescriptorRef = useRef(descriptor);\n  const memoizedUpdateScroll = useMemo(() => memoizeOne((x, y) => {\n    !whileDraggingRef.current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only update scroll when dragging') : invariant() : void 0;\n    const scroll = {\n      x,\n      y\n    };\n    marshal.updateDroppableScroll(descriptor.id, scroll);\n  }), [descriptor.id, marshal]);\n  const getClosestScroll = useCallback(() => {\n    const dragging = whileDraggingRef.current;\n    if (!dragging || !dragging.env.closestScrollable) {\n      return origin;\n    }\n    return getScroll(dragging.env.closestScrollable);\n  }, []);\n  const updateScroll = useCallback(() => {\n    const scroll = getClosestScroll();\n    memoizedUpdateScroll(scroll.x, scroll.y);\n  }, [getClosestScroll, memoizedUpdateScroll]);\n  const scheduleScrollUpdate = useMemo(() => rafSchd(updateScroll), [updateScroll]);\n  const onClosestScroll = useCallback(() => {\n    const dragging = whileDraggingRef.current;\n    const closest = getClosestScrollableFromDrag(dragging);\n    !(dragging && closest) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find scroll options while scrolling') : invariant() : void 0;\n    const options = dragging.scrollOptions;\n    if (options.shouldPublishImmediately) {\n      updateScroll();\n      return;\n    }\n    scheduleScrollUpdate();\n  }, [scheduleScrollUpdate, updateScroll]);\n  const getDimensionAndWatchScroll = useCallback((windowScroll, options) => {\n    !!whileDraggingRef.current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot collect a droppable while a drag is occurring') : invariant() : void 0;\n    const previous = previousRef.current;\n    const ref = previous.getDroppableRef();\n    !ref ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot collect without a droppable ref') : invariant() : void 0;\n    const env = getEnv(ref);\n    const dragging = {\n      ref,\n      descriptor,\n      env,\n      scrollOptions: options\n    };\n    whileDraggingRef.current = dragging;\n    const dimension = getDimension({\n      ref,\n      descriptor,\n      env,\n      windowScroll,\n      direction: previous.direction,\n      isDropDisabled: previous.isDropDisabled,\n      isCombineEnabled: previous.isCombineEnabled,\n      shouldClipSubject: !previous.ignoreContainerClipping\n    });\n    const scrollable = env.closestScrollable;\n    if (scrollable) {\n      scrollable.setAttribute(scrollContainer.contextId, appContext.contextId);\n      scrollable.addEventListener('scroll', onClosestScroll, getListenerOptions(dragging.scrollOptions));\n      if (process.env.NODE_ENV !== 'production') {\n        checkForNestedScrollContainers(scrollable);\n      }\n    }\n    return dimension;\n  }, [appContext.contextId, descriptor, onClosestScroll, previousRef]);\n  const getScrollWhileDragging = useCallback(() => {\n    const dragging = whileDraggingRef.current;\n    const closest = getClosestScrollableFromDrag(dragging);\n    !(dragging && closest) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only recollect Droppable client for Droppables that have a scroll container') : invariant() : void 0;\n    return getScroll(closest);\n  }, []);\n  const dragStopped = useCallback(() => {\n    const dragging = whileDraggingRef.current;\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot stop drag when no active drag') : invariant() : void 0;\n    const closest = getClosestScrollableFromDrag(dragging);\n    whileDraggingRef.current = null;\n    if (!closest) {\n      return;\n    }\n    scheduleScrollUpdate.cancel();\n    closest.removeAttribute(scrollContainer.contextId);\n    closest.removeEventListener('scroll', onClosestScroll, getListenerOptions(dragging.scrollOptions));\n  }, [onClosestScroll, scheduleScrollUpdate]);\n  const scroll = useCallback(change => {\n    const dragging = whileDraggingRef.current;\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot scroll when there is no drag') : invariant() : void 0;\n    const closest = getClosestScrollableFromDrag(dragging);\n    !closest ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot scroll a droppable with no closest scrollable') : invariant() : void 0;\n    closest.scrollTop += change.y;\n    closest.scrollLeft += change.x;\n  }, []);\n  const callbacks = useMemo(() => {\n    return {\n      getDimensionAndWatchScroll,\n      getScrollWhileDragging,\n      dragStopped,\n      scroll\n    };\n  }, [dragStopped, getDimensionAndWatchScroll, getScrollWhileDragging, scroll]);\n  const entry = useMemo(() => ({\n    uniqueId,\n    descriptor,\n    callbacks\n  }), [callbacks, descriptor, uniqueId]);\n  useIsomorphicLayoutEffect(() => {\n    publishedDescriptorRef.current = entry.descriptor;\n    registry.droppable.register(entry);\n    return () => {\n      if (whileDraggingRef.current) {\n        process.env.NODE_ENV !== \"production\" ? warning('Unsupported: changing the droppableId or type of a Droppable during a drag') : void 0;\n        dragStopped();\n      }\n      registry.droppable.unregister(entry);\n    };\n  }, [callbacks, descriptor, dragStopped, entry, marshal, registry.droppable]);\n  useIsomorphicLayoutEffect(() => {\n    if (!whileDraggingRef.current) {\n      return;\n    }\n    marshal.updateDroppableIsEnabled(publishedDescriptorRef.current.id, !args.isDropDisabled);\n  }, [args.isDropDisabled, marshal]);\n  useIsomorphicLayoutEffect(() => {\n    if (!whileDraggingRef.current) {\n      return;\n    }\n    marshal.updateDroppableIsCombineEnabled(publishedDescriptorRef.current.id, args.isCombineEnabled);\n  }, [args.isCombineEnabled, marshal]);\n}\n\nfunction noop() {}\nconst empty = {\n  width: 0,\n  height: 0,\n  margin: noSpacing\n};\nconst getSize = ({\n  isAnimatingOpenOnMount,\n  placeholder,\n  animate\n}) => {\n  if (isAnimatingOpenOnMount) {\n    return empty;\n  }\n  if (animate === 'close') {\n    return empty;\n  }\n  return {\n    height: placeholder.client.borderBox.height,\n    width: placeholder.client.borderBox.width,\n    margin: placeholder.client.margin\n  };\n};\nconst getStyle = ({\n  isAnimatingOpenOnMount,\n  placeholder,\n  animate\n}) => {\n  const size = getSize({\n    isAnimatingOpenOnMount,\n    placeholder,\n    animate\n  });\n  return {\n    display: placeholder.display,\n    boxSizing: 'border-box',\n    width: size.width,\n    height: size.height,\n    marginTop: size.margin.top,\n    marginRight: size.margin.right,\n    marginBottom: size.margin.bottom,\n    marginLeft: size.margin.left,\n    flexShrink: '0',\n    flexGrow: '0',\n    pointerEvents: 'none',\n    transition: animate !== 'none' ? transitions.placeholder : null\n  };\n};\nconst Placeholder = props => {\n  const animateOpenTimerRef = useRef(null);\n  const tryClearAnimateOpenTimer = useCallback(() => {\n    if (!animateOpenTimerRef.current) {\n      return;\n    }\n    clearTimeout(animateOpenTimerRef.current);\n    animateOpenTimerRef.current = null;\n  }, []);\n  const {\n    animate,\n    onTransitionEnd,\n    onClose,\n    contextId\n  } = props;\n  const [isAnimatingOpenOnMount, setIsAnimatingOpenOnMount] = useState(props.animate === 'open');\n  useEffect(() => {\n    if (!isAnimatingOpenOnMount) {\n      return noop;\n    }\n    if (animate !== 'open') {\n      tryClearAnimateOpenTimer();\n      setIsAnimatingOpenOnMount(false);\n      return noop;\n    }\n    if (animateOpenTimerRef.current) {\n      return noop;\n    }\n    animateOpenTimerRef.current = setTimeout(() => {\n      animateOpenTimerRef.current = null;\n      setIsAnimatingOpenOnMount(false);\n    });\n    return tryClearAnimateOpenTimer;\n  }, [animate, isAnimatingOpenOnMount, tryClearAnimateOpenTimer]);\n  const onSizeChangeEnd = useCallback(event => {\n    if (event.propertyName !== 'height') {\n      return;\n    }\n    onTransitionEnd();\n    if (animate === 'close') {\n      onClose();\n    }\n  }, [animate, onClose, onTransitionEnd]);\n  const style = getStyle({\n    isAnimatingOpenOnMount,\n    animate: props.animate,\n    placeholder: props.placeholder\n  });\n  return React.createElement(props.placeholder.tagName, {\n    style,\n    'data-rfd-placeholder-context-id': contextId,\n    onTransitionEnd: onSizeChangeEnd,\n    ref: props.innerRef\n  });\n};\nvar Placeholder$1 = React.memo(Placeholder);\n\nfunction isBoolean(value) {\n  return typeof value === 'boolean';\n}\nfunction runChecks(args, checks) {\n  checks.forEach(check => check(args));\n}\nconst shared = [function required({\n  props\n}) {\n  !props.droppableId ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'A Droppable requires a droppableId prop') : invariant() : void 0;\n  !(typeof props.droppableId === 'string') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `A Droppable requires a [string] droppableId. Provided: [${typeof props.droppableId}]`) : invariant() : void 0;\n}, function boolean({\n  props\n}) {\n  !isBoolean(props.isDropDisabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'isDropDisabled must be a boolean') : invariant() : void 0;\n  !isBoolean(props.isCombineEnabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'isCombineEnabled must be a boolean') : invariant() : void 0;\n  !isBoolean(props.ignoreContainerClipping) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'ignoreContainerClipping must be a boolean') : invariant() : void 0;\n}, function ref({\n  getDroppableRef\n}) {\n  checkIsValidInnerRef(getDroppableRef());\n}];\nconst standard = [function placeholder({\n  props,\n  getPlaceholderRef\n}) {\n  if (!props.placeholder) {\n    return;\n  }\n  const ref = getPlaceholderRef();\n  if (ref) {\n    return;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(`\n      Droppable setup issue [droppableId: \"${props.droppableId}\"]:\n      DroppableProvided > placeholder could not be found.\n\n      Please be sure to add the {provided.placeholder} React Node as a child of your Droppable.\n      More information: https://github.com/hello-pangea/dnd/blob/main/docs/api/droppable.md\n    `) : void 0;\n}];\nconst virtual = [function hasClone({\n  props\n}) {\n  !props.renderClone ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Must provide a clone render function (renderClone) for virtual lists') : invariant() : void 0;\n}, function hasNoPlaceholder({\n  getPlaceholderRef\n}) {\n  !!getPlaceholderRef() ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected virtual list to not have a placeholder') : invariant() : void 0;\n}];\nfunction useValidation(args) {\n  useDevSetupWarning(() => {\n    runChecks(args, shared);\n    if (args.props.mode === 'standard') {\n      runChecks(args, standard);\n    }\n    if (args.props.mode === 'virtual') {\n      runChecks(args, virtual);\n    }\n  });\n}\n\nclass AnimateInOut extends React.PureComponent {\n  constructor(...args) {\n    super(...args);\n    this.state = {\n      isVisible: Boolean(this.props.on),\n      data: this.props.on,\n      animate: this.props.shouldAnimate && this.props.on ? 'open' : 'none'\n    };\n    this.onClose = () => {\n      if (this.state.animate !== 'close') {\n        return;\n      }\n      this.setState({\n        isVisible: false\n      });\n    };\n  }\n  static getDerivedStateFromProps(props, state) {\n    if (!props.shouldAnimate) {\n      return {\n        isVisible: Boolean(props.on),\n        data: props.on,\n        animate: 'none'\n      };\n    }\n    if (props.on) {\n      return {\n        isVisible: true,\n        data: props.on,\n        animate: 'open'\n      };\n    }\n    if (state.isVisible) {\n      return {\n        isVisible: true,\n        data: state.data,\n        animate: 'close'\n      };\n    }\n    return {\n      isVisible: false,\n      animate: 'close',\n      data: null\n    };\n  }\n  render() {\n    if (!this.state.isVisible) {\n      return null;\n    }\n    const provided = {\n      onClose: this.onClose,\n      data: this.state.data,\n      animate: this.state.animate\n    };\n    return this.props.children(provided);\n  }\n}\n\nconst Droppable = props => {\n  const appContext = useContext(AppContext);\n  !appContext ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find app context') : invariant() : void 0;\n  const {\n    contextId,\n    isMovementAllowed\n  } = appContext;\n  const droppableRef = useRef(null);\n  const placeholderRef = useRef(null);\n  const {\n    children,\n    droppableId,\n    type,\n    mode,\n    direction,\n    ignoreContainerClipping,\n    isDropDisabled,\n    isCombineEnabled,\n    snapshot,\n    useClone,\n    updateViewportMaxScroll,\n    getContainerForClone\n  } = props;\n  const getDroppableRef = useCallback(() => droppableRef.current, []);\n  const setDroppableRef = useCallback((value = null) => {\n    droppableRef.current = value;\n  }, []);\n  const getPlaceholderRef = useCallback(() => placeholderRef.current, []);\n  const setPlaceholderRef = useCallback((value = null) => {\n    placeholderRef.current = value;\n  }, []);\n  useValidation({\n    props,\n    getDroppableRef,\n    getPlaceholderRef\n  });\n  const onPlaceholderTransitionEnd = useCallback(() => {\n    if (isMovementAllowed()) {\n      updateViewportMaxScroll({\n        maxScroll: getMaxWindowScroll()\n      });\n    }\n  }, [isMovementAllowed, updateViewportMaxScroll]);\n  useDroppablePublisher({\n    droppableId,\n    type,\n    mode,\n    direction,\n    isDropDisabled,\n    isCombineEnabled,\n    ignoreContainerClipping,\n    getDroppableRef\n  });\n  const placeholder = useMemo(() => React.createElement(AnimateInOut, {\n    on: props.placeholder,\n    shouldAnimate: props.shouldAnimatePlaceholder\n  }, ({\n    onClose,\n    data,\n    animate\n  }) => React.createElement(Placeholder$1, {\n    placeholder: data,\n    onClose: onClose,\n    innerRef: setPlaceholderRef,\n    animate: animate,\n    contextId: contextId,\n    onTransitionEnd: onPlaceholderTransitionEnd\n  })), [contextId, onPlaceholderTransitionEnd, props.placeholder, props.shouldAnimatePlaceholder, setPlaceholderRef]);\n  const provided = useMemo(() => ({\n    innerRef: setDroppableRef,\n    placeholder,\n    droppableProps: {\n      'data-rfd-droppable-id': droppableId,\n      'data-rfd-droppable-context-id': contextId\n    }\n  }), [contextId, droppableId, placeholder, setDroppableRef]);\n  const isUsingCloneFor = useClone ? useClone.dragging.draggableId : null;\n  const droppableContext = useMemo(() => ({\n    droppableId,\n    type,\n    isUsingCloneFor\n  }), [droppableId, isUsingCloneFor, type]);\n  function getClone() {\n    if (!useClone) {\n      return null;\n    }\n    const {\n      dragging,\n      render\n    } = useClone;\n    const node = React.createElement(PrivateDraggable, {\n      draggableId: dragging.draggableId,\n      index: dragging.source.index,\n      isClone: true,\n      isEnabled: true,\n      shouldRespectForcePress: false,\n      canDragInteractiveElements: true\n    }, (draggableProvided, draggableSnapshot) => render(draggableProvided, draggableSnapshot, dragging));\n    return ReactDOM.createPortal(node, getContainerForClone());\n  }\n  return React.createElement(DroppableContext.Provider, {\n    value: droppableContext\n  }, children(provided, snapshot), getClone());\n};\n\nfunction getBody() {\n  !document.body ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'document.body is not ready') : invariant() : void 0;\n  return document.body;\n}\nconst defaultProps = {\n  mode: 'standard',\n  type: 'DEFAULT',\n  direction: 'vertical',\n  isDropDisabled: false,\n  isCombineEnabled: false,\n  ignoreContainerClipping: false,\n  renderClone: null,\n  getContainerForClone: getBody\n};\nconst attachDefaultPropsToOwnProps = ownProps => {\n  let mergedProps = {\n    ...ownProps\n  };\n  let defaultPropKey;\n  for (defaultPropKey in defaultProps) {\n    if (ownProps[defaultPropKey] === undefined) {\n      mergedProps = {\n        ...mergedProps,\n        [defaultPropKey]: defaultProps[defaultPropKey]\n      };\n    }\n  }\n  return mergedProps;\n};\nconst isMatchingType = (type, critical) => type === critical.droppable.type;\nconst getDraggable = (critical, dimensions) => dimensions.draggables[critical.draggable.id];\nconst makeMapStateToProps = () => {\n  const idleWithAnimation = {\n    placeholder: null,\n    shouldAnimatePlaceholder: true,\n    snapshot: {\n      isDraggingOver: false,\n      draggingOverWith: null,\n      draggingFromThisWith: null,\n      isUsingPlaceholder: false\n    },\n    useClone: null\n  };\n  const idleWithoutAnimation = {\n    ...idleWithAnimation,\n    shouldAnimatePlaceholder: false\n  };\n  const getDraggableRubric = memoizeOne(descriptor => ({\n    draggableId: descriptor.id,\n    type: descriptor.type,\n    source: {\n      index: descriptor.index,\n      droppableId: descriptor.droppableId\n    }\n  }));\n  const getMapProps = memoizeOne((id, isEnabled, isDraggingOverForConsumer, isDraggingOverForImpact, dragging, renderClone) => {\n    const draggableId = dragging.descriptor.id;\n    const isHome = dragging.descriptor.droppableId === id;\n    if (isHome) {\n      const useClone = renderClone ? {\n        render: renderClone,\n        dragging: getDraggableRubric(dragging.descriptor)\n      } : null;\n      const snapshot = {\n        isDraggingOver: isDraggingOverForConsumer,\n        draggingOverWith: isDraggingOverForConsumer ? draggableId : null,\n        draggingFromThisWith: draggableId,\n        isUsingPlaceholder: true\n      };\n      return {\n        placeholder: dragging.placeholder,\n        shouldAnimatePlaceholder: false,\n        snapshot,\n        useClone\n      };\n    }\n    if (!isEnabled) {\n      return idleWithoutAnimation;\n    }\n    if (!isDraggingOverForImpact) {\n      return idleWithAnimation;\n    }\n    const snapshot = {\n      isDraggingOver: isDraggingOverForConsumer,\n      draggingOverWith: draggableId,\n      draggingFromThisWith: null,\n      isUsingPlaceholder: true\n    };\n    return {\n      placeholder: dragging.placeholder,\n      shouldAnimatePlaceholder: true,\n      snapshot,\n      useClone: null\n    };\n  });\n  const selector = (state, ownProps) => {\n    const ownPropsWithDefaultProps = attachDefaultPropsToOwnProps(ownProps);\n    const id = ownPropsWithDefaultProps.droppableId;\n    const type = ownPropsWithDefaultProps.type;\n    const isEnabled = !ownPropsWithDefaultProps.isDropDisabled;\n    const renderClone = ownPropsWithDefaultProps.renderClone;\n    if (isDragging(state)) {\n      const critical = state.critical;\n      if (!isMatchingType(type, critical)) {\n        return idleWithoutAnimation;\n      }\n      const dragging = getDraggable(critical, state.dimensions);\n      const isDraggingOver = whatIsDraggedOver(state.impact) === id;\n      return getMapProps(id, isEnabled, isDraggingOver, isDraggingOver, dragging, renderClone);\n    }\n    if (state.phase === 'DROP_ANIMATING') {\n      const completed = state.completed;\n      if (!isMatchingType(type, completed.critical)) {\n        return idleWithoutAnimation;\n      }\n      const dragging = getDraggable(completed.critical, state.dimensions);\n      return getMapProps(id, isEnabled, whatIsDraggedOverFromResult(completed.result) === id, whatIsDraggedOver(completed.impact) === id, dragging, renderClone);\n    }\n    if (state.phase === 'IDLE' && state.completed && !state.shouldFlush) {\n      const completed = state.completed;\n      if (!isMatchingType(type, completed.critical)) {\n        return idleWithoutAnimation;\n      }\n      const wasOver = whatIsDraggedOver(completed.impact) === id;\n      const wasCombining = Boolean(completed.impact.at && completed.impact.at.type === 'COMBINE');\n      const isHome = completed.critical.droppable.id === id;\n      if (wasOver) {\n        return wasCombining ? idleWithAnimation : idleWithoutAnimation;\n      }\n      if (isHome) {\n        return idleWithAnimation;\n      }\n      return idleWithoutAnimation;\n    }\n    return idleWithoutAnimation;\n  };\n  return selector;\n};\nconst mapDispatchToProps = {\n  updateViewportMaxScroll: updateViewportMaxScroll\n};\nconst ConnectedDroppable = connect(makeMapStateToProps, mapDispatchToProps, (stateProps, dispatchProps, ownProps) => {\n  return {\n    ...attachDefaultPropsToOwnProps(ownProps),\n    ...stateProps,\n    ...dispatchProps\n  };\n}, {\n  context: StoreContext,\n  areStatePropsEqual: isStrictEqual\n})(Droppable);\nvar ConnectedDroppable$1 = ConnectedDroppable;\n\nexport { DragDropContext, PublicDraggable as Draggable, ConnectedDroppable$1 as Droppable, useKeyboardSensor, useMouseSensor, useTouchSensor };\n"], "names": ["log", "type", "message", "bind", "noop$2", "bindEvents", "el", "bindings", "sharedOptions", "unbindings", "map", "binding", "options", "shared", "fromBinding", "getOptions", "addEventListener", "eventName", "fn", "removeEventListener", "for<PERSON>ach", "unbind", "isProduction", "prefix$1", "RbdInvariant", "Error", "invariant", "condition", "prototype", "toString", "this", "Error<PERSON>ou<PERSON><PERSON>", "constructor", "args", "super", "callbacks", "onWindowError", "event", "getCallbacks", "isDragging", "tryAbort", "error", "preventDefault", "setCallbacks", "componentDidMount", "window", "componentDidCatch", "err", "setState", "componentWillUnmount", "render", "props", "children", "position", "index", "withLocation", "source", "destination", "isInHomeList", "droppableId", "startPosition", "endPosition", "<PERSON><PERSON><PERSON><PERSON>", "id", "combine", "draggableId", "returnedToStart", "preset", "dragHandleUsageInstructions", "onDragStart", "start", "onDragUpdate", "update", "location", "onDragEnd", "result", "reason", "origin", "x", "y", "add", "point1", "point2", "subtract", "isEqual$1", "negate", "point", "patch", "line", "value", "otherValue", "distance", "Math", "sqrt", "closest$1", "target", "points", "min", "apply", "offsetByPosition", "spacing", "top", "left", "bottom", "right", "getCorners", "clip", "frame", "shouldClipSubject", "subject", "max", "width", "height", "executeClip", "pageMarginBox", "getSubject", "page", "withPlaceholder", "axis", "scrolled", "scroll", "diff", "displacement", "scroll$1", "marginBox", "increased", "increasedBy", "end", "increase", "active", "scrollDroppable", "droppable", "newScroll", "scrollable", "scrollDiff", "initial", "scrollDisplacement", "current", "toDroppableMap", "droppables", "reduce", "previous", "descriptor", "toDraggableMap", "draggables", "toDroppableList", "Object", "values", "toDraggableList", "getDraggablesInsideDroppable", "filter", "draggable", "sort", "a", "b", "tryGetDestination", "impact", "at", "tryGetCombine", "removeDraggableFromList", "remove", "list", "item", "isHomeOf", "noDisplacedBy", "emptyGroups", "invisible", "visible", "all", "noImpact", "displaced", "displacedBy", "<PERSON><PERSON><PERSON><PERSON>", "lowerBound", "upperBound", "isPartiallyVisibleThroughFrame", "isWithinVertical", "isWithinHorizontal", "isPartiallyVisibleVertically", "isPartiallyVisibleHorizontally", "isBiggerVertically", "isBiggerHorizontally", "isTotallyVisibleThroughFrame", "vertical", "direction", "crossAxisLine", "size", "crossAxisStart", "crossAxisEnd", "crossAxisSize", "horizontal", "isVisible$1", "toBeDisplaced", "viewport", "withDroppableDisplacement", "isVisibleThroughFrameFn", "<PERSON><PERSON><PERSON><PERSON>", "getDroppableDisplaced", "isVisibleInDroppable", "isVisibleInViewport", "isTotallyVisible", "getDisplacementGroups", "afterDragging", "forceShouldAnimate", "last", "groups", "expandBy", "get<PERSON><PERSON><PERSON>", "push", "shouldAnimate", "getShouldAnimate", "goAtEnd", "insideDestination", "inHomeList", "newIndex", "length", "indexOfLastItem", "getIndexOfLastItem", "calculateReorderImpact", "match", "find", "withoutDragging", "sliceFrom", "indexOf", "slice", "didStartAfterCritical", "afterCritical", "Boolean", "effected", "moveToNextIndex", "isMovingForward", "previousImpact", "wasAt", "currentIndex", "proposedIndex", "firstIndex", "lastIndex", "fromReorder", "isCombineEnabled", "combineId", "combineWithIndex", "fromCombine", "whenCombining", "combineWith", "center", "borderBox", "displaceBy", "isDisplaced", "getCombinedItemDisplacement", "distanceFromStartToBorderBoxCenter", "box", "margin", "getCrossAxisBorderBoxCenter", "isMoving", "goAfter", "moveRelativeTo", "goBefore", "distanceFromEndToBorderBoxCenter", "whenReordering", "draggablePage", "moveInto", "contentBox", "goIntoStart", "closestAfter", "closest", "withDisplacement", "getPageBorderBoxCenterFromImpact", "withoutDisplacement", "original", "getResultWithoutDroppableDisplacement", "scrollViewport", "getDraggables$1", "ids", "getClientFromPageBorderBoxCenter", "pageBorderBoxCenter", "withoutPageScrollChange", "withViewportDisplacement", "offset", "client", "isTotallyVisibleInNewLocation", "newPageBorderBoxCenter", "onlyOnMainAxis", "changeNeeded", "isTotallyVisibleOnAxis", "moveToNextPlace", "previousPageBorderBoxCenter", "previousClientSelection", "isEnabled", "getImpact", "closestId", "withoutDraggable", "indexOfClosest", "findIndex", "d", "moveToNextCombine", "clientSelection", "scrollJumpRequest", "cautious", "maxScroll<PERSON>hange", "scrolledViewport", "scrolledDroppable", "withViewportScroll", "withDroppableScroll", "i", "tryGetVisible", "speculativelyIncrease", "getKnownActive", "rect", "getCurrentPageBorderBoxCenter", "getCurrentPageBorderBox", "getDisplacedBy", "withMaxScroll", "addPlaceholder", "placeholderSize", "requiredGrowth", "mode", "availableSpace", "needsToGrowBy", "sum", "dimension", "getRequiredGrowthForPlaceholder", "added", "oldFrameMaxScroll", "maxScroll", "newFrame", "moveCrossAxis", "isOver", "isBetweenSourceClipped", "candidates", "activeOfTarget", "isBetweenDestinationClipped", "first", "second", "array", "contains", "isWithinDroppable", "getBestCrossAxisDroppable", "sorted", "distanceToA", "distanceToB", "getClosestDraggable", "proposed", "proposedPageBorderBoxCenter", "isGoingBeforeTarget", "relativeTo", "moveToNewDroppable", "whatIsDraggedOver", "moveInDirection", "state", "isActuallyOver", "getDroppableOver$1", "dimensions", "isMainAxisMovementAllowed", "home", "critical", "isMovingOnMainAxis", "borderBoxCenter", "selection", "isMovementAllowed", "phase", "isPositionInFrame", "getDroppableOver", "pageBorderBox", "childCenter", "isContained", "isStartContained", "isEndContained", "startCenter", "candidate", "getFurthestAway", "offsetRectByPosition", "getIsDisplaced", "getDragImpact", "pageOffset", "destinationId", "pageBorderBoxWithDroppableScroll", "area", "targetRect", "targetStart", "targetEnd", "child", "childRect", "threshold", "didStartAfterCritical$1", "getCombineImpact", "atIndex", "getReorderImpact", "patchDroppableMap", "updated", "clearUnusedPlaceholder", "now", "lastDroppable", "oldMaxScroll", "removePlaceholder", "forcedClientSelection", "forcedDimensions", "forcedViewport", "forcedImpact", "newImpact", "withUpdatedPlaceholders", "cleaned", "patched", "recomputePlaceholders", "recompute", "getDraggables", "getClientBorderBoxCenter", "refreshSnap", "movementMode", "needsVisibilityCheck", "getLiftEffect", "insideHome", "rawIndex", "inVirtualList", "key", "finish", "adjustAdditionsForScrollChanges", "additions", "updatedDroppables", "windowScrollChange", "getFrame", "droppableScrollChange", "moved", "offset$1", "initialWindowScroll", "placeholder", "offsetDraggable", "isSnapping", "postDroppableChange", "isEnabledChanging", "patchDimensionMap", "removeScrollJumpRequest", "idle$2", "completed", "<PERSON><PERSON><PERSON><PERSON>", "reducer", "action", "payload", "isWindowScrollAllowed", "every", "isFixedOnPage", "onLiftImpact", "published", "withScrollChange", "modified", "existing", "updatedAdditions", "removals", "wasOverId", "wasOver", "draggingState", "isWaiting", "publishWhileDraggingInVirtual", "dropDuration", "newHomeClientOffset", "guard", "predicate", "lift$1", "publishWhileDragging", "collectionStarting", "updateDroppableScroll", "updateDroppableIsEnabled", "updateDroppableIsCombineEnabled", "move", "moveUp", "moveDown", "moveRight", "moveLeft", "flush", "completeDrop", "drop", "dropAnimationFinished", "curves", "combining", "timings", "outOfTheWay", "minDropTime", "maxDropTime", "outOfTheWayTiming", "transitions", "fluid", "snap", "duration", "timing", "moveTo", "undefined", "transforms", "isCombining", "translate", "dropTimeRange", "dropMiddleware", "getState", "dispatch", "next", "dropPending", "didDropInsideDroppable", "lastImpact", "getDropImpact", "newClientCenter", "getNewHomeClientOffset", "distance$1", "Number", "toFixed", "getDropDuration", "animateDrop", "getWindowScroll", "pageXOffset", "pageYOffset", "getScrollListener", "onWindowScroll", "scheduled", "passive", "capture", "document", "getWindowScrollBinding", "isActive", "stop", "cancel", "scrollListener", "store", "listener", "shouldStop$1", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entries", "timerId", "setTimeout", "entry", "splice", "callback", "execute", "shallow", "clearTimeout", "with<PERSON><PERSON><PERSON>", "getDragStart", "responder", "data", "announce", "getDefaultMessage", "willExpire", "wasCalled", "isExpired", "timeoutId", "getExpiringAnnounce", "responders", "getResponders", "publisher", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragging", "beforeCapture", "onBeforeCapture", "beforeStart", "onBeforeDragStart", "lastCritical", "lastLocation", "lastCombine", "hasCriticalChanged", "isDraggableEqual", "isDroppableEqual", "isCriticalEqual", "hasLocationChanged", "hasGroupingChanged", "isCombineEqual", "abort", "getPublisher", "dropAnimationFinishMiddleware", "dropAnimationFlushOnScrollMiddleware", "frameId", "cancelAnimationFrame", "once", "requestAnimationFrame", "autoScroll", "autoScroller", "shouldStop", "pendingDrop", "postActionState", "composeEnhancers", "createStore", "dimension<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "style<PERSON><PERSON><PERSON>", "marshal", "dropping", "resting", "stopPublishing", "dimensionMarshalStopper", "request", "scrollOptions", "shouldPublishImmediately", "startPublishing", "lift", "isWatching", "tryRecordFoc<PERSON>", "tryRestoreFocusRecorded", "tryShiftRecord", "focus", "getMaxScroll", "scrollHeight", "scrollWidth", "getDocumentElement", "doc", "documentElement", "getMaxWindowScroll", "clientWidth", "clientHeight", "getInitialPublish", "registry", "getViewport", "windowScroll", "getAllByType", "getDimensionAndWatchScroll", "getDimension", "shouldPublishUpdate", "getById", "createDimensionMarshal", "collection", "staging", "collect", "keys", "getScrollWhileDragging", "publish", "createPublisher", "subscriber", "exists", "change", "unsubscribe", "subscribe", "dragStopped", "canStartDrag", "scrollWindow", "scrollBy", "getScrollableDroppables", "getBestScrollableDroppable", "maybe", "getScrollableDroppableOver", "defaultAutoScrollerOptions", "startFromPercentage", "maxScrollAtPercentage", "maxPixelScroll", "ease", "percentage", "durationDampening", "stopDampeningAt", "accelerateAt", "disabled", "getPercentage", "startOfRange", "endOfRange", "range", "getValue", "distanceToEdge", "thresholds", "dragStartTime", "shouldUseTimeDampening", "getAutoScrollerOptions", "autoScrollerOptions", "startScrollingFrom", "maxScrollValueAt", "percentageFromStartScrollingFrom", "ceil", "getValueFromDistance", "proposedScroll", "stopAt", "runTime", "Date", "betweenAccelerateAtAndStopAtPercentage", "dampenValueByTime", "getScrollOnAxis", "container", "distanceToEdges", "getDistanceThresholds", "clean", "getScroll$1", "required", "limited", "isTooBigVertically", "isTooBigHorizontally", "adjustForSizeLimits", "smallestSigned", "getOverlap", "get<PERSON><PERSON><PERSON>", "targetScroll", "overlap", "canPartiallyScroll", "rawMax", "smallestChange", "canScrollWindow", "canScrollDroppable", "getWindowScrollChange", "getDroppableScrollChange", "createFluidScroller", "scheduleWindowScroll", "scheduleDroppableScroll", "tryScroll", "wasScrollNeeded", "fakeScrollCallback", "createJumpScroller", "scrollDroppableAsMuchAsItCan", "getDroppableOverlap", "whatTheDroppableCanScroll", "scrollWindowAsMuchAsItCan", "getWindowOverlap", "whatTheWindowCanScroll", "droppableRemainder", "windowRemainder", "moveByOffset", "createAutoScroller", "fluidScroller", "jumpScroll", "prefix", "dragHandle", "base", "contextId", "scrollContainer", "getStyles", "rules", "property", "rule", "styles", "selector", "join", "getStyles$1", "getSelector", "context", "attribute", "dragHandle$1", "grabCursor", "always", "dropAnimating", "transition", "userCancel", "useIsomorphicLayoutEffect", "createElement", "useLayoutEffect", "useEffect", "getHead", "head", "querySelector", "createStyleEl", "nonce", "setAttribute", "querySelectorAll", "parentNode", "Array", "from", "getWindowFromEl", "ownerDocument", "defaultView", "isHtmlElement", "HTMLElement", "findDragHandle", "possible", "handle", "getAttribute", "createRegistry", "subscribers", "notify", "cb", "findDraggableById", "findDroppableById", "register", "uniqueId", "unregister", "findById", "StoreContext", "getBodyElement", "body", "visuallyHidden", "border", "padding", "overflow", "getId", "defaults", "separator", "useUniqueId", "AppContext", "useDev", "useHook", "useDevSetupWarning", "inputs", "usePrevious", "ref", "useRef", "tab", "enter", "pageUp", "pageDown", "<PERSON><PERSON><PERSON><PERSON>", "preventStandardKeyEvents", "keyCode", "supportedEventName", "idle$1", "getCaptureBindings", "getPhase", "setPhase", "button", "clientX", "clientY", "actions", "pending", "abs", "fluidLift", "shouldBlockNextClick", "shouldRespectForcePress", "noop$1", "scrollJumpKeys", "getDraggingBindings", "idle", "interactiveTagNames", "isAnInteractiveElement", "parent", "includes", "tagName", "toLowerCase", "parentElement", "isEventInInteractiveElement", "getBorderBoxCenterPosition", "getBoundingClientRect", "supportedMatchesName", "name", "Element", "closestPonyfill", "findClosestDragHandleFromEvent", "expected", "isLockActive", "<PERSON><PERSON><PERSON><PERSON>", "canStart", "lockAPI", "isClaimed", "tryStart", "forceSensorStop", "sourceEvent", "draggable$1", "findDraggable", "canDragInteractiveElements", "lock", "claim", "getShouldRespectForcePress", "tryDispatchWhenDragging", "getAction", "release", "cleanup", "liftActionArgs", "move$1", "snapLift", "defaultSensors", "api", "phaseRef", "unbindEventsRef", "startCaptureBinding", "defaultPrevented", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "altKey", "findClosestDraggableId", "tryGetLock", "startPendingDrag", "preventForcePressBinding", "findOptionsForDraggable", "canGetLock", "listenForCapture", "bindCapturingEvents", "preDrag", "isCapturing", "touch", "touches", "longPressTimerId", "unbindTarget", "hasMoved", "force", "shouldRespect", "getHandleBindings", "unbind<PERSON><PERSON>ow", "getWindowBindings", "startDragging", "useSensorMarshal", "customSensors", "enableDefaultSensors", "useSensors", "useState", "abandon", "newLock", "tryAbandon", "create", "tryAbandonLock", "forceStop", "tryGetClosestDraggableIdFromEvent", "tryReleaseLock", "isLockClaimed", "createResponders", "t", "flushSync", "createAutoScrollerOptions", "getStore", "lazyRef", "App", "sensors", "lazyStoreRef", "lastPropsRef", "style", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "textContent", "useAnnouncer", "dragHandleUsageInstructionsId", "text", "getElementId", "display", "useHiddenTextElement", "alwaysRef", "dynamicRef", "setDynamicStyle", "setAlwaysStyle", "dynamic", "useStyleMarshal", "lazyDispatch", "marshalCallbacks", "useRegistry", "entriesRef", "recordRef", "restoreFocusFrameRef", "isMountedRef", "tryGiveFocus", "tryGiveFocusTo", "activeElement", "redirectTo", "record", "focused", "useFocusMarshal", "tryResetStore", "getCanLift", "getIsMovementAllowed", "appContext", "canLift", "Provider", "DragDropContext", "zIndexOptions", "getDraggingTransition", "shouldAnimateDragMovement", "getDraggingOpacity", "isDropAnimating", "getStyle$1", "mapped", "getShouldDraggingAnimate", "transform", "boxSizing", "opacity", "zIndex", "pointerEvents", "getDraggingStyle", "secondary", "shouldAnimateDisplacement", "useDraggablePublisher", "getDraggableRef", "computedStyles", "getComputedStyle", "getDimension$1", "publishedRef", "isFirstPublishRef", "DroppableContext", "useRequiredContext", "Context", "useContext", "preventHtml5Dnd", "isStrictEqual", "whatIsDraggedOverFromResult", "getSecondarySnapshot", "combineTargetFor", "isClone", "dropAnimation", "draggingOver", "atRest", "snapshot", "mapDispatchToProps$1", "ConnectedDraggable", "draggingSelector", "memoizedOffset", "getMemoizedSnapshot", "getMemoizedProps", "ownProps", "getCombineWithFromResult", "curve", "scale", "getDraggableSelector", "secondarySelector", "get<PERSON>allback", "getProps", "ownId", "draggingId", "visualDisplacement", "isAfterCriticalInVirtualList", "getSecondarySelector", "areStatePropsEqual", "setRef", "getRef", "dropAnimationFinishedAction", "dragHandleProps", "tabIndex", "role", "onMoveEnd", "propertyName", "provided", "onTransitionEnd", "innerRef", "draggableProps", "rubric", "PrivateDraggable", "isUsingCloneFor", "PublicDraggable", "isDragDisabled", "disableInteractiveElementBlocking", "isEqual", "isScroll", "isAuto", "is<PERSON><PERSON><PERSON>", "overflowX", "overflowY", "isElementScrollable", "getClosestScrollable", "getScroll", "scrollLeft", "scrollTop", "getIsFixed", "env", "isDropDisabled", "closestScrollable", "targetRef", "paddingBox", "getClient", "frameClient", "scrollSize", "getDroppableDimension", "immediate", "delayed", "getListenerOptions", "getClosestScrollableFromDrag", "useDroppablePublisher", "whileDraggingRef", "previousRef", "publishedDescriptorRef", "memoizedUpdateScroll", "getClosestScroll", "updateScroll", "scheduleScrollUpdate", "onClosestScroll", "getDroppableRef", "getEnv", "ignoreContainerClipping", "removeAttribute", "noop", "empty", "getStyle", "isAnimatingOpenOnMount", "animate", "getSize", "marginTop", "marginRight", "marginBottom", "marginLeft", "flexShrink", "flexGrow", "Placeholder$1", "animateOpenTimerRef", "tryClearAnimateOpenTimer", "onClose", "setIsAnimatingOpenOnMount", "onSizeChangeEnd", "AnimateInOut", "isVisible", "on", "getDerivedStateFromProps", "defaultProps", "renderClone", "getContainerForClone", "attachDefaultPropsToOwnProps", "defaultPropKey", "mergedProps", "isMatchingType", "getDraggable", "mapDispatchToProps", "updateViewportMaxScroll", "ConnectedDroppable", "idleWithAnimation", "shouldAnimatePlaceholder", "isDraggingOver", "draggingOverWith", "draggingFromThisWith", "isUsingPlaceholder", "useClone", "idleWithoutAnimation", "getDraggableRubric", "getMapProps", "isDraggingOverForConsumer", "isDraggingOverForImpact", "ownPropsWithDefaultProps", "was<PERSON><PERSON><PERSON>", "isHome", "stateProps", "dispatchProps", "droppableRef", "placeholder<PERSON><PERSON>", "setDroppableRef", "setPlaceholderRef", "onPlaceholderTransitionEnd", "droppableProps", "droppableContext", "node", "draggableProvided", "draggableSnapshot", "getClone", "ConnectedDroppable$1"], "sourceRoot": ""}