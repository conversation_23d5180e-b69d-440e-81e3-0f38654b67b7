inside_docker: false
cooperative_planning: true
autonomous_execution: false
max_actions_per_step: 5
max_turns: 20
approval_policy: "auto-conservative"
allow_for_replans: true
model_context_token_limit: 110000
allow_follow_up_input: true
playwright_port: -1
novnc_port: -1

# Model configuration for Ollama
model_client_configs:
  default:
    model_type: "ollama"
    model: "qwen3:14b-fp16"
    base_url: "http://localhost:11434"
    api_key: ""  # Ollama doesn't require API key
    temperature: 0.7
    max_tokens: 4096
